
/**
 * 微信群聊消息服务
 * 主要功能：
 * 1. 管理和处理微信群聊消息
 * 2. 提供群聊消息的CRUD操作
 * 3. 支持AI内容生成和分析
 * 4. 处理群成员打卡信息
 */


import { sleep } from "../../utils/utils";
import { INotAtMsg } from "./groupNotAtMsgTypes";

import { isValidTimeRange } from "../../utils/date_tools";
import calcKeywordsStatistics, { processHealthCheckInThread } from "../health/healthStatisticsNoAI";
import { ParseDB } from "../../database/database";
import { getLogger } from '../../utils/logger';

const logger = getLogger('notAtMsg');

// ================ 工具函数 ================
/**添加 doneTask 并过滤重复内容 */
const addDoneTask = (tasks: string[], newTask: string | string[]): Array<string> => {
    if (!tasks) {
        tasks = [];
    }
    if (Array.isArray(newTask)) {
        tasks.push(...newTask);
    } else {
        tasks.push(newTask);
    }
    return Array.from(new Set(tasks));
}
/** 将Parse对象转换为INotAtMsg接口格式 */
const getRecordJson = (record: ParseDB.Object): INotAtMsg =>
    record
        ? {
            agent: record.get('agent'),
            objectId: record.id,
            userName: record.get('userName'),
            groupName: record.get('groupName'),

            content: record.get('content'),
            type: record.get('type'),

            aiGenContent: record.get('aiGenContent'),
            aiGenContentJson: record.get('aiGenContentJson'),

            doneTasks: record.get('doneTasks') || [],

            time: record.get('time'),
            msgid: record.get('msgid'),
            source: record.get('source'),

            refItchatUser: { ...record.get('refItchatUser'), objectId: record.get('refItchatUser')?.id },
            refItchatGroup: { ...record.get('refItchatGroup'), objectId: record.get('refItchatGroup')?.id },

            is_at: record.get('is_at'),
            is_group: record.get('is_group'),
            thumb: record.get('thumb') || '',
            extra: record.get('extra') || '',
            system_name: record.get('system_name') || '',

            createdAt: record.get('createdAt'),
            updatedAt: record.get('updatedAt')
        }
        : null;

/** 查询所有AIGroupNotAtMsg相关的数据表 */
async function queryClasses() {
    try {
        const response = await ParseDB.Schema.all();
        const allSchemas = response.map(schema => schema.className);
        const filteredResults = allSchemas.filter(className => className.startsWith('AIGroupNotAtMsg_'));

        return filteredResults;
    } catch (error) {
        logger.error('Error while fetching classes:', error);
    }
    return [];
}


// 设置查询条件,方便后续的 or 操作
const setQueryKey = (query: any, keywords: string, lastDay: number) => {
    query.descending('createdAt');
    query.exists('agent');
    query.exists('refItchatGroup');
    query.include('refItchatGroup');
    query.exists('groupName');
    query.equalTo('type', 'TEXT');
    // 30天内的消息
    query.greaterThanOrEqualTo("createdAt", new Date(Date.now() - 1000 * 60 * 60 * 24 * lastDay));// 30天内的消息

    if (keywords) {
        const orRegexpStr: string[] = keywords.split(',');
        // 先 "或",后"与"
        const regexpStr = `(${orRegexpStr.join('|')}).{8,}$` // and ' 是一个特殊字符，需要进行转义      

        const regex = new RegExp(regexpStr, 'i');
        query.matches('content', regex);
    }
}
// 获取未处理的聊天内容(专用于健康打卡的),doneTasks = undefined 的也包含在内
const getRecordsHealthForAIAnalysis = async ({ tableName, keywords,
    noContainedTasks, lastDay = 30, limit = 1000 }: {
        tableName: string, keywords: string, noContainedTasks: string[], lastDay?: number, limit?: number
    }): Promise<{ total: number, records: any[] }> => {
    try {
        const query = new ParseDB.Query(tableName);
        setQueryKey(query, keywords, lastDay);
        query.notContainedIn('doneTasks', noContainedTasks);

        const query2 = new ParseDB.Query(tableName);
        setQueryKey(query2, keywords, lastDay);
        query2.doesNotExist('doneTasks');

        const compoundQuery = ParseDB.Query.or(query, query2);
        compoundQuery.descending('createdAt');

        const total = await compoundQuery.count();
        compoundQuery.limit(limit);

        return { total, records: await compoundQuery.find() };
    }
    catch (e) {
        logger.error('getRecordsHealthForAIAnalysis error=====>');
        logger.error(e);
    }
    return { total: 0, records: [] };
};
/** 定期为开启AI总结的群聊天记录进行整理 */
const cronChatAIGenContent = async (test: boolean = false) => {
    try {
        logger.info('cronChatAIGenContent====>');
        const tables = await queryClasses();
        logger.info('aiGenContent tables:', tables);
        for (const tableName of tables) {
            // 获取未处理的聊天内容(专用于健康打卡的)
            const defaultTasks = ['weightloss', 'HAS_IGNORE', 'HAS_KEYWORD',
                'NOT_WEIGHT_LOSS_CHECKIN', 'NOT_WEIGHT_LOSS_GROUP',
                'EXTRACT_CALORIES_ERR', 'EXTRACT_CALORIES_SUCESS', 'IS_CALORIES_DUPLICATE',
                'EXTRACT_WEIGHT_LOSS_ERR', 'EXTRACT_WEIGHT_LOSS_SUCESS',
                'DELETED', 'IS_DUPLICATE', 'NOT_FOOD_CALORIE_CHECKIN'];

            const keywords = process.env.HEALTH_CHECKIN_KEYWORDS + ',' + process.env.HEALTH_FOOD_CALORIE_CHECKIN_KEYWORDS;
            logger.warn(`aiGenContent: AI处理聊天内容,关键字(或) \n===>${tableName}\n[${keywords}]`);
            const result = await getRecordsHealthForAIAnalysis({
                tableName, keywords, noContainedTasks: defaultTasks,
                lastDay: 30, limit: 100
            })
            if (!result) continue;
            const { total, records } = result;

            for (const record of records) {
                const data = getRecordJson(record);
                const ret = await processHealthCheckInThread({ tableName, data, isRealTime: false, test });
                if (ret && ret.doneTasks) {
                    logger.warn(`aiGenContent: AI处理聊天内容,===>[${ret.doneTasks}] [${ret.showText}],${tableName}`);
                    record.set('doneTasks', addDoneTask(record.get('doneTasks'), ret.doneTasks));
                    await record.save();
                } else {
                    logger.warn(`processHealthCheckInThread: AI处理聊天内容返回null,===> ${tableName}`);
                    record.set('doneTasks', addDoneTask(record.get('doneTasks'), 'UNKNOWN_ERR'));
                    await record.save()
                }
                sleep(100)
            };
            logger.info(`aiGenContent: AI处理聊天内容完成,共(${records?.length}/${total})条记录;${tableName}`);
        };
    } catch (e) {
        logger.error('cronChatAIGenContent error=====>');
        logger.error(e);
    }
};
// 基础查询构建器
const createNotAtMsgQuery = (agent: string) => {
    const query = new ParseDB.Query(`AIGroupNotAtMsg_${agent}`);
    query.limit(1000000);
    return query;
};

/**
 * 获取群聊关键词统计数据的具体实现
 * @param agent - 代理地址
 * @param groupObjectId - 群组ID
 * @param keywords - 关键词列表
 * @param range - 时间范围 [开始时间, 结束时间]
 * @returns 返回按用户分组的统计数据列表
 */
const getKeywordsStatisticsGroupRange = async (
    agent: string,
    groupObjectId: string,
    keywords: string[],
    range: [number, number]
): Promise<any[]> => {
    const groupPointer = {
        __type: 'Pointer',
        className: 'AIItchatGroups',
        objectId: groupObjectId,
    };
    const query = new ParseDB.Query(`AIGroupNotAtMsg_${agent}`);
    query.descending('createdAt');
    query.equalTo('refItchatGroup', groupPointer);

    if (isValidTimeRange(range)) {
        query.greaterThanOrEqualTo("createdAt", new Date(range[0]));
        query.lessThanOrEqualTo("createdAt", new Date(range[1]));
    }

    // 查询 content 中是否包含 keywords 多个关键词中的一个
    const regex = new RegExp(keywords.join('|'), 'i');
    query.matches('content', regex);

    const total = await query.count();
    query.limit(5000); // 默认只有100条

    const results = await query.find();

    // 按照用户分组,理论上一个群可以有N多人打卡
    const groupedResults = results.reduce((acc, current) => {
        const key = current.get('refItchatUser').id;
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(current);
        return acc;
    }, {} as Record<string, ParseDB.Object[]>);

    // 为每个分组计算统计信息
    const statisticsList = await Promise.all(
        Object.entries(groupedResults).map(async ([userId, userRecords]) => {
            logger.info(`群统计按用户分组: ${userId}`);
            const recordJsonAry = userRecords.map(getRecordJson);
            return calcKeywordsStatistics(recordJsonAry, keywords);
        })
    );

    return statisticsList;
};

export {
    addDoneTask,
    createNotAtMsgQuery, cronChatAIGenContent, getKeywordsStatisticsGroupRange, getRecordJson, getRecordsHealthForAIAnalysis
};

