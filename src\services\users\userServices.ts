
import { User } from '../../types/prisma';
import { <PERSON>th<PERSON><PERSON><PERSON>, <PERSON>th<PERSON><PERSON> } from "../../types/types";
import ApiError from "../../utils/api_error";
import { getLogger } from '../../utils/logger';

import { BaseServices } from "../baseServices";
import { fetchOauthByCode, OAuthUserInfo } from "../gateSSO/oauth2Api";
import { removeRepoCollaborator } from "../github/githubServices";
import { unshareGoogleDocs } from "../google/docsApi";
import { getGuildPlatformsInfo } from "../guildPlatformServices";
import { addGuildMember, isGuildOwner, removeGuildMember } from "../memberShipServices";
import { PlatformBody } from '../platformServices';
import { resetIssueAward } from "../rewardServices";
import { getRolesInfo, removeRoleMember } from "../roleServices";
import session from '../sessionManager';
import { getWxUserByNickNameJustOne } from '../wechat/cowUserFunctions';
import {
  createUser,
  getDoctorList, getPlatformUserId, getUser, getUserByOpenid, getUserBySSO, getUserJson, getUserList,
  getUserPlatformInfo, pushPlatformInfo2UserDB, pushSSOGatePlatforminfo2UserDB,
  removePlatforminfoFromUserDB, setUserObjectId, updatePlatforminfo
} from "./userFunction";


type UserConnectAuthData = {
  code: string;
  ssoClientId: string;
  userObjectId: string;
  scope: string;
  tag: string;
};
const logger = getLogger('Users');
const TABLE_NAME = 'Users';
class UserServices extends BaseServices {
  constructor() {
    super();
    this.TABLE_NAME = TABLE_NAME
  }

  // 通过第三方平台登录后与用户关联并保存到usersPlatformUser信息表
  // curl -X 'POST'  'http://127.0.0.1:8000/api/login/oauth/access_token?grant_type=authorization_code&client_id=13e4e87be1854ea47bbe&client_secret=ebcfb8cf0358489fb2d798785b48b7a41b39dceb&code=0f1gMMkl2A0Vzf4jiIll2SSeGA3gMMk6&tag=wechat_miniprogram'  -H 'accept: application/json'  -d ''
  serviceUserConnect = async (body: any) => {
    const address = body?.params?.addr;
    const payload = body?.payload;
    const { platformName, authData, agent } = payload
    if (!payload || !platformName || !authData || !agent) {
      throw new ApiError('payload or platformName or authData or agent is missing', 60001);
    }
    const { code, ssoClientId, tag, userObjectId } = authData as UserConnectAuthData

    const userInfo: OAuthUserInfo = await fetchOauthByCode(code, platformName, ssoClientId, tag);
    logger.info('serviceUserConnect:', userInfo?.name);

    // 不等待执行完成
    pushPlatformInfo2UserDB[platformName](address || userInfo.account, userInfo);

    const loginInfo = await session.login(userInfo.account, {
      account: userInfo.account,
      userId: userInfo.sub,
      accessToken: userInfo.accessToken,
      platformName,
      platformUserId: userInfo.platformUserId,
      name: userInfo.name,
      agent: payload.agent,
      userObjectId,
    });

    let userOID = userObjectId
    if (!userOID) {
      const res = await getWxUserByNickNameJustOne(agent, userInfo.nickName);
      userOID = res?.objectId;
    }
    // 如果 userObjectId为空,函数会自动搜索
    await setUserObjectId(userInfo.objectId, userOID, userInfo.nickName);

    return {
      success: true,
      platformUserId: userInfo.userId,
      ...loginInfo,
      ...userInfo,
      userObjectId: userOID,
    };
  };
  //
  serviceUserDisConnect = async (body: any) => {
    const account = body?.params?.addr;
    const payload = body?.payload;
    const platformName = payload?.platformName;

    let result = null;

    const user = await removePlatforminfoFromUserDB(account, platformName);
    if (user && user?.id) {
      result = {
        addresses: user?.get('addresses'),
        platformUsers: await getPlatformUserId(account, platformName),
        id: user?.get('id'),
      };
    }

    return {
      success: result ?? true,
      ...result,
    };
  };

  serviceUserLeaveGuild = async (body: any) => {
    const account: EthAddress = body?.params?.addr;
    const payload = body?.payload;
    const guildId: number = payload?.guildId;

    const isOwner = await isGuildOwner(guildId, account);
    if (isOwner) {
      throw new Error('Guild owner can not leave guild');
    }

    logger.info('==>serviceUserLeaveGuild body: ', payload);
    // remove google docs permission
    const guildPlatforms = await getGuildPlatformsInfo(guildId);
    // eslint-disable-next-line no-restricted-syntax
    for (const platform of guildPlatforms) {
      if (platform?.platformName === 'GOOGLE') {
        // eslint-disable-next-line no-await-in-loop
        const email = await getPlatformUserId(account, platform?.platformName);
        unshareGoogleDocs(platform.platformProjectId, email).then(result => {
          logger.warn('==>unshareGoogleDocs result: ', result, platform.platformProjectId, email);
        });
        // eslint-disable-next-line no-await-in-loop
        const roles = await getRolesInfo(guildId);
        roles.forEach(async role => {
          logger.info('==>role: ', role);
          role?.rolePlatforms?.forEach(async rolePlatform => {
            // 如果再次加群，确保会再次仔细授权
            await resetIssueAward({
              account,
              guildId,
              roleId: role.id,
              rolePlatformId: rolePlatform.objectId,
            });
          });
        });
      } else if (platform?.platformName === 'GITHUB') {
        // eslint-disable-next-line no-await-in-loop
        const platUser = await getUserPlatformInfo(account, platform.platformName);
        // eslint-disable-next-line no-await-in-loop
        const platOwner = await getUserPlatformInfo(platform?.account, platform.platformName);

        const ownerUserData: any = platOwner?.platformUserData;
        const platUserData: any = platUser?.platformUserData;
        // eslint-disable-next-line no-await-in-loop
        await removeRepoCollaborator({
          repo: platform?.platformProjectName,
          owner: ownerUserData?.login,
          username: platUserData?.login,
          accessToken: ownerUserData?.access_token,
        });
        // eslint-disable-next-line no-await-in-loop
        const roles = await getRolesInfo(guildId);
        roles.forEach(async role => {
          logger.info('==>role: ', role);
          role?.rolePlatforms?.forEach(async rolePlatform => {
            // 如果再次加群，确保会再次仔细授权
            await resetIssueAward({
              account,
              guildId,
              roleId: role.id,
              rolePlatformId: rolePlatform.objectId,
            });
          });
        });
      }
    }
    // remove role member
    const roles = await getRolesInfo(guildId);
    roles.map(async role => removeRoleMember(role.id, account));
    // remove guild member
    const result = await removeGuildMember(guildId, account);
    return {
      success: !!result,
      account,
      guildId,
    };
  };

  serviceUserJoinGuild = async (body: any) => {
    const account = body?.params?.addr;
    const payload = body?.payload;
    const guildId = payload?.guildId;

    logger.info('==>serviceUserJoinGuild body: ', payload);
    // 添加用户到 guild member
    const result = await addGuildMember(guildId, account, false);
    return {
      success: !!result,
      account,
      guildId,
    };
    // }
  };

  // 可获取其他用户的信息
  serviceGetUser = async ({ sessionId, otherAccount, detail = false }:
    { sessionId: string, otherAccount: string, detail: boolean }) => {
    const sess = await session.validateSession(sessionId);
    if (!sess) throw new ApiError('session is error', 60001);

    return getUserJson(await getUser(otherAccount), detail);
  }

  serviceGetUserDetails = async (payload: any) => {
    const address: string = payload?.account;
    const userId = Number(payload?.id) > 0 ? Number(payload?.id) : undefined;

    return getUserJson(await getUser(userId ?? address), true);
  };
  serviceGetUserBySSO = async (ssoID: string): Promise<User> => {
    logger.info('==>serviceGetUserBySSO ssoID: ', ssoID);
    const user = await getUserJson(await getUserBySSO(ssoID), true);
    logger.info('serviceGetUserBySSO:', user?.name, user?.account, user?.ssoID);
    return user;
  };

  // 获取医生列表,医生权限由sso gate设置,doctor 在 user上扩展字段而来
  servicesGetDoctorList = async ({ sessionId, sign }: { sessionId: string; sign: string }) => {
    // 验证session
    const sess = await session.validateSession(sessionId);
    if (!sess) throw new ApiError('session is error', 60001);

    return getDoctorList();
  };
  serviceCreateUser = async ({ address, body }: { address: EthAddress; body: User }) => {
    logger.info('==>serviceCreateUser address: ', address);
    // 如果用户已经存在，那只是更改信息即可
    let result = await getUserByOpenid(body.openid);
    if (!result) result = await getUserBySSO(body.ssoID);
    if (!result) result = await getUser(address);

    if (result?.id) {
      const ssoID = result.get('ssoID');
      // 如果ssoID为空或者不相同,则更新
      if (body.ssoID && ssoID !== body.ssoID) {
        result.set('ssoID', body.ssoID);
        result = await result.save();
      }
      // 更新roles,有可能删除了某个权限
      if (body.roles.length > 0) {
        const roles = result.get('roles') || [];
        const newRoles = body.roles.filter((role: string) => !roles.includes(role));
        if (newRoles.length > 0) {
          result.set('roles', body?.roles);
        }
      }

      await updatePlatforminfo(address, body?.platformUsers);
      return getUserJson(result, true);
    }
    const inUser = body;

    // 微信登录时,获取相关信息
    const wxUser = body.platformUsers?.filter((item: any) => item.platformName === 'GATESSO');
    if (wxUser && wxUser.length > 0) {
      const wxUserP = wxUser[0].platformUserData?.properties;
      if (wxUserP?.oauth_WeChat_id) inUser.openid = wxUserP.oauth_WeChat_id;
    }

    const plats = await Promise.all(
      inUser?.platformUsers?.map((platformUser: PlatformBody) => pushSSOGatePlatforminfo2UserDB(address, platformUser)),
    );
    const refPlats = plats.map(item => ({ __type: 'Pointer', className: 'UsersPlatformUser', objectId: item.id }));
    inUser.refPlats = refPlats

    const newUser = await createUser(inUser)
    return getUserJson(newUser, true);
  };

  serviceUpdateUser = async ({ sessionId, body }: { sessionId: string; body: any }) => {
    logger.info('==>serviceUpdateUser address: ', sessionId, body.account);
    const inUser = (body?.payload || body) as User;
    const sess = await this.isAdminOrMySelf(sessionId, inUser.account);

    let user = await getUser(inUser.account);
    if (user?.id) {
      // 规范更新内容
      user.set('ssoID', inUser.ssoID);
      // user.set("account", payload.account);不允许更新
      user.set('agent', inUser.agent || user.get('agent') || EthZero); // 用于区分用户来自不同的系统和终端及渠道
      user.set('addresses', inUser.addresses);
      user.set('avatar', inUser.avatar);
      // user.set("name", payload.name); // name 不能修改,后续会用于查找用户
      user.set('nickName', inUser.nickName);
      user.set('email', inUser.email)
      // user.set("publicKey", payload.publicKey); // 不会发送更新
      user.set('cotaAddresses', inUser.cotaAddresses);
      user.set('status', inUser.status || '启用') // status: '启用' |'禁用'|  '删除' ;

      // 密码只有管理员或自己才可以更新
      if (inUser.pwd) {
        if (inUser.pwd.length < 6)
          throw new ApiError('密码长度不能小于6位', 60002);
        user.set('pwd', inUser.pwd);
      }
      if (inUser.phoneInfo)
        user.set('phoneInfo', inUser.phoneInfo);
      // ------------------------
      // 额外增加的数据
      user.set('gender', inUser.gender);
      user.set('age', inUser.age);
      user.set('era', inUser.era);
      user.set('wxid', inUser.wxid);
      user.set('country', inUser.country);
      user.set('city', inUser.city);
      user.set('region', inUser.region);
      user.set('district', inUser.district);
      user.set('intro', inUser.intro);
      user.set('skill', inUser.skill);
      user.set('other', inUser.other);
      user.set('department', inUser.department);
      user.set('professionalPhoto', inUser.professionalPhoto);
      user.set('professionalName', inUser.professionalName);
      // 只有未设置userObjectId时才更新
      if (!user.get("userObjectId")) {
        if (inUser.userObjectId) user.set('userObjectId', inUser.userObjectId);
        else if (inUser.nickName) {
          const res = await getWxUserByNickNameJustOne(sess.agent, inUser.nickName);
          if (res) user.set('userObjectId', res.objectId);
        }
      }
      // 只有管理员才可以改权限
      if (this.isAdminByRoles(sess.roles)) {
        user.set('roles', inUser.roles)
      }
      // ------------------------
      user = await user.save();
    }
    logger.info('serviceUpdateUser result', user);
    return this.makeGResponse(await getUserJson(user, true));
  };
  // 管理员获取用户列表
  serviceGetUserList = async ({ sessionId, pageNum, pageSize }: { sessionId: string; pageNum: number, pageSize: number }) => {
    const sess = await this.isSuperMan(sessionId);
    const { total, list } = await getUserList(pageNum, pageSize);
    return this.makeGResponseList(list, pageNum, pageSize, total);
  };
}

export default new UserServices();
