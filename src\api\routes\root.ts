/* eslint-disable no-unused-expressions */
import { Request, Response, Router } from "express";
import fs from "fs";
import packageJson from "../../../package.json";
import * as utilsController from "../../controllers/utilsController";
import { formatDateTime } from "../../utils/date_tools";


const router: Router = Router();

const getVersionInfo = (): { name: string, version: string, buildDateString: string } => {
  // 使用package.json中的version作为返回值
  const { name, version } = packageJson;
  // 使用package.json中的 main对应的文件日期作为编译日期
  const buildDate = fs.statSync(`package.json`).mtime;
  const buildDateString = formatDateTime(buildDate);
  return {
    name,
    version,
    buildDateString: buildDateString,
  };
}
const getProjectVersion = (req: Request, res: Response) => {
  const { name, version, buildDateString } = getVersionInfo()
  res.send(`name: ${name} </br>version: ${version} </br>buildDate: ${buildDateString}`);
}

export default (app: any) => {
  app.use("/", router);
  router.get("/", getProjectVersion);
  router.get("/api/pinata-key", utilsController.getPinataKey);
  router.post("/api/pinata-key", utilsController.revokePinataKey);


};
export { getProjectVersion, getVersionInfo };