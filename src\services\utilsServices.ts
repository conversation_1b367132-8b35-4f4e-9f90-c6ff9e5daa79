import crypto from "crypto";
import moment from "moment";

import { fetchTokens, Token } from "./token/useToken";

import ApiError from "../utils/api_error";
import { strCompNoCase } from "../utils/utils";
import { ParseDB, ParseObject } from "./services";

import { getLogger } from '../utils/logger';
import { BaseServices } from "./baseServices";
import session from "./sessionManager";

const logger = getLogger('utilsSrv');
const getTokenJson = (record: ParseObject): Token =>
  record
    ? {
      chain: record.get("chain"),
      name: record.get("name"),
      symbol: record.get("symbol"),
      decimals: record.get("decimals"),
      address: record.get("address"),
    }
    : null;
const saveToken2DB = async (chain: string, token: Token) => {
  const query = new ParseDB.Query("Tokens");
  if (query && query.get("ObjectId")) return query;

  logger.info("saveToken2DB:", chain, token);

  const db = new (ParseDB.Object.extend("Tokens"))();
  db.set("chain", chain);
  db.set("address", token.address);
  db.set("name", token.name);
  db.set("symbol", token.symbol);
  db.set("decimals", token.decimals);
  return db.save();
};
const saveTokens2DB = async (chain: string, tokens: Array<Token>) => tokens?.map(x => saveToken2DB(chain, x));

const getTokenRecord = async ({ chain, address }: { chain: string; address: string }): Promise<ParseObject> => {
  logger.info("getTokenRecord:", address, chain);

  const query = new ParseDB.Query("Tokens");
  query.matches("address", new RegExp(address, "i"));
  query.equalTo("chain", chain);

  return query.first();
};

const getTokenInfo = async ({ chain, address }: { chain: string; address: string }): Promise<Token> =>
  getTokenJson(await getTokenRecord({ chain, address }));

const serviceGetTokenInfo = async ({ address, chain }: { address: string; chain: string }) => {
  logger.info("serviceGetTokenInfo:", address, chain);

  let token = await getTokenInfo({ address, chain });
  if (token && token.name) return token;

  const tokens = await fetchTokens(chain);
  if (tokens) {
    token = tokens.find(x => strCompNoCase(x.address, address));
    if (token) {
      saveToken2DB(chain, token);
      return token;
    }
  }
  return null;
};
// TODO:每次生成一个pinata key
const getPinataKey = async () => {
  const query = new ParseDB.Query("ApiKeyInfo");
  query.equalTo("name", "pinata-key");
  const token = await query.first();
  if (token) {
    return { key: token.get("key"), jwt: token.get("jwt") };
  }
  throw new Error("PINATA not found");
};
// TODO:吊销已经使用的pinata key
const revokePinataKey = async ({ key }: { key: string }) => {
  const query = new ParseDB.Query("ApiKeyInfo");
  query.equalTo("name", "pinata-key");
  query.equalTo("key", key);
  const token = await query.first();
  if (token) {
    return { key: token.get("key") };
  }
  throw new Error("PINATA not found");
};

const feedBack = async (body: any) => {
  const { sessionId } = body;
  const { question, contact } = body.payload;

  let account = "";
  const sess = await session.validateSession(sessionId);
  if (sess) account = sess.account;

  const table = new (ParseDB.Object.extend("feedBack"))();
  table.set({
    question,
    contact,
    account,
  });
  table.save();
  return true;
};
function generateCosKey(ext: string) {
  // 配置参数
  const config = {
    // 获取腾讯云密钥，建议使用限定权限的子用户的密钥 https://console.cloud.tencent.com/cam/capi
    secretId: process.env.TENCENT_COS_SECRETID,
    secretKey: process.env.TENCENT_COS_SECRETKEY,
    // 这里填写存储桶、地域，例如：test-**********、ap-guangzhou
    bucket: process.env.TENCENT_COS_BUCKET,
    region: process.env.TENCENT_COS_REGION,
    // 允许的上传后缀
    extWhiteList: process.env.TENCENT_COS_EXT_WHITE_LIST?.split(","),
  };

  // 生成要上传的 COS 文件路径文件名
  const generateCosKeyA = (ext2: string) => {
    const ymd = moment().format("YYYYMM"); // 每月产生一个文件夹
    // const ymd = moment().format('YYYYMMDD'); //每天产生一个文件夹
    // const ymd = moment().format("YYYYMMDD_HHmmss_"); //每秒产生一个文件夹
    const r = `000000${Math.random() * 1000000}`.slice(-6);
    const cosKey = `images/${ymd}/IMG_${ymd}_${r}.${ext2}`;
    return cosKey;
  };

  // 判断异常情况
  if (!config.secretId || !config.secretKey) throw new ApiError("secretId or secretKey not ready");
  if (!config.bucket || !config.region) throw new ApiError("bucket or regions not ready");
  if (!config.extWhiteList.includes(ext)) throw new ApiError(`不接受文件类型(${ext})`);
  // 开始计算凭证
  const cosHost = `${config.bucket}.cos.${config.region}.myqcloud.com`;
  const cosKey = generateCosKeyA(ext);
  const now = Math.round(Date.now() / 1000);
  const exp = now + 900;
  const qKeyTime = `${now};${exp}`;
  const qSignAlgorithm = "sha1";
  // 生成上传要用的 policy
  // PostObject 签名保护文档 https://cloud.tencent.com/document/product/436/14690#.E7.AD.BE.E5.90.8D.E4.BF.9D.E6.8A.A4
  const policy = JSON.stringify({
    expiration: new Date(exp * 1000).toISOString(),
    conditions: [
      // {'acl': query.ACL},
      // ['starts-with', '$Content-Type', 'image/'],
      // ['starts-with', '$success_action_redirect', redirectUrl],
      // ['eq', '$x-cos-server-side-encryption', 'AES256'],
      { "q-sign-algorithm": qSignAlgorithm },
      { "q-ak": config.secretId },
      { "q-sign-time": qKeyTime },
      { bucket: config.bucket },
      { key: cosKey },
    ],
  });

  // 步骤一：生成 SignKey
  const signKey = crypto.createHmac("sha1", config.secretKey).update(qKeyTime).digest("hex");

  // 步骤二：生成 StringToSign
  const stringToSign = crypto.createHash("sha1").update(policy).digest("hex");

  // 步骤三：生成 Signature
  const qSignature = crypto.createHmac("sha1", signKey).update(stringToSign).digest("hex");

  // 返回域名、文件路径、凭证信息
  return {
    code: 0,
    data: {
      cosHost,
      cosKey,
      policy: Buffer.from(policy).toString("base64"),
      qSignAlgorithm,
      qAk: config.secretId,
      qKeyTime,
      qSignature,
      // securityToken: securityToken, // 如果 SecretId、SecretKey 是临时密钥，要返回对应的 sessionToken 的值
    },
  };
}

class UtilServices extends BaseServices {
  servicesGetTokenInfo = serviceGetTokenInfo;
}
export default new UtilServices
export const utilsServices = {
  getPinataKey,
  revokePinataKey,
  serviceGetTokenInfo,
  generateCosKey,
  feedBack
};
