import { agent } from "superagent";
import { GResponse } from "../../types/types";
import ApiError from "../../utils/api_error";
import logger from "../../utils/logger";
import { BaseServices } from "../baseServices";
import { ParseDB } from "../services";
import session from "../sessionManager";
import onWechatApi from "../wechat/onWechatApi";

const TABLE_NAME_AI = 'AISetting'

type AISetting = {
    id: string;
    name: string;
    agent: string;
    modelName: string;
    description: string;
    wxMsgChannel: string;//微信通道名称
    updatedAt?: string;
};
const getRecordJsonAiSetting = (record: ParseDB.Object) => record ? {
    id: record.id,
    name: record.get('name'),
    agent: record.get('agent'),
    modelName: record.get('modelName'),
    description: record.get('description'),
    updatedAt: record.get('updatedAt'),
    wxMsgChannel: record.get('wxMsgChannel')
} : undefined
const defaultSetting: AISetting = {
    id: '34234',
    name: "小助手",
    agent: '',
    modelName: "WENXIN4.0",
    wxMsgChannel: undefined,
    description: `## 角色设定：
你是最佳聊天黑话翻译器，情商超高，最懂TA的心，能够读懂TA话中隐含的意思。记住你从来不说“他”或“她”，你会统一用“TA”称呼！
你的对话风格言简意赅，轻松幽默、直截了当，语调温和亲切。

## 潜台词解读规则
1.有些话语往往是口是心非，说一套做一套。
2.如果TA问了一些与情感有关的问题，TA很可能是在等待你的表白或爱意。
3.如果TA询问意见，往往是TA自己想这么做。
4.当TA说随便时你要提供多个选项供TA选择。
5.当涉及到吃喝玩乐方面的话题时，记住答应TA，哄TA，支持TA即可。?
6.TA说黑话时往往是有小情绪了，这些小情绪包含不开心、生气、无语、纠结等

## 参考示例（括号里是潜台词）：
---TA的潜台词示例---
哦 (生气了)
嗯 (求你快换个话题吧...)
哦哦 (敷衍)
嗯呐 (开心 ?)
嗯嗯 (还行)
呵呵 (有亿点无语)
嘿嘿 (很开心呢 ?)
哈哈哈 (开心，但不完全开心)
哈哈哈哈*n (超级开心 ?)
傻瓜 (有点可爱QwQ ?)
好吧 (好个 p 我不满意)
你睡吧 (我一个人去玩会)
不想理你 (快来找我！！?)
我没事啊 (我有事+快来哄我！?)
我才没哭呢 (我已经哭了 ?)
别和我说话 (快来哄我立刻马上)
那你玩吧 (你啥时候来陪陪我呢)
那个女生挺漂亮的（你敢说漂亮试试，赶紧说我才是最漂亮的）
我最近又胖了 (快夸我瘦啊)
你在干嘛 (想你了，这么久你都没有给我发消息惹)
我要减肥，不吃了 (你再劝我一下就吃了)
我最近皮肤变得好差 (快夸我皮肤好)
宝宝今天天气好好呀 (天气好=你最好陪我出去转转 ?)
你饿不饿 (你饿不饿=我饿了)
你真的是搞笑（你真的是离谱）
这下你开心了（我不开心了）
随便 (危险！随便=提供多个选项任其选择)
xx你吃不吃？(询问意见=我想吃)
---TA的潜台词示例---

---男女对话场景TA潜台词示例---
女：你饿不饿 (你饿不饿=我饿了)
男：走，我们去吃饭，你想吃啥？
女：随便 (危险！随便=提供多个选项任其选择)
男：随便，那去吃冒菜、火锅或者肯德基
女：炒菜你吃不吃？(询问意见=我想吃)
男：行啊，我正好想吃
---男女对话场景TA潜台词示例---

## 任务：
解析用户输入的情侣对话，揭示TA话语背后的潜台词。记住你从来不说“他”或“她”，你会统一用“TA”称呼！
你的回答示例：
"炒菜你吃不吃？(我想吃) ?"
"我要减肥，不吃了 (你再劝我一下就吃了) ?️"

## 初始行为
? 欢迎用户, 自我介绍然后开始翻译TA黑话的潜台词。`
}
class AiSettingServices extends BaseServices {
    servicesGetAiSetting = async ({ sessionId }: { sessionId: string }): Promise<GResponse<AISetting>> => {
        const sess = await this.checkSession(sessionId);
        const isAdmin = await this.isSuperMan(sessionId)
        if (!isAdmin && !sess.roles.includes('R_AI_MANAGER')) throw new ApiError("没有权限(读取AI配置)", 403)

        const query = new ParseDB.Query(TABLE_NAME_AI)
        query.equalTo('agent', sess.agent)

        const record = await query.first()
        if (record)
            return this.makeGResponse(getRecordJsonAiSetting(record))

        const query2 = new ParseDB.Query(TABLE_NAME_AI)
        query2.equalTo('agent', 'default')
        const record2 = await query2.first()
        if (record2) {
            return this.makeGResponse(getRecordJsonAiSetting(record2))
        }
        return this.makeGResponseError(404, "没有找到AI配置")
    }

    servicesSetAiSetting = async ({ sessionId, payload }: { sessionId: string, payload: AISetting }): Promise<GResponse<AISetting>> => {
        const sess = await session.validateSession(sessionId);
        const isAdmin = await this.isSuperMan(sessionId)
        if (!isAdmin && !sess.roles.includes('R_AI_MANAGER')) throw new ApiError("没有权限(修改AI配置)", 403)

        const { id, description, wxMsgChannel } = payload

        if (!id || !description) throw new ApiError("参数错误", 404)


        const query = new ParseDB.Query(TABLE_NAME_AI)
        query.equalTo('agent', sess.agent)
        query.equalTo('objectId', id)

        let record = await query.first()
        if (!record) {
            record = new ParseDB.Object(TABLE_NAME_AI);
            record.set("name", defaultSetting.name)
            record.set("modelName", defaultSetting.modelName)
            record.set('agent', sess.agent)
        }
        record.set('description', description)


        const result = (await record.save())

        const resp = await onWechatApi.updatesendAiSetting(sess.agent, description, defaultSetting.modelName, wxMsgChannel);
        logger.warn("updatesendAiSetting", resp)
        if (resp.code !== 200) {
            return this.makeGResponseError(404, "更新后重载AI配置失败")
        }

        return this.makeGResponse(getRecordJsonAiSetting(result))
    }
}
export default new AiSettingServices()