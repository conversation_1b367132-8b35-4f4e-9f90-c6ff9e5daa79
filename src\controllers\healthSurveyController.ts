import { Request, Response } from "express";
import HealthSurveyServices from "../services/health/healthSurveyServices";
import { HealthSurvey } from "../types/healthSurvey";
import { controlFunc } from './controller';

/**
 * 提交用户健康调研报告
 */
export const postUserHealthSurvey = async (req: Request, res: Response) =>
    controlFunc(req, res, HealthSurveyServices.createOrUpdateSurvey, {
        sessionId: req.headers['session-id'],
        survey: req.body as HealthSurvey
    });

/**
 * 获取用户健康调研报告
 */
export const getUserHealthSurvey = async (req: Request, res: Response) =>
    controlFunc(req, res, HealthSurveyServices.getSurvey, {
        sessionId: req.headers['session-id'],
        account: req.params.account
    });



/**
 * 获取所有健康调研报告列表
 */
export const getHealthSurveyList = async (req: Request, res: Response) =>
    controlFunc(req, res, HealthSurveyServices.getAllSurveys, {
        sessionId: req.headers['session-id'],
        pageNum: req.params.pageNum,
        pageSize: req.params.pageSize,
        search: req.query
    });