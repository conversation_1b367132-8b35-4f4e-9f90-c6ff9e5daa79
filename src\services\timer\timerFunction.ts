import { getLogger } from 'log4js';
import { Eth<PERSON>ddress, Eth<PERSON>ero } from '../../types/types';

import { formatDateTime, isLastDayOfMonth, isValidDate } from '../../utils/date_tools';
import { sleep } from '../../utils/utils';
import { getDailyNews } from '../dailyNewsServices';
import { ParseDB } from '../services';
import itchatGroup from '../wechat/cowGroupClass';

import { addOrUpdateWxUser, getItchatUserInfo } from '../wechat/cowUserFunctions';
import onWechatApi from '../wechat/onWechatApi';
import followUpServices from '../users/followUpServices';
import { TABLE_TIMER, TIMER_CLIENT } from './types';
import { addOrUpdateItchatGroup, getGroupsBySearch } from '../wechat/cowGroupFunction';

const { <PERSON>ronJob } = require('cron');

const logger = getLogger('timerSrv');


// # 文件格式说明
// #  ——分钟（0 - 59）
// # |  ——小时（0 - 23）
// # | |  ——日（1 - 31）
// # | | |  ——月（0 - 11）
// # | | | |  ——星期（0 - 6）
// # | | | | |
// # * * * * *
const genCronString = (item: TIMER_CLIENT) => {
  const { time, period, event, msg, dayOfMonth, daysOfWeek } = item;

  const daysAry: { [key: string]: number } = { 日: 0, 一: 1, 二: 2, 三: 3, 四: 4, 五: 5, 六: 6 };

  const days = daysOfWeek?.map((v: string) => daysAry[v]);

  if (!time || !period || !event) return null;
  try {
    const cur = new Date();
    const cYear = cur.getFullYear();
    const cMonth = cur.getMonth() + 1;
    const cDay = cur.getDate();
    // logger.info(`cur:${cYear}-${cMonth}-${cDay}`);

    let t = new Date(time);
    if (!t.getFullYear()) t = new Date(`cur:${cYear}-${cMonth}-${cDay} ${time}`);
    if (!isValidDate(t)) {
      logger.error(`time:${time} is not valid`);
      return null;
    }

    const year = t.getFullYear();
    let month = t.getMonth() + 1;
    const day = t.getDate();
    const hour = t.getHours();
    const minute = t.getMinutes();
    const second = t.getSeconds();

    month -= 1; // js 的 cron 是从0-11,不能加 1
    switch (item.period) {
      case '今天':
        // 12 12 12 9 12 ?  // 执行后需要删除任务
        return `${second} ${minute} ${hour} ${cDay} ${month} *`;
      case '明天':
        return `${second} ${minute} ${hour} ${cDay + 1} ${month} *`;
      case '每月每天':
      case '本月每天':
      case '每天':
        // 12 55 23 * * ? *
        return `${second} ${minute} ${hour} * * *`;
      case '指定':
        // 12 55 23 23 * ? *
        return `${second} ${minute} ${hour} ${day} * *`;
      case '每周':
        // 12 12 12 * * 3,5 周三周五12:12:12
        return `${second} ${minute} ${hour} * * ${days.join(',')}`;
      case '每月':
        return `${second} ${minute} ${hour} ${day} ${month} *`;
      case '每年':
        return `${second} ${minute} ${hour} ${day} ${month} *`;
      default:
        return null;
    }
  } catch (e) {
    logger.error(e);
  }
  return null;
};
const patientNoticeTimer: Map<string, any> = new Map();
const getMsgContent = async (item: TIMER_CLIENT) => {
  let { event } = item;
  let wxMsg = item.msg;
  if (['月图片'].some(keyword => event.includes(keyword))) {
    wxMsg = item.url;
  } else if (['图片', '视频', '文件'].some(keyword => event.includes(keyword))) {
    wxMsg = item.url;
  } else if (item.event === '日报' || item.event === '早报') wxMsg = await getDailyNews();
  else if (item.event === '随访') wxMsg = (await followUpServices.getCurFollowUpInfo(item))?.content;
  else if (item.event === '消息列表') {
    const result = await followUpServices.getCurMsgList(item);
    event = result?.type;
    wxMsg = result?.content;
  }
  return { wxMsg, event };
};
const sendWxMsgToUser = async (toUserId: string, toNickName: string, item: TIMER_CLIENT) => {
  let result;
  if (item.event === '消息列表') {
    logger.info(`sendWxMsgToUser:${toUserId} ${toNickName} 消息列表 extData.data :${item.extData?.data} ${item.objectId}`);
    result = await sendMultiTypeMsgList(item)
    return result;
  }
  const { wxMsg, event } = await getMsgContent(item);
  const wxMsgChannel = item.agent; // 使用 agent 作为 wxMsgChannel

  if (['月图片'].some(keyword => event.includes(keyword))) {
    const data = item?.extData?.data?.find((v: any) => {
      const date1 = new Date(v.date);
      const data2 = new Date();
      if (item.period === '本月每天')
        return date1.getDate() === data2.getDate() && date1.getMonth() === data2.getMonth();
      // 每月每天,比较是否本月的第N天就行
      return date1.getDate() === data2.getDate();
    });

    if (data?.imgUrl) {
      result = await onWechatApi.sendUrl(event, wxMsg, item.msg, toUserId, toNickName, wxMsgChannel);
      if (result && result.error === 0 && event === '公开月图片' && process.env.IKONW_COMMON_IMG_PROMPT) {
        result = await onWechatApi.sendText(
          process.env.IKONW_COMMON_IMG_PROMPT?.replace('{groupObjectId}', item.recvObjectId),
          toUserId,
          toNickName,
          wxMsgChannel,
        );
      }
    } else {
      logger.error(`未找到"${event}"定时器待发送图片信息`, item);
    }
  } else if (['图片', '视频', '文件'].some(keyword => event.includes(keyword))) {
    result = await onWechatApi.sendUrl(event, wxMsg, item.msg, toUserId, toNickName, wxMsgChannel);
  } else if (['微信链接'].some(keyword => event.includes(keyword))) {
    result = await onWechatApi.sendWxMsg('wx_link', item.msg, toUserId, toNickName, wxMsgChannel);
  } else if (['扩展'].some(keyword => event.includes(keyword))) {
    result = await onWechatApi.sendPluginsProc(wxMsg, toUserId, toNickName, wxMsgChannel);
  } else {
    result = await onWechatApi.sendText(wxMsg, toUserId, toNickName, wxMsgChannel);
  }
  if (result) logger.warn(`微信通知消息发送成功! ${toNickName},${item.event},${wxMsg}`);
  else logger.error(`微信通知消息发送失败!`, item.period, event, wxMsg, toNickName, toUserId);

  return result;
};

// 只发送给一个用户,否则会照成多次重复发送;发送消息列表,消息列表包含各种类型的消息
const sendMultiTypeMsgList = async (item: TIMER_CLIENT) => {
  if (!item.event.includes('消息列表')) { logger.error(`非消息列表定时器`, item); return; }
  if (!item.extData?.data) { logger.error(`消息列表为空,无法发送!`, item); return; }

  const mapEvent = {
    TEXT: '文本',
    IMAGE: '图片',
    WX_LINK: '微信链接',
    VIDEO_URL: '视频',
    IMAGE_URL: '图片',
  };
  // 构建新的 TIMER_CLIENT,回调 sendWxMsgMain
  for (const msgItem of item.extData?.data) {
    const timerItem: TIMER_CLIENT = {
      ...item,
      msg: msgItem.content,
      event: mapEvent[msgItem.type],
      url: msgItem.content,
      extData: undefined,// 防止回调被识别为消息列表
    };
    await sendWxMsgMain(timerItem);
    await sleep(1000 + Math.random() * 2000);
  }
};
const saveSentMsgLog = async (toUserId: string, toNickName: string, item: TIMER_CLIENT, result: any) => {
  try {
    const { objectId, ownerAccount, period, event } = item;
    const record = new ParseDB.Object(`${TABLE_TIMER}_log`);
    const refTimerRecord = {
      __type: 'Pointer',
      className: TABLE_TIMER,
      objectId,
    };
    record.set('refTimerRecord', refTimerRecord);
    record.set('ownerAccount', ownerAccount);
    record.set('period', period);
    record.set('event', event);
    record.set('toUserId', toUserId);
    record.set('toNickName', toNickName);
    record.set('result', !!result);
    record.set('actual_to_userid', result?.actual_to_userid);
    record.set('actual_to_nickname', result?.actual_to_nickname);
    await record.save();
  } catch (e) {
    logger.error('saveSentMsgLog error:', e, item);
  }
};

/**
 * 发送微信消息定时器执行后,更新发送结果记录
 * @param toUserId 
 * @param toNickName 
 * @param item 
 * @param result 
 */
const updateSendWxMsgResultStats = async (toUserId: string, toNickName: string, item: TIMER_CLIENT, result: any) => {
  const { objectId, ownerAccount, period } = item;

  saveSentMsgLog(toUserId, toNickName, item, result);

  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('ownerAccount', ownerAccount);
  query.equalTo('objectId', objectId);
  query.first().then((record: any) => {
    if (!record) {
      logger.warn('sendWxMsg TABLE_TIMER 查找记录失败...', ownerAccount);
      return;
    }
    // 无论是否调用成功,任务到期都要删除,除非是"每*"
    if (period.startsWith('每')) {
      logger.info('循环定时器执行成功,不删除', period);
      record.increment('runCount');
      if (!result || !result.to_user_id) record.increment('errCount');
      else {
        if (
          (result.actual_user_id && result.actual_user_id !== toUserId) ||
          (result.actual_user_nickname && result.actual_user_nickname !== toNickName)
        ) {
          logger.warn(
            'sendWxMsg 实际发送给用户:',
            result.actual_to_userid,
            '实际发送给用户昵称:',
            result.actual_to_nickname,
          );
          if (item.recvIsGroup) {
            addOrUpdateItchatGroup(item.agent, {
              objectId: item.recvObjectId,
              groupUserName: result.actual_user_id,
              groupNickName: result.actual_user_nickname,
              agent: item.agent,
              groupHeadImgUrl: '',
              owner_ObjectId: '',
              owner_NickName: '',
              owner_Account: EthZero,
            }).then((res: any) => {
              logger.info('addOrUpdateGroup:', res);
              // record.set('recvObjectId', res);
              // record.save();
            });
          } else {
            addOrUpdateWxUser(item.agent, {
              UserName: result.actual_user_id,
              NickName: result.actual_user_nickname,
              HeadImgUrl: '',
            })
              .then((res: any) => {
                logger.info('addOrUpdateWxUser:', res);
                // record.set('recvObjectId', res);
                // record.save();
              });
          }
        }
      }
      record.save();
      if (item.event === '随访') followUpServices.setNextFollowUpInfo(item);
      return;
    }
    if (period.startsWith('本月每天')) {
      logger.info('本月每天任务...', record.id);

      if (isLastDayOfMonth()) {
        logger.info('本月每天任务已到月底,删除...', record.id);
        record.destroy();
        patientNoticeTimer.delete(objectId);
      }

      return;
    }
    logger.info('DB删除job...', record.id);
    record.destroy();
    patientNoticeTimer.delete(objectId);
  });
};
const randomDelay = (): Promise<void> => {
  const delay = Math.floor(Math.random() * 3000) + 1500; // Random between 1500-4000ms
  return new Promise(resolve => setTimeout(resolve, delay));
};
const sendWxMsgToGroups = async (item: TIMER_CLIENT) => {
  let result: any;
  // 确保按顺序执行,每次发送之间都有延时
  for (const [index, objectId] of item.recvObjectIds.entries()) {
    const g = await itchatGroup.getWxGroupInfoByObjectId(objectId);
    if (g) {
      // 实际发送消息
      result = await sendWxMsgToUser(g.groupUserName, g.groupNickName, {
        ...item,
        recvObjectId: objectId,
        recvObjectIds: undefined,
      });
    } else {
      logger.error('群组信息不存在:', item);
    }
    // 发送后更新数据库,多个任务更新一次
    updateSendWxMsgResultStats(g?.groupUserName, g?.groupNickName, item, result);
    // 随机延时1-3秒
    await randomDelay();
    logger.info(`发送消息到微信群 ${index}/${item.recvObjectIds.length}`);
  }
};


const sendWxMsgToGroupsByRegexp = async (item: TIMER_CLIENT) => {
  try {
    if (typeof item.extData?.regexpStr !== 'string') return false;
    const { total, results } = await getGroupsBySearch(EthZero, 1, 300, {
      groupNameRegex: item.extData?.regexpStr,
      owner_account: item.ownerAccount,
      startTime: item.extData?.startTime,
      endTime: item.extData?.endTime,
    });
    // 确保按顺序执行,每次发送之间都有延时
    for (const [index, g] of results?.entries()) {
      const result = await sendWxMsgToUser(g.groupUserName, g.groupNickName, item);
      // 发送后更新数据库,多个任务更新一次
      updateSendWxMsgResultStats(g?.groupUserName, g?.groupNickName, item, result);
      await randomDelay();
      logger.info(`通过[正则表达式]发送消息到微信群 ${index}/${results.length}`);
    }
    logger.warn(`通过[正则表达式]发送消息到多个微信群任务已完成:共${total}个`);
    return true
  } catch (e) {
    logger.error('sendWxMsgToGroupsByRegexp error:', e, item);
  }
  return false
}
const sendWxMsgMain = async (item: TIMER_CLIENT) => {
  try {
    let toUserId: string;
    let toNickName: string;
    // 需要排在第一位,否则被后续接受处理
    //正则匹配,即使未来创建的群也去适配
    if (item.recvIsGroup && item.extData?.isFutureGroup && item.extData.regexpStr?.length > 0) {
      await sendWxMsgToGroupsByRegexp(item);
      logger.warn(`根据正则表达式发送消息到多个微信群任务已完成 ${item.extData.regexpStr}`);
      return;
    }
    // 处理多个群
    if (item.recvIsGroup && item.recvObjectIds?.length > 0) {
      await sendWxMsgToGroups(item);
      logger.warn(`发送消息到多个微信群任务已完成:共${item.recvObjectIds.length}个`);
      return; // 已经发送完成
    }

    if (item.recvIsGroup && item.recvObjectId) {
      // 发给一个群.
      const g = await itchatGroup.getWxGroupInfoByObjectId(item.recvObjectId);
      toUserId = g?.groupUserName;
      toNickName = g?.groupNickName;
    } else {
      // 发给一个用户
      const u = await getItchatUserInfo(item.agent, {
        objectId: item.recvObjectId,
        account: item.recvAccount,
        NickName: item.recvName,
        UserName: '',
        HeadImgUrl: '',
      });
      toUserId = u?.UserName;
      toNickName = u?.NickName;
    }
    if (!toUserId) throw new Error(`sendWxMsg toUserId is null, item:${JSON.stringify(item)}`);
    // 实际发送消息
    const result = await sendWxMsgToUser(toUserId, toNickName, item);
    // 发送后更新数据库
    updateSendWxMsgResultStats(toUserId, toNickName, item, result);
  } catch (e) {
    logger.error('sendWxMsg error====>', e, item);
  }
};
// CronJob参数说明
// cronExpression: Cron表达式,指定任务运行时间
// onTick: 任务回调函数,在每次触发时被调用
// onComplete: 任务完成回调,可选,可以触发任务结束后的回调
// start: 是否立即启动任务,默认false
// timezone: 时区设置,默认为机器当前时区
// context: this绑定的上下文,在onTick回调中可通过this访问
// runOnInit: 是否在构造时立即触发一次,默认false
// unrefTimeout: 是否设置定时器为非引用计时,默认false
const startJobWxNotice = (item: TIMER_CLIENT) => {
  // 定时任务
  const { objectId, event } = item;
  const cronTime = genCronString(item);
  if (!cronTime) {
    logger.error('定时任务生成失败', item, event);
    return;
  }

  const job = patientNoticeTimer.get(objectId);
  if (job) {
    logger.warn('定时任务已存在,首先停止:', objectId);
    job.stop();
    patientNoticeTimer.delete(objectId);
  }
  try {
    const jobWxNotice = new CronJob(
      cronTime,
      () => sendWxMsgMain(item),
      () => logger.info(`job onComplete! ${item}`),
      true,
      'Asia/Taipei',
    );

    logger.info('启动定时器 startJobWxNotice:', item.period, item.event, "创建", item.time, `已经运行:${item.runCount}次,发送错误:${item.errCount}次`);

    patientNoticeTimer.set(objectId, jobWxNotice);
  } catch (e) {
    logger.error('startJobWxNotice error:', e, item);
  }
};
const stopJobWxNotice = async (item: any) => {
  const { account, event, objectId } = item;
  const jobWxNotice = patientNoticeTimer.get(objectId);
  if (jobWxNotice) {
    // map中的定时任务=》停止+删除
    jobWxNotice.stop();
    patientNoticeTimer.delete(objectId);

    // 定时任务对应的随访信息列表=》删除
    if (event === '随访') {
      const ret = await followUpServices.delFollowUpOfTimer({ account, objectId });
      if (!ret) {
        logger.error('delFollowUpOfTimer 随访删除定时提醒失败', objectId);
        return null;
      }
    }
    return true;
  }
  logger.error('stopJobWxNotice 找不到job:', objectId);
  return false;
};

const setTimerRecord = async (record: ParseDB.Object, data: TIMER_CLIENT): Promise<TIMER_CLIENT> => {
  let t = new Date(data.time);
  if (!t.getFullYear()) {
    const cur = new Date();
    const year = cur.getFullYear();
    const month = cur.getMonth() + 1;
    const day = cur.getDate();
    t = new Date(`${year}-${month}-${day} ${data.time}`);
  }
  if (!isValidDate(t)) {
    throw new Error(`时间格式错误:${data.time}`);
  }

  record.set('ownerAccount', data.ownerAccount);
  record.set('recvObjectId', data.recvObjectId);
  record.set('recvObjectIds', data.recvObjectIds);
  record.set('recvIsGroup', data.recvIsGroup);
  record.set('recvAccount', data.recvAccount);
  record.set('recvName', data.recvName);
  record.set('period', data.period);
  record.set('time', formatDateTime(t));

  if (data.period.startsWith('每周')) {
    // 两种方式，按顺序首先取第一种，第一种包括第二种情形
    // daysOfWeek内是中文，如 ['一',’二‘]
    record.set('daysOfWeek', data.daysOfWeek || []);
    // record.set('dayOfWeek', data.dayOfWeek);
  }

  record.set('event', data.event);
  record.set('msg', data.msg);
  record.set('status', 'init');
  record.set('lastMessage', data.msg);
  record.set('runCount', 0);
  record.set('errCount', 0);
  record.set('url', data.url);
  record.set('ext', data.ext);
  record.set('extData', data.extData);
  const result: any = await record.save();

  const item = result.toJSON();

  if (data.event === '随访') {
    const ret = await followUpServices.setFollowUpOfTimer(item);
    if (!ret) {
      logger.error('addGroupTimer 随访设置定时提醒失败', item);
      return null;
    }
  } else if (data.event === '消息列表') {
    const ret = await followUpServices.addMstListTimer(item);
    if (!ret) {
      logger.error('addGroupTimer 消息列表设置定时提醒失败', item);
      return null;
    }
  }
  return item;
};
const getMonthImageTimers = (account: EthAddress, data: any) => {
  if (!data || !account) return null;
  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('ownerAccount', account);
  query.equalTo('event', '月图片');
  query.containedIn('period', ['每月每天', '本月每天']);
  query.descending('updatedAt');
  return query.find();
};
const searchTimers = (
  account: EthAddress,
  data: {
    recvObjectId: string;
    recvIsGroup: boolean;
  },
): Promise<ParseDB.Object[]> => {
  if (!data || !data.recvObjectId) return null;

  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('ownerAccount', account);
  if (data.recvIsGroup !== undefined) query.equalTo('recvIsGroup', data.recvIsGroup);
  if (data.recvObjectId) query.equalTo('recvObjectId', data.recvObjectId);
  query.descending('updatedAt');
  return query.find();
};
//-----------------------------------------
// friend timer
const addFriendTimer = async ({ account, data }: { account: EthAddress; data: TIMER_CLIENT }) => {
  if (!data || (!data.recvAccount && !data.recvName && !data.id) || (!data.recvObjectId && !data.recvIsGroup))
    throw new Error('添加定时器缺少参数');

  const timers = await searchTimers(account, data);

  if (timers.length >= 3) throw new Error('一个好友最多只能添加3个定时器');

  const record = new ParseDB.Object(TABLE_TIMER);
  const item = await setTimerRecord(record, data);

  startJobWxNotice(item);

  return item;
};

const getFriendTimer = (account: EthAddress, friendAccount: string, id: string) => {
  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('account', account);
  query.equalTo('friendAccount', friendAccount);
  query.equalTo('objectId', id);
  return query.first();
};
const getFriendTimers = async ({
  account,
  data,
}: {
  account: EthAddress;
  data: {
    account: EthAddress;
    name: string;
    recvObjectId: string;
    recvIsGroup: boolean;
  };
}) => {
  if (!account && !data.name) throw new Error('account,name,userId is required');

  const timers = await searchTimers(account, data);

  return timers.map((item: any) => item.toJSON());
};
const deleteFriendTimer = async ({ account, data }: { account: EthAddress; data: TIMER_CLIENT }) => {
  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('ownerAccount', account);
  query.equalTo('objectId', data.objectId);

  const record = await query.first();
  if (record) {
    await stopJobWxNotice(record.toJSON());
    return record.destroy();
  }
  logger.error('找不到需要删除的timer:', account, data);
  return null;
};
//-----------------------------------------
// group timer
async function getMyGroupTimers(account: EthAddress, data: any): Promise<any> {
  if (data.recvObjectId) {
    const timers = await searchTimers(account, data);
    return timers.map(item => item.toJSON());
  }
  if (data.type === '月图片') {
    const timers = await getMonthImageTimers(account, data);
    return timers.map(item => item.toJSON());
  }
  return [];
}
const addGroupTimer = async ({ account, data }: { account: EthAddress; data: TIMER_CLIENT }) => {
  if (!data || (!data.recvAccount && !data.recvName && !data.id) || !data.recvObjectId || !data.recvIsGroup)
    throw new Error('添加定时器缺少参数');

  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('ownerAccount', account);
  query.equalTo('recvIsGroup', data.recvIsGroup);
  query.equalTo('recvObjectId', data.recvObjectId);

  if ((await query.count()) >= 3) throw new Error('一个群最多只能设置3个定时提醒');

  const record = new ParseDB.Object(TABLE_TIMER);

  const item = await setTimerRecord(record, { ...data, ownerAccount: account });

  startJobWxNotice(item);

  return item;
};

const getRecordJson = (record: any): TIMER_CLIENT =>
  record
    ? {
      recvAccount: record.get('account'),
      ownerAccount: record.get('ownerAccount'),
      id: record.id,
      agent: record.get('agent'),
      recvName: record.get('name'),

      time: record.get('time'),

      recvObjectId: record.get('recvObjectId'),
      recvIsGroup: record.get('recvIsGroup'),

      period: record.get('period'),
      event: record.get('event'),
      dayOfMonth: record.get('dayOfMonth'),
      dayOfWeek: record.get('dayOfWeek'),
      daysOfWeek: record.get('daysOfWeek'),

      extData: record.get('extData'),
      ext: record.get('ext'),
    }
    : undefined;

const addGroupTimerPublic = async ({
  agent,
  account,
  data,
}: {
  agent: EthAddress;
  account: EthAddress;
  data: TIMER_CLIENT;
}) => {
  const { objectId, id, recvName: name, recvObjectId, recvIsGroup } = data;
  if (!data || (account && name && id) || !recvObjectId || !recvIsGroup) throw new Error('添加定时器缺少参数');

  const isMember = await itchatGroup.isGroupMember(agent, data.recvObjectId, account);
  if (!isMember) {
    const err = new Error();
    err.name = 'USER_NOT_GROUP_MEMBER';
    err.message = '用户不是群成员';
    throw err;
  }

  let newTimer: TIMER_CLIENT = data;

  const query = new ParseDB.Query(TABLE_TIMER);

  query.equalTo('recvIsGroup', data.recvIsGroup);
  query.equalTo('recvObjectId', data.recvObjectId);

  let filterExtData = data.extData ? data.extData : { data: [] };
  let record = await query.first();
  if (!record) {
    record = new ParseDB.Object(TABLE_TIMER);
    newTimer = { ...data, ownerAccount: data.ownerAccount || account };
  } else {
    const old = record.get('extData') || { data: [] };
    const oldData = old.data.filter((item: any) => item.ownerAccount !== account);
    // 只能获取我自己的extData
    const newData1 = filterExtData.data.filter((item: any) => item.ownerAccount && item.ownerAccount === account);
    filterExtData = record.get('extData') || { data: [] };
    // 当我的extData与原有的extData日期相同时,我的将被舍弃.
    const newData = newData1.filter((item: any) => oldData.filter((oldItem: any) => oldItem.date === item.date));

    const arr = oldData.concat(newData);
    // 去重
    const unique = arr.filter(
      (obj: any, index: number) => arr.findIndex((item: any) => item.date === obj.date) === index,
    );
    filterExtData.data = unique;

    newTimer = getRecordJson(record);
    if (newTimer.ownerAccount === account) {
      // 拥有者才可以更新时间
      newTimer.time = data.time;
    }
    newTimer.extData = filterExtData; // 其他内容不可以修改
  }

  const item = await setTimerRecord(record, newTimer);

  startJobWxNotice(item);

  return item;
};

// 添加发送给多个群的定时器
const addGroupsTimer = async ({ account, data }: { account: EthAddress; data: TIMER_CLIENT }) => {
  const { objectId, id, recvName: name, recvObjectIds, time, period, extData } = data;
  if (!data || !time || !period || (!account && !name && !id) || !recvObjectIds || recvObjectIds.length < 1) throw new Error('添加定时器缺少参数');

  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('ownerAccount', account);
  query.equalTo('recvIsGroup', true);
  // query.equalTo('recvObjectIds', data.recvObjectId);
  const count = await query.count();
  logger.info(`用户 ${account} 已经设置了 ${count}个定时器`);
  if (count >= 20) throw new Error(`一个账号最多只能设置20个定时器,您已有:${count}个`);
  if (recvObjectIds?.length > 100) throw new Error('一个定时器最多只能设置100个信息接收群');

  let record;
  if (objectId) {
    const query2 = new ParseDB.Query(TABLE_TIMER);
    record = await query2.get(objectId);
    if (!record) throw new Error('该定时提醒不存在');
  } else record = new ParseDB.Object(TABLE_TIMER);

  const item = await setTimerRecord(record, { ...data, ownerAccount: account });

  startJobWxNotice(item);

  return item;
};

const delGroupTimer = async ({ account, data }: { account: EthAddress; data: TIMER_CLIENT }) => {
  const { recvAccount: groupAccount, objectId } = data;

  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('ownerAccount', account);
  if (groupAccount) {
    query.equalTo('recvIsGroup', data.recvIsGroup);
    query.equalTo('account', groupAccount);
  }
  query.equalTo('objectId', objectId);
  const record = await query.first();
  if (record) {
    await stopJobWxNotice(record.toJSON());
    return record.destroy();
  }
  logger.error('找不到需要删除的微信群定时器:', account, data);
  return null;
};

// 从表中读取定时任务,使用cron执行:https://www.bejson.com/othertools/cron/
const processNoticTimers = async () => {
  logger.info('processNoticTimers.......');
  const query = new ParseDB.Query(TABLE_TIMER);
  const results = await query.find();
  if (results.length > 0) {
    results.forEach(async (record: any) => {
      startJobWxNotice(record.toJSON());
    });
  }
};

// 删除我的定时器 ********
const delMyTimer = async ({ account, timerObjectId }: { account: EthAddress; timerObjectId: string }) => {
  const query = new ParseDB.Query(TABLE_TIMER);
  query.equalTo('ownerAccount', account);
  query.equalTo('objectId', timerObjectId);
  const record = await query.first();
  if (record) {
    await stopJobWxNotice(record.toJSON());
    return record.destroy();
  }
  logger.error('找不到需要删除的timer:', account, timerObjectId);
  throw new Error('找不到需要删除的定时器');
};




export {
  addFriendTimer, addGroupsTimer, addGroupTimer, addGroupTimerPublic,
  deleteFriendTimer, delGroupTimer, delMyTimer,
  getFriendTimer, getFriendTimers, getMyGroupTimers,
  processNoticTimers,
  startJobWxNotice
};

