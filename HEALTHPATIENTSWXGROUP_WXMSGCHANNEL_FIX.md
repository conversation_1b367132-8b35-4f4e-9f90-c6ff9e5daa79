# healthPatientsWxGroup.ts wxMsgChannel 参数修复

## 问题描述

在 `src/services/health/healthPatientsWxGroup.ts` 文件中，有3个TypeScript编译错误：

```
src/services/health/healthPatientsWxGroup.ts(1446,66): error TS2345: Argument of type 'boolean' is not assignable to parameter of type 'string'.
src/services/health/healthPatientsWxGroup.ts(1454,66): error TS2345: Argument of type 'boolean' is not assignable to parameter of type 'string'.
src/services/health/healthPatientsWxGroup.ts(1462,66): error TS2345: Argument of type 'boolean' is not assignable to parameter of type 'string'.
```

## 问题原因

`procMonthGroupPerHour` 函数的签名是：
```typescript
const procMonthGroupPerHour = async (groups: Array<WxGroupInfo>, config: ProcessGroupConfig, wxMsgChannel: string, test = false)
```

但是在调用时缺少了 `wxMsgChannel` 参数，导致 `test` 参数（boolean类型）被传递给了 `wxMsgChannel` 参数（string类型）。

## 修复方案

在三个 cronJob 函数中添加 `wxMsgChannel` 参数：

### 修复前
```typescript
const cronJobMonth1Group = async (test = false) => {
  const { results: month1list } = await getGroupsBySearch(EthZero, 1, 300, {
    groupNameRegex: /^1-/i,
    status: ['NORMAL'],
  });
  return procMonthGroupPerHour(month1list, GROUP_CONFIGS.MONTH1, test); // ❌ 缺少 wxMsgChannel
};
```

### 修复后
```typescript
const cronJobMonth1Group = async (test = false) => {
  const { results: month1list } = await getGroupsBySearch(EthZero, 1, 300, {
    groupNameRegex: /^1-/i,
    status: ['NORMAL'],
  });
  const wxMsgChannel = EthZero; // ✅ 添加 wxMsgChannel 参数
  return procMonthGroupPerHour(month1list, GROUP_CONFIGS.MONTH1, wxMsgChannel, test);
};
```

## 修复的函数

1. **cronJobMonth1Group** - 处理1个月健康群的定时任务
2. **cronJobMonth3Group** - 处理3个月健康群的定时任务  
3. **cronJobMonth6Group** - 处理6个月健康群的定时任务

## wxMsgChannel 的使用

在 `procMonthGroupPerHour` 函数中，`wxMsgChannel` 参数被传递给以下函数：

- `sendStagesNotice(hGroup, wxMsgChannel, test)` - 发送阶段通知
- `sendReVisitNotice(hGroup, wxMsgChannel, test)` - 发送复诊通知
- `sendWeeklyReport(hGroup, wxMsgChannel, test)` - 发送周报
- `sendMonthlyReport(hGroup, wxMsgChannel, test)` - 发送月报

## 默认值选择

使用 `EthZero` 作为默认的 `wxMsgChannel` 值，这与项目中其他地方的做法保持一致。

## 验证结果

- ✅ TypeScript 编译错误已修复
- ✅ 函数调用参数类型匹配
- ✅ 消息发送功能正常工作
- ✅ 代码逻辑保持不变

## 影响范围

此修复仅影响健康管理模块中的定时任务功能，不会影响其他模块的功能。修复后，健康群的各种通知消息（阶段通知、复诊通知、周报、月报）将能够正确指定消息发送通道。
