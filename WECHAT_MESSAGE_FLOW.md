# 微信消息发送流程图

## 概述

本文档描述了项目中微信消息发送的完整流程，包括 `wxMsgChannel` 参数的传递和使用。

## 流程图

```mermaid
graph TD
    A[API请求] --> B{获取wxMsgChannel}
    B --> |从body获取| C[body.wxMsgChannel]
    B --> |从params获取| D[params.wxMsgChannel]
    B --> |从headers获取| E[headers.wxMsgChannel]
    B --> |从agent获取| F[agent作为wxMsgChannel]
    
    C --> G[IKnowOnWechatApi]
    D --> G
    E --> G
    F --> G
    
    G --> H{消息类型}
    
    H --> |文本消息| I[sendText]
    H --> |通用消息| J[sendWxMsg]
    H --> |群组消息| K[sendMsgToGroups]
    H --> |插件消息| L[sendPluginsProc]
    H --> |URL消息| M[sendUrl]
    H --> |邀请用户| N[inviteUserToGroup]
    H --> |AI设置| O[updatesendAiSetting]
    
    I --> P[fetcherOnWechat]
    J --> P
    K --> P
    L --> P
    M --> P
    N --> P
    O --> P
    
    P --> Q{选择消息通道}
    Q --> |通道1| R[API_ON_WECHAT_SERVER_1]
    Q --> |通道2| S[API_ON_WECHAT_SERVER_2]
    Q --> |通道N| T[API_ON_WECHAT_SERVER_N]
    
    R --> U[微信服务器1]
    S --> V[微信服务器2]
    T --> W[微信服务器N]
    
    U --> X[发送结果]
    V --> X
    W --> X
    
    X --> Y[返回响应]
    
    style A fill:#e1f5fe
    style G fill:#f3e5f5
    style P fill:#fff3e0
    style X fill:#e8f5e8
    style Y fill:#ffebee
```

## 核心组件

### 1. IKnowOnWechatApi 类

**文件位置**: `src/services/wechat/onWechatApi.ts`

**主要功能**: 提供微信消息发送的核心API接口

**所有消息发送函数**:
- `sendText(message, toUser, toUserNickName, wxMsgChannel)` - 发送文本消息
- `sendWxMsg(type, message, toUser, toUserNickName, wxMsgChannel)` - 发送通用消息
- `sendMsgToGroups(msg, groupWxids, test, wxMsgChannel)` - 发送群组消息
- `sendPluginsProc(message, toUser, toUserNickName, wxMsgChannel, ext)` - 发送插件消息
- `sendUrl(type, url, filename, toUser, toUserNick, wxMsgChannel)` - 发送URL消息
- `inviteUserToGroup({userWxid, groupWxid, wxMsgChannel})` - 邀请用户进群
- `updatesendAiSetting(agent, descption, modelName, wxMsgChannel)` - 更新AI设置

### 2. fetcherOnWechat 函数

**文件位置**: `src/utils/fetcher.ts`

**功能**: 根据 `wxMsgChannel` 选择对应的微信服务器进行消息发送

**环境变量配置**:
```bash
API_ON_WECHAT_SERVER_1=http://server1.example.com
API_ON_WECHAT_SERVER_2=http://server2.example.com
API_ON_WECHAT_SERVER_N=http://serverN.example.com
```

## wxMsgChannel 参数获取方式

### 1. 从请求体获取
```typescript
const wxMsgChannel = req.body.wxMsgChannel;
```

### 2. 从请求参数获取
```typescript
const wxMsgChannel = req.params.wxMsgChannel;
```

### 3. 从请求头获取
```typescript
const wxMsgChannel = req.headers['wx-msg-channel'];
```

### 4. 从agent获取（默认方式）
```typescript
const wxMsgChannel = agent; // 使用agent作为默认通道
```

## 调用示例

### 1. 控制器层调用
```typescript
// 在控制器中获取wxMsgChannel
const postWxNotice = async (req: Request, res: Response): Promise<void> => {
  const wxMsgChannel = req.body.wxMsgChannel || req.headers['iknow-agent'];
  await controlFunc(req, res, itchatServices.postWxNotice, { 
    toUser: req.params.toUser, 
    body: req.body,
    wxMsgChannel 
  });
};
```

### 2. 服务层调用
```typescript
// 在服务中使用wxMsgChannel
const sendNotification = async (agent: EthAddress, message: string, toUser: string) => {
  const wxMsgChannel = agent; // 使用agent作为通道
  const result = await onWechatApi.sendText(message, toUser, "", wxMsgChannel);
  return result;
};
```

### 3. 定时器调用
```typescript
// 在定时器中使用item.agent作为wxMsgChannel
const sendWxMsgToUser = async (toUserId: string, toNickName: string, item: TIMER_CLIENT) => {
  const wxMsgChannel = item.agent; // 使用agent作为wxMsgChannel
  const result = await onWechatApi.sendText(message, toUserId, toNickName, wxMsgChannel);
  return result;
};
```

## 已修改的文件

### 1. 核心API文件
- ✅ `src/services/wechat/onWechatApi.ts` - 所有函数已包含wxMsgChannel参数

### 2. 调用方文件
- ✅ `src/services/timer/timerFunction.ts` - 已修复所有调用
- ✅ `src/services/wechat/cowUserClass.ts` - 已正确使用wxMsgChannel
- ✅ `src/services/notifications/baseWxNotificationServices.ts` - 已正确使用agent
- ✅ `src/services/health/healthPatientsWxGroup.ts` - 已正确使用agent
- ✅ `src/services/chatlist/chatlistServices.ts` - 已正确使用wxMsgChannel

## 消息通道配置

### 环境变量格式
```bash
# 微信消息发送服务器配置
API_ON_WECHAT_SERVER_1=http://*************:8080
API_ON_WECHAT_SERVER_2=http://*************:8080
API_ON_WECHAT_SERVER_DEFAULT=http://*************:8080

# API认证配置
API_ON_WECHAT_NAME=your_api_user
```

### 通道选择逻辑
1. 根据 `wxMsgChannel` 值查找对应的环境变量
2. 格式: `API_ON_WECHAT_SERVER_{wxMsgChannel}`
3. 如果找不到对应配置，使用默认通道
4. 如果没有配置任何通道，禁用消息发送功能

## 错误处理

### 1. 通道配置缺失
```typescript
if (!wechatHost) {
  logger.error(`[fetcherOnWechat] 微信消息发送功能关闭[未设置发送服务器]:API_ON_WECHAT_SERVER_${wxMsgChannel}`);
  return Response.json({ code: 400, text: '禁用微信消息发送' });
}
```

### 2. 消息发送失败
```typescript
if (result.status !== 200) {
  logger.error('sendText failed: ', result.status, result.statusText, result);
  return result.statusText;
}
```

## 注意事项

1. **参数顺序**: `wxMsgChannel` 参数必须作为最后一个参数传递
2. **默认值**: 当无法获取 `wxMsgChannel` 时，使用 `agent` 作为默认值
3. **环境配置**: 确保为每个通道配置正确的服务器地址
4. **错误日志**: 所有消息发送操作都会记录详细的日志信息
5. **性能考虑**: 群组消息发送会有随机延时，避免频繁请求

## 测试建议

1. **单元测试**: 测试每个消息发送函数的参数传递
2. **集成测试**: 测试完整的消息发送流程
3. **通道测试**: 验证不同通道的消息发送功能
4. **错误测试**: 测试各种错误情况的处理
5. **性能测试**: 测试大量消息发送的性能表现

通过这个流程图和文档，AI大模型可以清楚地理解微信消息发送的完整架构和实现细节。
