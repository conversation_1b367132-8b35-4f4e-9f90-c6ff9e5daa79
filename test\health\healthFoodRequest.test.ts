import { logger } from "ethers";
import mocha from "mocha";
import initParseServer from "../../src/database/database";
import { addDoneTask, getRecord<PERSON><PERSON>, getRecordsHealthForAIAnalysis } from "../../src/services/chatlist/groupNotAtMsgFunctions";
import healthCaloriesServices from "../../src/services/health/calories/healthCaloriesServices";
import { foodCalorieCheckInFunGPT, isFoodCalorieCheckIn } from "../../src/services/health/healthStatisticsNoAI";
import wxGroupServices from '../../src/services/wechat/cowGroupClass';
import { sleep } from "../../src/utils/utils";
import { extractDate } from "../../src/utils/date_tools";
import { makeWxUserRef } from "../../src/services/wechat/cowUserFunctions";
import { makeWxGroupRef } from "../../src/services/wechat/cowGroupFunction";

const { describe } = mocha;
const { it } = mocha;
const { assert } = require("chai");

const { expect } = require("chai");

const agent1 = "******************************************"; // 徐子军
const agent2 = "0x3E6e1844b7dcf7861428458Fab49b4063D1a761A"; // Akun
const agent = "0xDb0a2D08A7217E7fb0eAD08FDF4d54d66e4365Ef"; // 221服务器
const account = "0x3E6e1844b7dcf7861428458Fab49b4063D1a761A";

initParseServer({
    appId: "nftweb2",
    serverURL: "http://**************:1339/parse",
    javascriptKey: "123456aaa",
    masterKey: "123456aaa",
});
// 导入食物列表,只执行一次即可
describe('记录NotAt打卡内容', () => {
    before(async function () {
        this.timeout(30000); // 增加时时间到30秒
    });
    // it(`统计需要处理的卡路里打卡记录数 ${agent}`, async () => {
    //     const tableName = `AIGroupNotAtMsg_${agent}`;
    //     //const caloriesTasks = ['weightloss', 'HAS_IGNORE', 'HAS_KEYWORD', 'EXTRACT_WEIGHT_LOSS_SUCESS', , 'NOT_WEIGHT_LOSS_GROUP', 'DELETED']
    //     const caloriesTasks = ['weightloss', 'HAS_IGNORE', 'HAS_KEYWORD', 'EXTRACT_WEIGHT_LOSS_SUCESS',
    //         'NOT_WEIGHT_LOSS_CHECKIN', 'NOT_WEIGHT_LOSS_CHECKIN', 'NOT_WEIGHT_LOSS_GROUP',
    //         'EXTRACT_CALORIES_ERR', 'EXTRACT_CALORIES_SUCESS', 'IS_CALORIES_DUPLICATE',
    //         'EXTRACT_WEIGHT_LOSS_ERR', 'EXTRACT_WEIGHT_LOSS_SUCESS', 'NOT_FOOD_CALORIE_CHECKIN',
    //         'DELETED', 'IS_DUPLICATE'];
    //     const records = await getRecordsHealthForAIAnalysis({
    //         tableName,
    //         keywords: process.env.HEALTH_FOOD_CALORIE_CHECKIN_KEYWORDS,
    //         noContainedTasks: caloriesTasks,
    //         limit: 20 // 太大了会导致后续的ParseDB.query查询出现错误
    //     });
    //     logger.info('----------------------------->')
    //     logger.info('共获得需要处理记录数:', records?.length);
    //     for (const [index, record] of records?.entries()) {
    //         const data = getRecordJson(record);
    //         const isCaloriesCheckIn = await isFoodCalorieCheckIn(data.content, data.doneTasks, data.agent);
    //         if (isCaloriesCheckIn) {
    //             logger.warn(`卡路里打卡----->${index}:${data.content}`);
    //         } else {
    //             logger.info(`其他数据 ${index}:`, data.content);
    //         }
    //         sleep(100)
    //     };

    // }).timeout(130000); // 30 秒

    it(`识别卡路里打卡,并修正doneTasks插入记录 ${agent}`, async () => {
        const tableName = `AIGroupNotAtMsg_${agent}`;
        //const caloriesTasks = ['weightloss', 'HAS_IGNORE', 'HAS_KEYWORD', 'EXTRACT_WEIGHT_LOSS_SUCESS', , 'NOT_WEIGHT_LOSS_GROUP', 'DELETED']
        const caloriesTasks = ['weightloss', 'HAS_IGNORE', 'HAS_KEYWORD', 'EXTRACT_WEIGHT_LOSS_SUCESS',
            'NOT_WEIGHT_LOSS_CHECKIN', 'NOT_WEIGHT_LOSS_CHECKIN', 'NOT_WEIGHT_LOSS_GROUP',
            'EXTRACT_CALORIES_ERR', 'EXTRACT_CALORIES_SUCESS', 'IS_CALORIES_DUPLICATE',
            'EXTRACT_WEIGHT_LOSS_ERR', 'EXTRACT_WEIGHT_LOSS_SUCESS',
            'DELETED', 'IS_DUPLICATE', 'NOT_FOOD_CALORIE_CHECKIN'];
        const result = await getRecordsHealthForAIAnalysis({
            tableName,
            keywords: process.env.HEALTH_FOOD_CALORIE_CHECKIN_KEYWORDS,
            noContainedTasks: caloriesTasks,
            limit: 20 // 太大了会导致后续的ParseDB.query查询出现错误
        });
        const { total, records } = result;
        for (const [index, record] of records.entries()) {
            const data = getRecordJson(record);
            const isCaloriesCheckIn = await isFoodCalorieCheckIn(data.content, data.doneTasks, data.agent);
            if (isCaloriesCheckIn) {
                const group = await wxGroupServices.getWxGroupInfoByObjectId(data.refItchatGroup.objectId);
                //const user = await wxUserServices.getWxUserByObjectId(data.refItchatUser.objectId);
                const aiResp = await foodCalorieCheckInFunGPT({
                    group, msg: data,
                    foodInfo: {
                        mealType: "早餐",
                        foods: []
                    },
                    test: true
                });
                logger.info('aiResp:', aiResp);
                const activityDate = extractDate(data.content) ?? new Date()
                const result = await healthCaloriesServices.mergeRecord(activityDate, {
                    account: data.account,
                    agent: data.agent,
                    activityDate: undefined,
                    userName: data.userName,
                    groupName: data.groupName,
                    mealType: "早餐",
                    foodItems: [],
                    imageFoods: [],

                    aiRespCalories: aiResp?.json?.calories,
                    aiRespNutrition: aiResp?.json?.nutrition,
                    aiRespSummary: aiResp?.json?.summary,
                    aiRespSuggestion: aiResp?.json?.suggestion,
                    aiRespOther: aiResp?.json?.other,
                    aiRespTotalCalories: aiResp?.json?.totalCalories,

                    scoreDetails: { format: 0, content: 0, image: 0, nutrition: 0 },
                    refWxUser: makeWxUserRef(data.refItchatUser.objectId),
                    refWxGroup: makeWxGroupRef(data.refItchatGroup.objectId),
                });
                logger.info('result:', result);
                if (result?.result) {
                    record.set("doneTasks", addDoneTask(record.get('doneTasks'), ["EXTRACT_CALORIES_SUCESS"]));
                } else {
                    record.set("doneTasks", addDoneTask(record.get('doneTasks'), ["EXTRACT_CALORIES_FAILED"]));
                }
                // task 去重

                await record.save();
            } else {
                record.set("doneTasks", addDoneTask(record.get('doneTasks'), ["NOT_FOOD_CALORIE_CHECKIN"]));
                await record.save();
                logger.info(`设置为非餐食数据 ${index}:`, data.content);
            }
            sleep(100)
        };
        logger.info('----------------------------->')
        logger.info(`总记录数 ${total},共处理记录数:${records?.length}`);
    }).timeout(130000); // 30 秒

});