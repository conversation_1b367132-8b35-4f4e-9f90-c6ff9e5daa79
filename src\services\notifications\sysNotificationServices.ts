import { ParseDB } from "../../database/database";
import { EthAddress } from "../../types/types";
import { getLogger } from '../../utils/logger';

import BaseWxNotificationServices, { NotificationType } from "./baseWxNotificationServices";
const logger = getLogger('sysNSrv');
/**
 * 系统微信通知服务类
 * 
 * 功能：
 * 1. 管理微信群组信息的获取和维护
 * 2. 处理系统级别的通知发送（3月打卡、6月打卡、每日打卡、系统通知等）
 * 3. 实现每日消息发送限制（同一消息每日只发送一次）
 * 4. 提供不同类型系统通知的专门发送方法
 * 
 * 说明：
 * - 该类负责所有系统级别的通知发送
 * - 维护微信群组的映射关系
 * - 确保系统通知不会重复发送
 * - 通过环境变量配置不同类型通知的目标群组
 */
export class SysWxNotificationServices extends BaseWxNotificationServices {
    // 获取微信群ID
    private async getWxGroups(agent: EthAddress, name: string): Promise<string[]> {
        try {
            const query = new ParseDB.Query("NotificationWXGroups");
            query.equalTo('agent', agent);
            query.equalTo('name', name);
            const results = await query.find({ useMasterKey: true });
            return results?.map(group => group.get('groupWxid'));
        } catch (error) {
            logger.error('微信通知: 获取微信群失败:', error);
            return [];
        }
    }

    // 获取多个微信群ID
    private async getWxGroupsByNames(agent: EthAddress, groupNames: string[]): Promise<string[]> {
        try {
            const query = new ParseDB.Query("NotificationWXGroups");
            query.equalTo('agent', agent);
            query.containedIn('name', groupNames);

            const results = await query.find({ useMasterKey: true });
            return results?.map(group => group.get('groupWxid')) || [];
        } catch (error) {
            logger.error('微信通知: 获取微信群列表失败:', error);
            return [];
        }
    }

    // 检查消息是否在当天已经发送过
    private async isMsgSentToday(agent: EthAddress, groupWxid: string, msg: string): Promise<boolean> {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            const query = new ParseDB.Query("NotificationWXGroupsMessages");
            query.equalTo('agent', agent);
            query.equalTo('groupWxid', groupWxid);
            query.equalTo('message', msg);
            query.greaterThanOrEqualTo('createdAt', today);

            const results = await query.find({ useMasterKey: true });
            return results && results.length > 0;
        } catch (error) {
            // 如果查询失败，为安全起见，认为消息已发送
            logger.error('检查消息发送记录失败，为安全起见认为消息已发送:', error);
            return true;
        }
    }

    // 系统通知发送（带每日限制）
    private async sendSysNotification(agent: EthAddress, groupWxids: Array<string>, msg: string, type: NotificationType, test: boolean = false): Promise<boolean> {
        const results = await Promise.all(groupWxids.map(async (groupWxid) => {
            try {
                // 检查消息是否已发送
                const isSent = await this.isMsgSentToday(agent, groupWxid, msg);
                if (isSent) {
                    logger.info(`微信通知: 消息已在今天发送过到群${groupWxid}, 跳过发送`);
                    return false;
                }

                // 先尝试记录发送状态
                const { recordResult, objectId } = await this.recordMsgSent(agent, groupWxid, msg, type);
                if (!recordResult) {
                    logger.error(`微信通知: 记录发送状态失败，为避免重复发送，跳过发送到群${groupWxid}`);
                    return false;
                }

                // 记录成功后发送消息
                const result = await this.sendMsgToGroups(agent, [groupWxid], msg, type, test);
                if (!result) {
                    logger.error(`微信通知: 发送消息失败，为避免重复发送，跳过发送到群${groupWxid}`);
                    await this.removeMsgSent(objectId);
                    return false;
                }
                return true;
            } catch (error) {
                logger.error(`微信通知: 发送消息过程发生错误，群${groupWxid}:`, error);
                return false;
            }
        }));

        return results.some(result => result);
    }
    // 打卡异常通知（支持1月、3月、6月）
    async sendMsgToGroupForMonthlyCheck(agent: EthAddress, month: 1 | 3 | 6, msg: string, test: boolean = false): Promise<void> {
        const envVarMap = {
            1: { envVar: process.env.NOTIFY_1MONTH_GROUP_NAME, defaultName: '打卡异常通知(1-)', type: 'sys_1month' as const },
            3: { envVar: process.env.NOTIFY_3MONTH_GROUP_NAME, defaultName: '打卡异常通知(3-)', type: 'sys_3month' as const },
            6: { envVar: process.env.NOTIFY_6MONTH_GROUP_NAME, defaultName: '打卡异常通知(6-)', type: 'sys_6month' as const }
        };

        const config = envVarMap[month];
        const wxids = await this.getWxGroups(agent, config.envVar ?? config.defaultName);
        if (wxids && wxids.length) {
            await this.sendSysNotification(agent, wxids, msg, config.type, test);
        }
    }

    // 兼容旧方法
    async sendMsgToGroupFor1month(agent: EthAddress, msg: string, test: boolean = false): Promise<void> {
        await this.sendMsgToGroupForMonthlyCheck(agent, 1, msg, test);
    }
    async sendMsgToGroupFor3month(agent: EthAddress, msg: string, test: boolean = false): Promise<void> {
        await this.sendMsgToGroupForMonthlyCheck(agent, 3, msg, test);
    }
    async sendMsgToGroupFor6month(agent: EthAddress, msg: string, test: boolean = false): Promise<void> {
        await this.sendMsgToGroupForMonthlyCheck(agent, 6, msg, test);
    }

    // 每日打卡异常通知
    async sendMsgToGroupForDaily(agent: EthAddress, msg: string, test: boolean = false): Promise<void> {
        const wxids = await this.getWxGroups(agent, process.env.NOTIFY_DAILY_GROUP_NAME ?? '每日打卡通知异常');
        if (wxids && wxids.length) {
            await this.sendSysNotification(agent, wxids, msg, 'sys_daily', test);
        }
    }

    // 发送系统通知
    async sendMsgToGroupForSystem(agent: EthAddress, msg: string, test: boolean = false): Promise<void> {
        try {
            const groups = await this.getWxGroups(agent, process.env.NOTIFY_SYSTEM_GROUP_NAME ?? '系统通知');
            if (!groups || groups.length === 0) {
                logger.warn('未找到"系统通知群",不发送!');
                return;
            }

            await this.sendSysNotification(agent, groups, msg, 'system', test);
        } catch (error) {
            logger.error('Error sending message to groups:', error);
            throw error;
        }
    }
}

export default new SysWxNotificationServices();