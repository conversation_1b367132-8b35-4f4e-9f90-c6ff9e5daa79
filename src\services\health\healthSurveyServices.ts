import Parse from "parse/node";
import { HealthSurvey } from "../../types/healthSurvey";
import logger from "../../utils/logger";
import { BaseServices } from '../baseServices';
import { GResponse } from '../../types/types';

class HealthSurveyServices extends BaseServices {
  className = "AIHealthSurvey";

  getRecordJson = (record: Parse.Object): HealthSurvey => record ? {
    objectId: record.id,
    createdAt: record.createdAt,
    updatedAt: record.updatedAt,
    nickname: record.get("nickname"),
    userObjectId: record.get("userObjectId"),
    account: record.get("account"),
    region: record.get("region"),
    district: record.get("district"),
    gender: 'man',
    era: 0,
    bodyCondition: record.get("bodyCondition"),
    weightManagement: record.get("weightManagement"),
    otherConditions: record.get("otherConditions"),
  } : undefined
  /**
   * 创建或更新用户健康调研报告
   */
  createOrUpdateSurvey = async ({ sessionId, survey }: { sessionId: string; survey: HealthSurvey }): Promise<GResponse<HealthSurvey>> => {
    try {
      // 验证必要参数
      if (!survey) {
        throw new Error('Survey data is required');
      }

      const sess = await this.checkSession(sessionId);
      // 查找是否已存在该用户的调研报告
      const query = new Parse.Query(this.className);
      query.equalTo("account", sess.account);
      let surveyObj = await query.first({ useMasterKey: true });

      if (!surveyObj) {
        // 创建新的调研报告
        surveyObj = new Parse.Object(this.className);
        surveyObj.set("account", sess.account);
      }

      // 更新调研报告内容，只更新非空值
      for (const [key, value] of Object.entries(survey)) {
        if (key === "objectId" || key === "createdAt" || key === "updatedAt" || key === "account") {
          continue; // 跳过不需要更新的字段
        }
        // 处理特殊字段
        if (value && value !== undefined && value !== null) {
          logger.info(`Setting ${key} to ${value}`);
          surveyObj.set(key, value);
        }
      };

      // 保存更新
      const result = await surveyObj.save(null, { useMasterKey: true });
      logger.info(`Health survey saved/updated for account: ${sess.account} nickname: ${sess.name}`);

      return this.makeGResponse(this.getRecordJson(result));
    } catch (error) {
      logger.error(`Error in createOrUpdateSurvey: ${error}`);
      throw error;
    }
  }

  /**
   * 获取用户健康调研报告
   */
  getSurvey = async ({ sessionId, account }: { sessionId: string; account: string }) => {
    try {
      const sess = await this.checkSession(sessionId);
      const query = new Parse.Query(this.className);
      query.equalTo("account", account);
      const survey = await query.first({ useMasterKey: true });
      return this.makeGResponse(this.getRecordJson(survey));
    } catch (error) {
      logger.error(`Error in getSurvey: ${error}`);
      throw error;
    }
  }

  /**
   * 获取所有健康调研报告
   */
  getAllSurveys = async ({ sessionId, page = 1, limit = 10 }: { sessionId: string; page?: number; limit?: number }): Promise<{
    results: Parse.Object[];
    total: number;
  }> => {
    try {
      const sess = await this.checkSession(sessionId);
      const query = new Parse.Query(this.className);
      query.descending("createdAt");

      // 获取总数  
      const total = await query.count({ useMasterKey: true });

      // 分页查询
      query.skip((page - 1) * limit);
      query.limit(limit);

      const results = await query.find({ useMasterKey: true });

      return { results, total };
    } catch (error) {
      logger.error(`Error in getAllSurveys: ${error}`);
      throw error;
    }
  }

  /**
   * 删除用户健康调研报告
   */
  deleteSurvey = async (account: string): Promise<boolean> => {
    try {
      const query = new Parse.Query(this.className);
      query.equalTo("account", account);
      const survey = await query.first({ useMasterKey: true });

      if (survey) {
        await survey.destroy({ useMasterKey: true });
        logger.info(`Health survey deleted for account: ${account}`);
        return true;
      }

      return false;
    } catch (error) {
      logger.error(`Error in deleteSurvey: ${error}`);
      throw error;
    }
  }
}

export default new HealthSurveyServices();