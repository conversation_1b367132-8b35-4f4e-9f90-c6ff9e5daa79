import { IKnowReferer } from '../../controllers/controller';
import { GResponse } from "../../types/types";
import ApiError from '../../utils/api_error';
import logger from '../../utils/logger';
import { BaseServices } from "../baseServices";
import {
    RouteItem,
    RoutesInfo,
    constRouters,
    adminRoutesFirst,
    getRouteData,
    saveRouteData,
    getMenuByClientType,
    saveRoutesToMenu
} from './routeUtils';

class RouteServices extends BaseServices {
    constructor() {
        super();
    }
    servicesGetConstantRoutes = async ({ sessionId }: { sessionId: string })
        : Promise<GResponse<RouteItem[]>> => {
        try {
            const sess = await this.checkSession(sessionId);
            const isAdmin = await this.isAdminByRoles(sess.roles)

            const result = await getRouteData(sess.agent, isAdmin ? 'admin' : 'normal_user');
            if (!result) return this.makeGResponse(constRouters);
            const data = result.get('constantRoutes');
            return this.makeGResponse(data);
        }
        catch (e) {
            logger.error(e);
            logger.warn("未登录用户,提供固定菜单")
            return this.makeGResponse(constRouters);
        }
    }

    servicesGetUserRoutes = async ({ sessionId }: { sessionId: string }, referer?: IKnowReferer):
        Promise<GResponse<{ home: string, authRoutes: RouteItem[], routes: RouteItem[] }>> => {
        const sess = await this.checkSession(sessionId);
        const isAdmin = await this.isAdminByRoles(sess.roles);

        // 根据请求来源确定客户端类型
        const clientType = {
            isWechat: this.isWechat(referer),
            isMobile: this.isMobile(referer),
            isPc: this.isPc(referer)
        };
        logger.info(`isWechat: ${clientType.isWechat}, isMobile: ${clientType.isMobile}, isPc: ${clientType.isPc}`);

        // 从菜单表中获取数据，根据客户端类型和用户角色过滤
        const { routes } = await getMenuByClientType(sess.agent, isAdmin, clientType, sess.roles);

        // 如果有路由数据，返回这些数据
        if (routes && routes.length > 0) {
            return this.makeGResponse({
                home: "home",
                authRoutes: routes,
                routes: routes
            });
        }
        else {
            // 如果没有路由数据，返回默认路由
            return this.makeGResponse({
                home: "home",
                constRouters,
                authRoutes: isAdmin ? adminRoutesFirst : undefined,
                routes: undefined
            });
        }
    }

    servicesUploadRoutes = async ({ sessionId, user, routes }: { sessionId: string, user: string, routes: RoutesInfo }) => {
        if (!routes || !sessionId) throw new ApiError("参数错误", 404);
        const sess = await this.isSuperMan(sessionId);

        await saveRouteData(sess.agent, user, routes, sess.name);
        const countConstant = await saveRoutesToMenu(sess.agent, routes.constantRoutes);
        const countAuth = await saveRoutesToMenu(sess.agent, routes.authRoutes);

        return this.makeGResponse({
            message: `成功保存 ${countConstant} 个Constant菜单 ${countAuth} 个Auth菜单`,
            count: countConstant + countAuth
        });
    }

    /**
     * 将路由数据保存到菜单表中
     * @param param0 参数对象
     * @returns 保存结果
     */
    servicesSaveRoutesToMenu = async ({ sessionId, routes }: { sessionId: string, routes: RouteItem[] }) => {
        if (!routes || !sessionId) throw new ApiError("参数错误", 404);
        const sess = await this.isSuperMan(sessionId);

        // 将路由数据保存到菜单表中
        const count = await saveRoutesToMenu(sess.agent, routes);

        return this.makeGResponse({
            message: `成功保存 ${count} 个菜单`,
            count
        });
    }
}

export default new RouteServices()
