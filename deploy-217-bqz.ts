/* eslint-disable no-console */
// (本机运行)可以在windows git bash命令行运行
// node deploy-217-bqz.ts makedist


const fs = require("fs-extra")
const path = require("path")
const { execSync } = require("child_process")
const sd = require("silly-datetime")

const args = process.argv.splice(2)
const isRollback = args.includes("rollback")
const makeDist = args.includes("makedist")

// 当前时间
const curTime = sd.format(new Date(), "YYYYMMDDHH")

// 当前时间格式化
console.log(`${isRollback ? "回滚" : "部署"}时间:${curTime}`)

// 设置本地 dist 文件路径
const distPath = path.resolve(__dirname, "dist")

// 部署路径
const parentPath = path.resolve(__dirname, "..")
const deployPath = path.resolve(parentPath, "dist-bqz-groupx-api")

const makeDistDir = async () => {
  try {
    // 先尝试停止 PM2 进程
    const pm2AppName = "bqz-groupx-api"
    try {
      execSync(`pm2 stop ${pm2AppName}`, { stdio: 'ignore' })
    } catch (e) {
      // 忽略错误，可能进程不存在
    }

    // 等待2秒
    console.log(`[deploy217] pm2停止${pm2AppName}，等待2秒...`)
    await new Promise(resolve => setTimeout(resolve, 2000))
    const pm2ListCmd = `pm2 list`
    const pm2ListOutput = execSync(pm2ListCmd, { encoding: "utf-8" })
    console.log(`[deploy217] pm2列表:\n${pm2ListOutput}`)
    // 使用 Windows 命令强制删除目录
    if (fs.existsSync(distPath)) {
      execSync(`rd /s /q "${distPath}"`, { stdio: 'ignore' })
    }
    if (fs.existsSync(deployPath)) {
      execSync(`rd /s /q "${deployPath}"`, { stdio: 'ignore' })
    }
    console.log(`[deploy217] 删除目录成功!`)
    // 创建发布目录
    fs.ensureDirSync(deployPath)

    // 打包
    execSync("pnpm run build", { stdio: "inherit" });

    // 复制文件
    const files = ["package.json", "tsconfig.build.json", ".env", "public"];


    const cmd = `cp -r ${files.join(" ")} ${distPath}`;
    try {
      console.log(cmd);
      execSync(cmd, { stdio: "inherit" });
    } catch (err) {
      console.error("复制失败:", err);
    }


    // 检查文件是否存在,且文件创建时间在10分钟内
    const { statSync } = require("fs")
    const { mtime } = statSync("dist")
    const diff = new Date().getTime() - new Date(mtime).getTime()
    if (diff > 10 * 60 * 1000) {
      throw new Error("dist 文件创建时间超过10分钟")
    }

    console.log("dist 文件夹生成成功...")

    // 拷贝 dist 目录到发布目录
    const moveCmd = `cp -r ${distPath}/* ${deployPath}`
    try {
      console.log(moveCmd)
      execSync(moveCmd, { stdio: "inherit" })
      console.log("拷贝 dist 目录成功!")
    } catch (err) {
      console.error("拷贝 dist 目录失败:", err)
    }
  } catch (err) {
    console.error('操作目录失败:', err);
    process.exit(1);
  }
}

// 修改 main 函数为异步
async function main() {
  console.log(`${isRollback ? "回滚" : "部署"}：\n \
    \t1 生成 dist\n \
    \t2 拷贝 dist 到发布目录 ${deployPath} \n \
    \t3. pnpm install \n \
    \t4. pm2 restart \n`)

  await makeDistDir()

  // 检查是否已存在，不存在再添加
  const pm2AppName = "bqz-groupx-api"
  const pm2ListCmd = `pm2 list`
  try {
    const pm2ListOutput = execSync(pm2ListCmd, { encoding: "utf-8" })
    if (!pm2ListOutput.includes(pm2AppName)) {
      console.log(`[deploy217] pm2中 ${pm2AppName} 不存在，执行添加操作`)
      // 添加到 pm2，在指定目录下执行
      const pm2AddCmd = `cd ${deployPath} && pm2 start src/index.js --name ${pm2AppName} && pm2 save`
      execSync(pm2AddCmd, { stdio: "inherit" })
      console.log(`${pm2AppName} 在 pm2 中添加成功`)
    } else {
      console.log(`[deploy217] pm2 中 ${pm2AppName} 已存在，无需添加`)
    }
  } catch (err) {
    console.error("[deploy217] 检查 pm2 列表失败:", err)
  }

  // 切换到 dist 目录
  const cdCmd = `cd ${deployPath}`
  console.log(cdCmd)
  execSync(cdCmd, { stdio: "inherit" })

  // 安装依赖
  const pnpmInstallCmd = `cd ${deployPath} && pnpm install`
  try {
    console.log(pnpmInstallCmd)
    execSync(pnpmInstallCmd, { stdio: "inherit" })
    console.log("[deploy217] 安装依赖成功!")
  } catch (err) {
    console.error("[deploy217] 安装依赖失败:", err)
    return // 如果安装依赖失败，终止执行后续命令
  }
  // 拷贝最新配置文件和私钥
  const copyCmd = `cp ${parentPath}/prod.dist.env ${deployPath}/.env.development`
  const copyCmd2 = `cp ${parentPath}/meal_checkin_prompt.txt ${deployPath}/`

  const copyKeys = `cp -R ${parentPath}/keys ${deployPath}/`
  try {
    console.log(copyCmd)
    execSync(copyCmd, { stdio: "inherit" })
    execSync(copyCmd2, { stdio: "inherit" })
    execSync(copyKeys, { stdio: "inherit" })
  } catch (err) {
    console.error("[deploy217] 拷贝配置文件失败:", err)
  }
  // 重启 index.js
  const pm2RestartCmd = `pm2 restart ${pm2AppName}`
  try {
    console.log(pm2RestartCmd)
    execSync(pm2RestartCmd, { stdio: "inherit" })
    console.log("[deploy217] 重启成功!")
  } catch (err) {
    console.error("[deploy217] 重启失败:", err)
  }
}

// 调用 main
main().catch(console.error)
