import { Request, Response } from 'express';
import { Result, ValidationError, validationResult } from 'express-validator';
import {
  createDiscordInvite,
  DiscordChannel,
  fetchDiscordGuild,
  fetchDiscordGuildChannels,
  getDiscordInvites,
} from '../services/discord/discordServices';
import { serviceGetGuildMemberships } from '../services/memberShipServices';
// import platformUserJoin, {
//   platformGetGuild,
//   platformUserLeave,
//   platformUserStatus,
// } from "../services/platformServices";
import { guildPlatformData } from '../static';
import logger from '../utils/logger';
import { controlFunc } from './controller';
import services from '../services/platformServices';
import { EthAddress } from '../types/types';

const getGuilds = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const guild = [guildPlatformData];
      res.status(200).json(guild);
    } catch (error: any) {
      res.status(204).send();
    }
  }
};

const getGuild = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.platformGetGuild, { ...req?.params });

const join = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.platformUserJoin, req.body);

// const join = async (req: Request, res: Response): Promise<void> => {
//   const errors: Result<ValidationError> = validationResult(req);
//   if (!errors.isEmpty()) {
//     res.status(400).json({ errors: errors.array() });
//   } else {
//     try {
//       logger.info(`/platform/user/join`, req.body);
//       const guilds = await platformUserJoin(req.body);
//       res.status(200).json(guilds);
//     } catch (error: any) {
//       res.status(204).send();
//     }
//   }
// };

const leave = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      logger.info(`/platform/user/leave`, req.body);
      const guild = services.platformUserLeave(req.body);
      res.status(200).json(guild);
    } catch (error: any) {
      res.status(204).send();
    }
  }
};

const access = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const guilds = [guildPlatformData];
      res.status(200).json(guilds);
    } catch (error: any) {
      res.status(204).send();
    }
  }
};

const member = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const guilds = [guildPlatformData];
      res.status(200).json(guilds);
    } catch (error: any) {
      res.status(204).send();
    }
  }
};

// 通过用户id获取用户所在的所有group
const status = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.platformUserStatus, { ...req?.body });

// const status = async (req: Request,res: Response): Promise<void> => {
//   const errors: Result<ValidationError> = validationResult(req);
//   logger.info("status params:", req?.params);
//   logger.info("status body:", req?.body);

//   const { platformName, platformUserId } = req.body;
//   if (!errors.isEmpty()) {
//     res.status(400).json({ errors: errors.array() });
//   } else {
//     try {
//       const guilds = await getGuildPlatformsByUserId(
//         platformName,
//         platformUserId,
//       );
//       res.status(200).json(guilds);
//     } catch (error: any) {
//       res.status(204).send();
//     }
//   }
// };

const membership = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const account = req?.params?.account as EthAddress;

      const guilds = serviceGetGuildMemberships(account);
      res.status(200).json(guilds);
    } catch (error: any) {
      res.status(204).send();
    }
  }
};
const oauthByCode = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.getPlatformUserInfoByCode, {
    account: req.body?.params?.addr,
    code: req.body?.payload?.code,
    clientId: req.body?.payload?.clientId,
    platformName: req.body?.payload?.platformName,
    agent: req.body?.payload?.agent,
    tags: req.body?.payload?.tags,
  });

const connect = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const guilds = {
        connectLink: 'https://guild.xyz/connect/sjfuvasodifjsdfk',
        alreadyConnected: false,
      };
      res.status(200).json(guilds);
    } catch (error: any) {
      res.status(204).send();
    }
  }
};

type Category = {
  id: string;
  name: string;
  channels: DiscordChannel[];
};

type Role = {
  guild: string;
  icon: string;
  unicodeEmoji: string;
  id: string;
  name: string;
  color: number;
  hoist: boolean;
  rawPosition: number;
  permissions: string;
  managed: boolean;
  mentionable: boolean;
  createdTimestamp: number;
};
type DiscordServerData = {
  serverIcon: string;
  membersWithoutRole: number;
  serverName: string;
  serverId: string;
  categories: Category[];
  isAdmin: boolean;
  channels?: any[];
  roles: Role[];
  invite: string;
  inviteChannel: string;
};
const getDiscordServer = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const serverId = req.params?.serverId;

      let guild = null;
      let inviteReturn = null;
      // let channels: any[] = [];
      const categories: Category[] = [];
      try {
        logger.info('fetchDiscordGuild:', serverId);
        guild = await fetchDiscordGuild(serverId);

        let channelId: string = null;
        const channelsRes = await fetchDiscordGuildChannels(serverId);
        channelsRes?.forEach((item: any) => {
          if (item.type === 4) {
            const channels = channelsRes?.filter((c: DiscordChannel) => c.parent_id === item.id);
            categories.push({ ...item, channels });
          } else if (!channelId) {
            channelId = item.id;
          }
        });
        inviteReturn = await getDiscordInvites(channelId);
        if (!inviteReturn) {
          inviteReturn = await createDiscordInvite(channelId);
        }
      } catch (error: any) {
        logger.error(`fetchDiscordGuild error: `, error);
      }

      const data: DiscordServerData = {
        serverIcon: guild?.icon ?? '/default_discord_icon.png',
        membersWithoutRole: 1,
        serverName: guild?.name,
        serverId,
        categories,
        isAdmin: !!guild,

        roles: [],
        ...guild,
        invite: inviteReturn ? `https://discord.com/invite/${inviteReturn?.code}` : null,
        inviteChannel: inviteReturn?.inviteChannel,
        // channels,
      };
      // logger.info("getDiscordServer:", data);
      res.status(200).json(data);
    } catch (error: any) {
      res.status(204).send();
    }
  }
};
const postDiscordButton = async (req: Request, res: Response): Promise<void> => { };
export {
  getGuild,
  getGuilds,
  join,
  leave,
  access,
  status,
  connect,
  member,
  membership,
  getDiscordServer,
  postDiscordButton,
  oauthByCode,
};
