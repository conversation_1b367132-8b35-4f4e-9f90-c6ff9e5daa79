/**
 * 修复工具：恢复被错误标记为HEALTH_EXPIRED的微信群
 * 
 * 这个工具会检查所有被标记为HEALTH_EXPIRED的微信群，
 * 如果群组的有效期还没有到期，则将其状态恢复为NORMAL。
 */

import { ParseDB } from "../database/database";
import { getLogger } from "../utils/logger";
import { EthZero } from "../types/types";
import { getGroupsBySearch } from "../services/wechat/cowGroupFunction";
import {
  getDateFromGroupName3Month,
  getDateFromGroupNameMonth136
} from "../services/health/healthPatientsWxGroup";
import initParseServer from "../database/database";

const logger = getLogger('fixHealthExpiredGroups');

// 初始化数据库连接
const init = async () => {
  await initParseServer();
  logger.info("数据库连接初始化完成");
};

// 查找所有被错误标记为过期的群组
const findIncorrectlyExpiredGroups = async () => {
  logger.info("开始查找被错误标记为过期的群组...");

  const incorrectlyExpiredGroups = [];

  // 查找3-前缀的群组
  const { results: expiredGroups3 } = await getGroupsBySearch(EthZero, 1, 300, {
    groupNameRegex: /^3-/i,
    status: ['HEALTH_EXPIRED']
  });

  logger.info(`找到 ${expiredGroups3.length} 个被标记为过期的3-前缀群组`);

  for (const group of expiredGroups3) {
    const startDate = getDateFromGroupName3Month(EthZero, group.groupNickName, new Date(group.createdAt));

    if (startDate) {
      const now = new Date();
      const threeMonthsLater = new Date(startDate);
      threeMonthsLater.setMonth(threeMonthsLater.getMonth() + 3);

      if (now < threeMonthsLater) {
        // 这个群组被错误地标记为过期
        incorrectlyExpiredGroups.push({
          objectId: group.objectId,
          groupNickName: group.groupNickName,
          startDate,
          expiryDate: threeMonthsLater,
          type: '3-month'
        });
        logger.info(`找到被错误标记的群组: ${group.groupNickName}, 开始日期: ${startDate.toISOString().split('T')[0]}, 过期日期: ${threeMonthsLater.toISOString().split('T')[0]}`);
      }
    } else {
      logger.warn(`无法从群名中提取日期: ${group.groupNickName}`);
    }
  }

  // 查找6-前缀的群组
  const { results: expiredGroups6 } = await getGroupsBySearch(EthZero, 1, 300, {
    groupNameRegex: /^6-/i,
    status: ['HEALTH_EXPIRED']
  });

  logger.info(`找到 ${expiredGroups6.length} 个被标记为过期的6-前缀群组`);

  for (const group of expiredGroups6) {
    const startDate = getDateFromGroupNameMonth136(EthZero, group.groupNickName, new Date(group.createdAt), '6');

    if (startDate) {
      const now = new Date();
      const sixMonthsLater = new Date(startDate);
      sixMonthsLater.setMonth(sixMonthsLater.getMonth() + 6);

      if (now < sixMonthsLater) {
        // 这个群组被错误地标记为过期
        incorrectlyExpiredGroups.push({
          objectId: group.objectId,
          groupNickName: group.groupNickName,
          startDate,
          expiryDate: sixMonthsLater,
          type: '6-month'
        });
        logger.info(`找到被错误标记的群组: ${group.groupNickName}, 开始日期: ${startDate.toISOString().split('T')[0]}, 过期日期: ${sixMonthsLater.toISOString().split('T')[0]}`);
      }
    } else {
      logger.warn(`无法从群名中提取日期: ${group.groupNickName}`);
    }
  }

  // 查找1-前缀的群组
  const { results: expiredGroups1 } = await getGroupsBySearch(EthZero, 1, 300, {
    groupNameRegex: /^1-/i,
    status: ['HEALTH_EXPIRED']
  });

  logger.info(`找到 ${expiredGroups1.length} 个被标记为过期的1-前缀群组`);

  for (const group of expiredGroups1) {
    const startDate = getDateFromGroupNameMonth136(EthZero, group.groupNickName, new Date(group.createdAt), '1');

    if (startDate) {
      const now = new Date();
      const oneMonthLater = new Date(startDate);
      oneMonthLater.setMonth(oneMonthLater.getMonth() + 1);

      if (now < oneMonthLater) {
        // 这个群组被错误地标记为过期
        incorrectlyExpiredGroups.push({
          objectId: group.objectId,
          groupNickName: group.groupNickName,
          startDate,
          expiryDate: oneMonthLater,
          type: '1-month'
        });
        logger.info(`找到被错误标记的群组: ${group.groupNickName}, 开始日期: ${startDate.toISOString().split('T')[0]}, 过期日期: ${oneMonthLater.toISOString().split('T')[0]}`);
      }
    } else {
      logger.warn(`无法从群名中提取日期: ${group.groupNickName}`);
    }
  }

  // 特别检查 "3-4.5朱朱体重管理第二阶段"
  const { results: specificGroup } = await getGroupsBySearch(EthZero, 1, 10, {
    groupNickName: "3-4.5朱朱体重管理第二阶段"
  });

  if (specificGroup && specificGroup.length > 0) {
    const group = specificGroup[0];
    logger.info(`找到特定群组: ${group.groupNickName}, 状态: ${group.status}`);

    if (group.status === "HEALTH_EXPIRED") {
      const startDate = getDateFromGroupName3Month(EthZero, group.groupNickName, new Date(group.createdAt));

      if (startDate) {
        const now = new Date();
        const threeMonthsLater = new Date(startDate);
        threeMonthsLater.setMonth(threeMonthsLater.getMonth() + 3);

        if (now < threeMonthsLater) {
          // 确保这个特定群组被添加到修复列表中
          if (!incorrectlyExpiredGroups.some(g => g.objectId === group.objectId)) {
            incorrectlyExpiredGroups.push({
              objectId: group.objectId,
              groupNickName: group.groupNickName,
              startDate,
              expiryDate: threeMonthsLater,
              type: '3-month'
            });
            logger.info(`添加特定群组到修复列表: ${group.groupNickName}`);
          }
        }
      }
    }
  } else {
    logger.warn("未找到特定群组: 3-4.5朱朱体重管理第二阶段");
  }

  logger.info(`总共找到 ${incorrectlyExpiredGroups.length} 个被错误标记为过期的群组`);
  return incorrectlyExpiredGroups;
};

// 修复被错误标记为过期的群组
const fixIncorrectlyExpiredGroups = async (groups) => {
  logger.info(`开始修复 ${groups.length} 个被错误标记为过期的群组...`);

  const fixedGroups = [];
  const failedGroups = [];

  for (const group of groups) {
    try {
      // 更新群组状态为 NORMAL
      const query = new ParseDB.Query('AIItchatGroups');
      query.equalTo('objectId', group.objectId);
      const record = await query.first();

      if (record) {
        record.set('status', 'NORMAL');
        await record.save();

        fixedGroups.push({
          objectId: group.objectId,
          groupNickName: group.groupNickName,
          oldStatus: 'HEALTH_EXPIRED',
          newStatus: 'NORMAL',
          type: group.type
        });

        logger.info(`成功修复群组: ${group.groupNickName}`);
      } else {
        logger.warn(`未找到群组记录: ${group.groupNickName}`);
        failedGroups.push({
          objectId: group.objectId,
          groupNickName: group.groupNickName,
          reason: 'Record not found'
        });
      }
    } catch (error) {
      logger.error(`修复群组 ${group.groupNickName} 时出错:`, error);
      failedGroups.push({
        objectId: group.objectId,
        groupNickName: group.groupNickName,
        reason: (error as Error).message || String(error)
      });
    }
  }

  logger.info(`成功修复 ${fixedGroups.length} 个群组`);
  if (failedGroups.length > 0) {
    logger.warn(`${failedGroups.length} 个群组修复失败`);
  }

  return { fixedGroups, failedGroups };
};

// 主函数
const main = async () => {
  try {
    await init();

    // 查找被错误标记为过期的群组
    const incorrectlyExpiredGroups = await findIncorrectlyExpiredGroups();

    if (incorrectlyExpiredGroups.length === 0) {
      logger.info("没有找到被错误标记为过期的群组，无需修复");
      process.exit(0);
    }

    // 询问用户是否要修复这些群组
    console.log(`找到 ${incorrectlyExpiredGroups.length} 个被错误标记为过期的群组。是否要修复？(y/n)`);

    // 在实际环境中，这里应该等待用户输入
    // 为了简化，我们直接修复
    const { fixedGroups, failedGroups } = await fixIncorrectlyExpiredGroups(incorrectlyExpiredGroups);

    // 输出修复结果
    console.log("修复完成!");
    console.log(`成功修复 ${fixedGroups.length} 个群组`);
    if (failedGroups.length > 0) {
      console.log(`${failedGroups.length} 个群组修复失败`);
    }

    // 输出详细信息
    console.log("\n成功修复的群组:");
    fixedGroups.forEach(group => {
      console.log(`- ${group.groupNickName} (${group.type})`);
    });

    if (failedGroups.length > 0) {
      console.log("\n修复失败的群组:");
      failedGroups.forEach(group => {
        console.log(`- ${group.groupNickName}: ${group.reason}`);
      });
    }

    process.exit(0);
  } catch (error) {
    logger.error("执行过程中出错:", error);
    process.exit(1);
  }
};

// 如果直接运行此文件，则执行主函数
if (require.main === module) {
  main();
}

// 导出函数，以便其他模块可以使用
export {
  findIncorrectlyExpiredGroups,
  fixIncorrectlyExpiredGroups
};
