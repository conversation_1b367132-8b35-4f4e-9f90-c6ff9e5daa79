{"version": "1.5.6", "name": "group-api-server", "main": "index.ts", "scripts": {"start": "nodemon -f ./.env.development nodemon src/index.ts", "dev": "nodemon", "dev223": "nodemon", "dev212": "nodemon", "iknow-admin": "nodemon", "dev223-xuzijun": "nodemon", "dev221": "nodemon", "macos": "nodemon", "build2": "tsc", "build": "tsc && pnpm run uglify", "uglify": "node ./minifyJs.js", "ts-node": "ts-node --compiler-options \"{\\\"module\\\":\\\"commonjs\\\"}\"", "format": "prettier --write .", "lint-fix": "eslint --fix . --ext .ts,.tsx,.js,.jsx", "test": "mocha --require ts-node/register --require tsconfig-paths/register --require source-map-support/register --require test/setup.ts --recursive --exit --timeout 20000", "test-contract": "cross-env NODE_ENV=test DISABLE_LOGS=true mocha --require ts-node/register --require tsconfig-paths/register --require source-map-support/register --require test/setup.ts --exit --timeout 20000 --reporter dot test/**/contract.test.ts", "test-router-root": "cross-env NODE_ENV=test DISABLE_LOGS=true mocha --require ts-node/register --require tsconfig-paths/register --require source-map-support/register --require test/setup.ts --exit --timeout 20000 --reporter dot test/**/router-root.test.ts", "test-user": "pnpm test test/user.test.ts", "test-health": "pnpm test test/healthServices.test.ts", "test-doctor": "pnpm test test/doctorServices.test.ts", "test-nft": "cross-env NODE_ENV=test DISABLE_LOGS=true mocha --require ts-node/register --require tsconfig-paths/register --require source-map-support/register --require test/setup.ts --exit --timeout 20000 --reporter dot test/**/nft.test.ts", "test3": "cross-env NODE_ENV=test DISABLE_LOGS=true mocha --require ts-node/register --require tsconfig-paths/register --require source-map-support/register --require test/setup.ts --recursive --exit --timeout 20000 --reporter dot", "test4": "cross-env NODE_ENV=test DISABLE_LOGS=true mocha --require ts-node/register --require tsconfig-paths/register --require source-map-support/register --require test/setup.ts --exit --timeout 20000 --reporter dot test/**/chatlistServicesFunction.test.ts", "test5": "pnpm test test/health/healthFood.test.ts", "pre-release": "mocha --require ts-node/register --require tsconfig-paths/register --require source-map-support/register --require test/setup.ts --exit --timeout 20000 --reporter spec 'test/health/healthFunction.test.ts' 'test/a.test.ts' 'test/pre_release/*.test.ts' 'test/health/healthPatientsWxGroup.test.ts'", "test-health-group": "pnpm test test/health/healthGroupExpiredTest.ts"}, "dependencies": {"@google-cloud/local-auth": "^2.1.1", "@nervina-labs/cota-sdk": "^0.8.0", "@nervosnetwork/ckb-sdk-utils": "^0.103.1", "@peculiar/x509": "^1.9.7", "@react-native-async-storage/async-storage": "^1.17.11", "@tars/winston-tars": "^2.1.3", "@types/morgan": "^1.9.4", "@types/parse": "^3.0.4", "arweave": "^1.13.1", "async-mutex": "^0.5.0", "axios": "^1.2.2", "cors": "^2.8.5", "cron": "^2.2.0", "dayjs": "^1.11.13", "eslint-plugin-react": "^7.32.2", "ethers": "^5.6.5", "express": "^4.18.2", "express-validator": "^6.11.1", "fast-safe-stringify": "^2.1.1", "fs-extra": "^11.2.0", "google-auth-library": "^8.7.0", "googleapis": "^109.0.1", "https-proxy-agent": "^5.0.1", "jsrsasign": "^11.1.0", "log4js": "^6.9.1", "lunisolar": "^2.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.44", "moment-timezone-ts": "^0.5.6", "morgan": "^1.10.0", "nodemon": "^3.0.1", "parse": "^4.0.1", "should": "^13.2.3", "superagent": "^8.1.2", "supertest": "^6.3.3", "urllib": "^3.23.0", "uuid": "^9.0.0", "web3": "^1.8.2", "wechatpay-node-v3": "2.1.8", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "wxpay-v3": "^3.0.2"}, "devDependencies": {"@types/cors": "^2.8.12", "@types/express": "^4.17.11", "@types/isomorphic-fetch": "^0.0.36", "@types/jest": "^29.4.0", "@types/mocha": "^10.0.1", "@types/moment": "^2.13.0", "@types/moment-timezone": "^0.5.30", "@types/node": "^15.14.9", "@types/sinon": "^17.0.3", "@types/superagent": "^8.1.4", "@types/uuid": "^9.0.3", "@typescript-eslint/eslint-plugin": "^5.52.0", "@typescript-eslint/parser": "^5.52.0", "@typescript-eslint/typescript-estree": "^5.52.0", "archiver": "^5.3.1", "chai": "^4.3.7", "cross-env": "^7.0.3", "dotenv": "^10.0.0", "eslint": "^8.34.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-typescript-enum": "^2.1.0", "husky": "^8.0.3", "isomorphic-fetch": "^3.0.0", "lint-staged": "^13.1.2", "mocha": "^10.2.0", "prettier": "^2.8.0", "pretty-quick": "^3.1.3", "silly-datetime": "^0.1.2", "sinon": "^19.0.2", "ssh2": "^1.11.0", "ts-node": "^10.9.1", "typescript": "^5.8.2", "uglify-js": "^3.17.4", "uglifyjs": "^2.4.11"}, "husky": {"hooks": {"pre-commit": "npx lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix --cache --cache-location 'node_modules/.cache/.eslintcache'", "*.{js,jsx,ts,tsx,md,html,css}": "pretty-quick --staged"}, "nodemonConfig": {"exec": "node -r ./register -r ts-node/register", "ext": "ts", "ignore": [".git", "node_modules"], "watch": ["./src"]}}