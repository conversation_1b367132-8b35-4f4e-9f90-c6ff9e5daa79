import logger from '../src/utils/logger';
import * as log4js from 'log4js';
import { readFileSync } from 'fs';
import { join } from 'path';

// 加载环境变量配置
import '../src/config';

// 添加全局函数来加载 JSON 文件
(global as any).loadJsonFile = (relativePath: string) => {
    const fullPath = join(__dirname, relativePath);
    return JSON.parse(readFileSync(fullPath, 'utf8'));
};

// 保存原始的控制台方法，用于测试输出
const originalConsole = {
    log: console.log,
    info: console.info,
    warn: console.warn,
    error: console.error,
    debug: console.debug,
};

// 创建一个代理函数，只允许来自 mocha 的输出
const createLogProxy = (originalFn: Function) => {
    return function (this: any, ...args: any[]) {
        // 检查调用栈，如果是来自 mocha 的调用，则允许输出
        const stack = new Error().stack || '';
        if (stack.includes('mocha') || stack.includes('chai')) {
            originalFn.apply(this, args);
        }
    };
};

// 如果设置了 DISABLE_LOGS 环境变量，则禁用所有控制台输出
if (process.env.DISABLE_LOGS === 'true') {
    // 重写控制台方法，使用代理
    console.log = createLogProxy(originalConsole.log);
    console.info = createLogProxy(originalConsole.info);
    console.warn = createLogProxy(originalConsole.warn);
    console.error = createLogProxy(originalConsole.error);
    console.debug = createLogProxy(originalConsole.debug);

    // 禁用 log4js logger 输出
    const log4jsLogger = logger as log4js.Logger;
    log4jsLogger.level = 'off';
}

// 在所有测试完成后恢复设置
process.on('exit', () => {
    if (process.env.DISABLE_LOGS === 'true') {
        console.log = originalConsole.log;
        console.info = originalConsole.info;
        console.warn = originalConsole.warn;
        console.error = originalConsole.error;
        console.debug = originalConsole.debug;

        // 恢复 logger 输出
        const log4jsLogger = logger as log4js.Logger;
        log4jsLogger.level = 'info';  // 或者其他适当的日志级别
    }
});