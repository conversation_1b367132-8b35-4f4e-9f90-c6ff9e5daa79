import axios from "axios"
import logger from "../../utils/logger"
import GPTServerComm, { IGPTResponse } from "./gptServerComm"

class GPTServerApiQWen extends GPTServerComm {
  // 通义千问大模型
  async qianwenPostRequest(userid: string, prompt: string): Promise<any> {
    const apiKey = process.env.QWEN_API_KEY
    const url = process.env.QWEN_CHAT_URL

    logger.warn("====>开始调用 通义千问 post\n", prompt)

    const json = [
      {
        "role": "system",
        "content": process.env.SYSTEM_PROMPT || "你现在时一个健康医疗记录助手，用于识别用户的录入信息。"
      },
      {
        role: "user",
        content: `${prompt}`,
      },
    ]
    const options = {
      method: "POST",
      url,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      data: {
        id: userid,//未确认
        session_id: userid,//未确认
        user_id: userid,//未确认
        model: "qwen-turbo",
        input: { messages: json },
        parameters: {
        }
      },
    }

    try {
      const response = await axios.request(options)
      if (response.status === 200) {
        logger.info("qianwenPostRequest===>:")
        logger.info(response?.data?.output?.text)

        return response
      }
      logger.error("qianwenPostRequest:", response)
    } catch (error) {
      logger.error("qianwenPostRequest 发生意外错误:", error)
    }
    return null
  }


  async qwenQuery<T>(userid: string, prompt: string): Promise<IGPTResponse<T>> {
    const res = await this.qianwenPostRequest(userid, prompt)
    if (!res?.data || res.status !== 200) {
      logger.error(`gptServerApi.getGptResult error: ${res}`)
      return null
    }

    const text = res.data.output?.text

    return {
      model: "qwen",
      showText: text,
      aiTokens: res?.data?.usage?.total_tokens,
      completionTokens: res?.data?.usage?.output_tokens,
      promptTokens: res?.data?.usage?.input_tokens,
    }
  }




  async getUserSummary(message: string) {
    try {
      const prompt = `对如下内容进行总结,直接返回结果,不要解释,其他内容不提供:"${message}"`
      // const res = await this.linkaiPostRequest(prompt)
      // return res.data.choices[0].message
      return ""
    } catch (error) {
      logger.error("Failed to summarize content:", error)
    }
    return null
  }


}

const gptQWen = new GPTServerApiQWen()
export default gptQWen
