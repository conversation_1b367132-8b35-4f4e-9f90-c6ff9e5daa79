import exp from 'constants';
import { controlFunc } from './controller';
import { Request, Response } from 'express';
import { healthReportServices as service } from '../services/health/healthReportSevices';
import session from '../api/routes/session';

class HealthReportController {
    // 获取体检报告
    getHealthReport = (req: Request, res: Response) =>
        controlFunc(req, res, service.servicesGetReport, {
            userObjectId: req.params.userObjectId,
            reportType: req.params.reportType,
            sessionId: req.headers['session-id']
        });


    // 获取体检报告列表
    getHealthReportList = (req: Request, res: Response) =>
        controlFunc(req, res, service.servicesGetReportList, {
            userObjectId: req.params.userObjectId,
            reportType: req.params.reportType,
            sessionId: req.headers['session-id'],
            pageNum: req.params.pageNum,
            pageSize: req.params.pageSize,
            search: req.query
        });

}

export const healthReportCtl = new HealthReportController();