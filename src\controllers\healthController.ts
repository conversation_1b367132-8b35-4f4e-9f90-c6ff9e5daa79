import { Request, Response } from "express";
import notAtServices from "../services/chatlist/groupNotAtMsgServices";
import healthServices from "../services/health/base/healthBaseServices";
import caloriesPlanServices from "../services/health/calories/healthCaloriesPlan";
import caloriesServices from "../services/health/calories/healthCaloriesServices";
import healthPatientsWxGroup from "../services/health/healthPatientsWxGroupClass";
import healthSettingServices from "../services/health/healthSettingServices";
import healthSurveyServices from "../services/health/healthSurveyServices";
import healthUserCardServices from "../services/health/healthUserCardServices";
import sleepServices from "../services/health/sleep/healthSleepServices";
import weightLossServices from "../services/health/weightLoss/healthWeightLossService";
import { controlFunc } from "./controller";

const getHello = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthServices.getHello, req?.query);

const getCheckinStatisticsOfGroupInRange = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, notAtServices.servicesGetKeywordsStatisticsGroupRnage, {
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
    groupObjectId: req.params.groupObjectId,
    search: req.query
  });

const postWeightLoss = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, weightLossServices.servicesWeightLoss, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    account: req.params.account,
    body: req.body?.payload
  });

const postCaloriesRecord = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesServices.servicesInsertRecord, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    account: req.params.account,
    body: req.body?.payload
  });

const getCaloriesPlan = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesGetCaloriesPlan, {
    sessionId: req.headers['session-id'],
    planObjectId: req.params.planObjectId
  });

const getCaloriesPlanList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesGetCaloriesPlans, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId,
    pageNum: req.params.pageNum,
    pageSize: req.params.pageSize,
    search: req.query
  });

const postCaloriesPlanSet = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesSetCaloriesPlan, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId,
    body: req.body?.payload
  });

const removeCaloriesPlan = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesRemoveCaloriesPlan, {
    sessionId: req.headers['session-id'],
    planObjectId: req.params.planObjectId,
    body: req.body?.payload
  });

const postCaloriesMeal = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesSetCaloriesMeal, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId,
    body: req.body?.payload
  });

const removeCaloriesMeal = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesRemoveCaloriesMeal, {
    sessionId: req.headers['session-id'],
    mealObjectId: req.params.mealObjectId,
    body: req.body?.payload
  });

const getCaloriesMeal = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesGetCaloriesMeal, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId
  });

const getCaloriesMealList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesGetCaloriesMealList, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId,
    pageNum: req.params.pageNum,
    pageSize: req.params.pageSize,
    search: req.query
  });

// 获取系统可用的卡路里摄入食物列表
const getCaloriesFoods = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesGetCaloriesFoods, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId,
    pageNum: req.params.pageNum,
    pageSize: req.params.pageSize,
    search: req.query
  });

const postCaloriesFood = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesSetCaloriesFood, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId,
    body: req.body?.payload
  });

const getCaloriesFood = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesGetCaloriesFood, {
    sessionId: req.headers['session-id'],
    foodObjectId: req.params.foodObjectId
  });

const getCaloriesCard = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesGetCalorieCard, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId
  });



const postCaloriesCard = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, caloriesPlanServices.servicesSetCalorieCard, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId,
    body: req.body?.payload
  });

const postWeightLossLastData = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, weightLossServices.servicesWeightLossLastData, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    account: req.params.account,
    body: req.body?.payload
  });

const postImageOcrRecord = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, weightLossServices.servicesImageOcrRecord, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    account: req.params.account,
    body: req.body?.payload
  });

const getSleepRecordLastData = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, sleepServices.servicesSleepRecordLastData, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    account: req.params.account,
    body: req.body?.payload
  })
const postSleepRecord = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, sleepServices.servicesSleepRecord, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    account: req.params.account,
    body: req.body?.payload
  })

// 获取健康打卡第二阶段消息列表(专用)
const getSettingSecondStageMsgList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesGetSecondStageMsgList,
    {
      agent: req.headers['iknow-agent'],
      sessionId: req.headers['session-id'],
      groupOID: req.query?.groupOID
    })
// 获取健康打卡第二阶段设置
const getSettingSecondStage = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesGetSecondStage,
    {
      agent: req.headers['iknow-agent'],
      sessionId: req.headers['session-id'],
      groupOID: req.query?.groupOID
    })

// 设置健康打卡第二阶段消息列表(专用)
const postSettingSecondStageMsgList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesSetSecondStageMsgList, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    body: req.body?.payload
  })

// 获取健康打卡加入群消息列表(专用)
const getSettingJoinGroupMsgList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesGetJoinGroupMsgList,
    {
      agent: req.headers['iknow-agent'],
      sessionId: req.headers['session-id'],
      groupOID: req.query?.groupOID
    })
// 设置健康打卡加入群消息列表(专用)
const postSettingJoinGroupMsgList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesSetJoinGroupMsgList, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    body: req.body?.payload
  })
// 获取健康打卡加入群设置
const getSettingJoinGroup = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesGetJoinGroup,
    { sessionId: req.headers['session-id'], groupOID: req.query?.groupOID })

// 获取两周复诊提醒设置
const getSettingTwoWeekReminder = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesGetTwoWeekReminder,
    { sessionId: req.headers['session-id'], groupOID: req.query?.groupOID })

// 获取两周复诊提醒消息列表(专用)
const getSettingTwoWeekReminderMsgList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesGetTwoWeekReminderMsgList,
    { sessionId: req.headers['session-id'], groupOID: req.query?.groupOID })
// 设置两周复诊提醒消息列表(专用)
const postSettingTwoWeekReminderMsgList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSettingServices.servicesSetTwoWeekReminderMsgList, {
    sessionId: req.headers['session-id'],
    body: req.body?.payload
  })

const getGroupList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthPatientsWxGroup.servicesGetGroupList, {
    sessionId: req.headers['session-id'],
    pageNum: req.params.pageNum,
    pageSize: req.params.pageSize,
    search: req.query
  })

// 获取用户信息档案卡列表
const getUserInfoCardList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthUserCardServices.servicesGetUserInfoCardList, {
    sessionId: req.headers['session-id'],
    pageNum: req.params.pageNum,
    pageSize: req.params.pageSize,
    search: req.query
  })
// 设置用户信息档案卡
const postUserInfoCardSet = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthUserCardServices.servicesSetUserInfoCard, {
    sessionId: req.headers['session-id'],
    userObjectId: req.params.userObjectId,
    body: req.body?.payload
  })
const test = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthPatientsWxGroup.servicesTest, req.headers['session-id'])

/**
 * 提交用户健康调研报告
 */
const postUserHealthSurvey = async (req: Request, res: Response) =>
  controlFunc(req, res, healthSurveyServices.createOrUpdateSurvey, {
    sessionId: req.headers['session-id'],
    survey: req.body?.payload
  })
/**
 * 获取用户健康调研报告
 */
const getUserHealthSurvey = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSurveyServices.getSurvey, {
    sessionId: req.headers['session-id'],
    account: req.params.account
  });

/**
 * 获取所有健康调研报告列表
 */
const getHealthSurveyList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, healthSurveyServices.getAllSurveys, {
    sessionId: req.headers['session-id'],
    page: parseInt(req.params.pageNum) || 1,
    limit: parseInt(req.params.pageSize) || 10
  });

export default getHello;
export {

  // 卡路里相关
  getCaloriesCard, getCaloriesFood, getCaloriesFoods,
  // 卡路里饮食记录
  getCaloriesMeal,
  getCaloriesMealList, getCaloriesPlan,
  getCaloriesPlanList, getCheckinStatisticsOfGroupInRange,
  getGroupList, getHealthSurveyList,
  // 基础功能
  getHello,
  // 进群通知设置
  getSettingJoinGroup,
  getSettingJoinGroupMsgList,
  // 健康打卡第二阶段
  getSettingSecondStage,
  getSettingSecondStageMsgList,
  // 两周复诊提醒
  getSettingTwoWeekReminder,
  getSettingTwoWeekReminderMsgList,
  // 睡眠记录
  getSleepRecordLastData, getUserHealthSurvey,
  // 用户信息档案卡
  getUserInfoCardList, postCaloriesCard, postCaloriesFood, postCaloriesMeal, postCaloriesPlanSet, postCaloriesRecord, postImageOcrRecord, postSettingJoinGroupMsgList, postSettingSecondStageMsgList, postSettingTwoWeekReminderMsgList, postSleepRecord,
  // 健康调研报告
  postUserHealthSurvey, postUserInfoCardSet,
  // 减重打卡
  postWeightLoss,
  postWeightLossLastData, removeCaloriesMeal, removeCaloriesPlan, test
};


