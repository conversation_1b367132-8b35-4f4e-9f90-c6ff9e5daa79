/**
 * 微信群管理服务
 * 
 * 提供微信群相关功能:
 * - 群列表管理
 * - 群成员管理
 * - 群消息记录
 * - 群统计信息
 * - 群关键词设置
 * - 群AI内容分析
 * 
 * <AUTHOR> <<EMAIL>>
 * @date 2024
 */
import { ParseDB, ParseRef } from '../../database/database';
import { DoctorInfo } from '../../types/prisma';
import { EthAddress } from '../../types/types';
import ApiError from '../../utils/api_error';
import { filterToTypeWithKeys } from '../../utils/filter_type';
import { getLogger } from '../../utils/logger';

import { isEthAddress, isValidString } from '../../utils/utils';
import { addItchatGroupOfUser } from '../users/myWxGroupsFunctions';
import { GROUP_MEMBER_INFO } from '../users/types';
import { getDoctorInfoByAccount, getDoctorList } from '../users/userFunction';
import userServices from '../users/userServices';
import { batchUpdateItchatUsers, getItchatUserInfo } from './cowUserFunctions';
import { WxGroupInfo, WxGroupStatus, WxUserInfo } from './types';
// 微信群信息，除微信群基本信息外，还包括医生给微信群添加的附属信息
// 表中可能出现分数不同agent的相同微信群.
// 每个群都有所属的agent账号.因此，微信群信息的唯一性是由groupId和agent唯一确定
const TABLE_NAME = 'AIItchatGroups';
const logger = getLogger('itchatWXGp');
export const TABLE_NAME_WXGROUPS = TABLE_NAME;

const makeWxGroupRef = (objectId: string) => ({ __type: "Pointer" as const, className: 'AIItchatGroups' as const, objectId });

const getRecordJson = (record: ParseDB.Object, detail: boolean = false): WxGroupInfo =>
  record
    ? {
      objectId: record.id,
      agent: record.get('agent'),
      groupNickName: record.get('groupNickName'),
      groupUserName: record.get('groupUserName'),
      groupHeadImgUrl: record.get('groupHeadImgUrl'),
      City: record.get('City'),
      Province: record.get('Province'),
      owner_ObjectId: record.get('owner_ObjectId'),
      owner_NickName: record.get('owner_NickName'),
      owner_Account: record.get('owner_Account'),

      status: record.get('status'),
      weightLossPlan: record.get('weightLossPlan'),

      memberCount: record.get('memberCount'),
      description: record.get('description') || '',
      joinWelcomeInfo: record.get('joinWelcomeInfo') || '',
      aiKeywords: record.get('aiKeywords') || [],
      aiSwitch: record.get('aiSwitch') || '',
      switchs: record.get('switchs') || [],
      wxid: record.get('wxid'),
      createdAt: record.get('createdAt'),
      updatedAt: record.get('updatedAt'),
      ...(detail ? {
        MemberList: record.get('memberList'),
        doctors: record.get('doctors'),
      } : {})
    }
    : undefined;
const getItchatGroupRecord = async (agent: EthAddress, wxGroup: WxGroupInfo): Promise<ParseDB.Object> => {
  const { objectId, groupNickName, groupUserName, groupHeadImgUrl, wxid } = wxGroup;
  const name = groupNickName;
  const id = groupUserName;
  const avatar = groupHeadImgUrl;

  let record = undefined;
  if (isValidString(objectId)) {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('objectId', objectId);
    record = await query.first();
    if (record) {
      return record
    }
    logger.error(`通过objectId:${objectId}未找到微信群`)
  }
  if (isValidString(wxid)) {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('agent', agent);
    query.equalTo('groupUserName', wxid);
    query.ascending("createdAt")
    record = await query.first();
    if (record) {
      return record
    }
    logger.error(`通过wxid:${wxid}未找到微信群`)
  }
  if (!record && isValidString(id)) {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('agent', agent);
    query.equalTo('groupUserName', id);
    query.ascending("createdAt")
    record = await query.first();
    if (record) {
      return record
    }
    logger.error(`通过groupUserName:${id}未找到微信群`)
  }

  return record;
};
const setWxGroupHealthExpired = async (agent: EthAddress, groupObjectId: string) => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('agent', agent);
  query.equalTo('objectId', groupObjectId);
  const record = await query.first();
  if (record) {
    record.set('status', 'HEALTH_EXPIRED');
    await record.save();
    logger.warn(`设置微信群:${record.get('groupNickName')}健康打卡过期`)
    return record;
  }
  logger.error(`微信群:${groupObjectId}未找到`)
};
// 返回群成员中account有效的用户列表
const getAccountsOfGroup = async (agent: EthAddress, memberList: Array<any>): Promise<Array<string>> => {
  // 先获取所有群成员用户的详情信息
  const userssPromises = memberList.map((member: any) => getItchatUserInfo(agent, member, true));
  const allMembers = await Promise.all(userssPromises);
  // 判断群成员是否有account
  const regUsers = allMembers?.filter(item => item?.account);
  // 获取account列表
  return regUsers?.map(item => item.account);
};

const isGroupMember = async (agent: EthAddress, groupObjectId: string, account: EthAddress, wxid: string = '') => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo("objectId", groupObjectId);
  const result = await query.first();
  if (result) {
    const memberList = result.get("memberList");
    if (!memberList) return false;
    if (isValidString(wxid)) return memberList.some((item: { UserName: string; }) => item.UserName === wxid)
    const accounts = await getAccountsOfGroup(agent, memberList)
    return accounts.includes(account);
  }
  return undefined;
};
const isGroupOwner2 = async (agent: EthAddress, groupObjectId: string, account: EthAddress) => {
  if (!groupObjectId) return undefined;

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo("agent", agent);
  query.equalTo("objectId", groupObjectId);

  query.equalTo("owner_Account", account);
  const result = await query.first();
  if (result) return result.toJSON();

  // 另外一个表: AIItchatGroupsOfUser 也需要判断
  return isGroupOwner2(agent, groupObjectId, account);
};



// 通过搜索项获取微信群列表
const getGroupsBySearch = async (agent: EthAddress, pageNum: number, pageSize: number,
  search: {
    groupNameRegex?: string | RegExp;
    groupNickName?: string;
    memberCount?: number;
    owner_account?: string;
    owner_NickName?: string;
    owner_ObjectId?: string;
    keyword?: string;
    startTime?: number;
    endTime?: number;
    status?: Array<WxGroupStatus>
  }): Promise<{ results: Array<WxGroupInfo>, total: number }> => {
  let query = new ParseDB.Query(TABLE_NAME);

  if (isEthAddress(agent)) query.equalTo('agent', agent);
  query.addAscending('createdAt'); // 一直用升序,防止重复

  if (search?.groupNickName) query.equalTo('groupNickName', search.groupNickName);
  if (search?.groupNameRegex) {
    if (search.groupNameRegex instanceof RegExp) {
      query.matches('groupNickName', search.groupNameRegex);
    } else if (typeof search.groupNameRegex === 'string') {
      const regexps = search.groupNameRegex.split("\n");
      for (const regexpStr of regexps) {
        query.matches('groupNickName', new RegExp(regexpStr));
      }
    }
  }
  if (search?.memberCount) query.greaterThanOrEqualTo('memberCount', search.memberCount);

  if (search?.owner_account) {
    query.equalTo('doctors.account', search.owner_account);
    //  query.equalTo('owner_Account', search.owner_account);
  }
  if (search?.owner_NickName) query.equalTo('owner_NickName', search.owner_NickName);
  if (search?.owner_ObjectId) query.equalTo('owner_ObjectId', search.owner_ObjectId);

  if (search?.startTime) query.greaterThanOrEqualTo('createdAt', new Date(Number(search.startTime)));
  if (search?.endTime) query.lessThanOrEqualTo('createdAt', new Date(Number(search.endTime)));

  if (search?.keyword) query.contains('aiKeywords', search.keyword);

  if (search?.status) query.containedIn('status', search.status);

  const total = await query.count();

  query.limit(pageSize);
  const skipStep = (pageNum - 1) * pageSize
  if (total > skipStep && skipStep)
    query.skip(skipStep);

  const groups = await query.find();
  return { results: groups?.map((record) => getRecordJson(record)), total };
};

// 获取我加入的微信群
const getGroupsOfJoined = async (
  account: EthAddress, wxid: string,
  agent: EthAddress,
  pageNum: number,
  pageSize: number): Promise<{ results: Array<WxGroupInfo>, total: number }> => {
  const u = await getItchatUserInfo(agent, {
    account, wxid,
    UserName: wxid,
    NickName: '',
    HeadImgUrl: ''
  });
  if (!u) return { total: 0, results: [] };
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('memberList.UserName', u.UserName);
  query.equalTo('agent', agent);
  query.descending('updatedAt');

  const total: number = await query.count();
  query.limit(pageSize);
  const skipStep = (pageNum - 1) * pageSize
  if (total > skipStep && skipStep)
    query.skip(skipStep);


  const results = await query.find();

  return { total, results: results.map((record) => getRecordJson(record)) };
};
// 收集并更新itchat群信息,返回group的objectId
const addOrUpdateItchatGroup = async (
  agent: EthAddress,
  wxGroup: WxGroupInfo, newMembers?: WxUserInfo[]
): Promise<WxGroupInfo> => {
  try {
    const { groupNickName, groupUserName, groupHeadImgUrl, MemberList } = wxGroup;
    if (!isValidString(groupNickName) || !isValidString(groupUserName)) {
      logger.error('addOrUpdateItchatGroup: 群名或群UserName不能为空', wxGroup);
      return undefined;
    }

    const doctorsAll = await getDoctorList();

    let record = await getItchatGroupRecord(agent, wxGroup);
    if (!record) {
      logger.warn(`====>未找到群信息, 开始创建群: ${groupNickName}`);
      record = new ParseDB.Object(TABLE_NAME);
      record.set('agent', agent);
      record.set('groupUserName', groupUserName);
      record.set('status', 'NORMAL');
    } else {
      if (record.get('status') === undefined) {
        record.set('status', 'NORMAL');
      }
    }

    let memberList = MemberList || record.get('memberList') || [];
    if (newMembers) {
      // 如果已经存在,则不添加
      memberList = memberList.filter((item: any) => !newMembers.some((newItem: WxUserInfo) => newItem.UserName === item.UserName));
      memberList.push(...newMembers.map((item: WxUserInfo) => ({ objectId: item.objectId, RemarkName: item.RemarkName, NickName: item.NickName, UserName: item.UserName })));
    }

    record.set('groupNickName', groupNickName);
    record.set('groupHeadImgUrl', groupHeadImgUrl);
    record.set('RemarkName', wxGroup.RemarkName);

    if (memberList?.length) {
      record.set('memberList', memberList);
      record.set('memberCount', memberList.length);
    }

    // // 获取所有account有效的群成员（普通用户+医生）
    const doctors = doctorsAll?.filter((d: DoctorInfo) => memberList?.some((g: any) => (
      (d.wxid && d.wxid === g.UserName)
      || (d.name && d.name === g.NickName)
      || (d.name && d.nickName === g.NickName)
    )));
    // const accounts = await getAccountsOfGroup(agent,retWxGroup)
    // // 获取群成员中的医生
    // const doctors = await userServices.getDoctorsOfAccounts(accounts)

    record.set('doctors', doctors); // 设置群医生,动态变化

    const doctor = doctors?.[0];
    if (doctor) {
      logger.info(`servicesPostUpdateGroups,获取群主:${groupNickName} ${doctor?.name} ${doctor?.account}`);
      await addItchatGroupOfUser({ account: doctor.account, name: doctor.name, objectId: doctor.objectId }, {
        wxid: wxGroup.wxid,
        UserName: wxGroup.groupUserName,
        NickName: wxGroup.groupNickName,
        HeadImgUrl: wxGroup.groupHeadImgUrl,
        memberCount: wxGroup.MemberList?.length,
        account: doctor.account,
        agent,
        groupObjectId: record.id,
      }, "itchat");

      const ownerAccount = record.get('owner_Account');
      // 设置过群主医生的不能再设置
      if (!ownerAccount && isEthAddress(doctor.account)) {
        logger.warn(`====>设置群医生:${groupNickName}: ${doctor.name} ${doctor.account}`);
        record.set('owner_NickName', doctor.name);
        record.set('owner_Account', doctor.account);
        record.set('owner_ObjectId', doctor.objectId);
      }
    }

    logger.info(`添加或更新微信群:${groupNickName} members:${memberList?.length} `);

    const result = await record.save();

    return getRecordJson(result);
  } catch (e) {
    logger.error('addOrUpdateItchatGroup: 添加或更新微信群失败: ', agent, wxGroup);
    return undefined;
  }
};
const batchUpdateItchatGroups = async (agent: EthAddress, groups: WxGroupInfo & WxUserInfo[], source: string = "itchat") => {
  const results = await Promise.all(
    groups?.map(async (group: any) => {
      if (!group.NickName) {
        logger.error('更新微信群时:NickName not found', group.UserName);

        return undefined;
      }
      const { groupNickName, groupUserName, groupHeadImgUrl, NickName, UserName, HeadImgUrl } = group;

      const wxUserList = await batchUpdateItchatUsers(agent, group.MemberList || []);
      const inGroup: WxGroupInfo = {
        ...group,
        groupHeadImgUrl: groupHeadImgUrl || HeadImgUrl,
        groupNickName: groupNickName || NickName,
        groupUserName: groupUserName || UserName,
        MemberList: wxUserList?.map(item => ({ objectId: item.objectId, UserName: item.UserName, NickName: item.NickName }))
      }

      const retWxGroup = await addOrUpdateItchatGroup(agent, inGroup);
      logger.warn('基于群成员,批量添加或更新用户', NickName, group.MemberList?.length);
      return retWxGroup;
    }),
  );

  return results;
};
// 接受来自itchat的群信息更新,同时设置医生账号为拥有者账号 TODO: 多个医生时情况需要调整,群名可能重复
// 1 根据 NickName查找原有群信息,若不存在,则创建群
// 2 更新群信息
// 3 如果未设置owner信息,那么从"群成员"中找医生,选择第一个医生为owner
// 4 设置群的 doctors, 把群众所有用户列入进去
// 4 保存群信息
const updateGroups = async (body: {
  account: EthAddress; // 机器人账号
  NickName: string; // 机器人昵称
  groups: any; // 群信息
  source: string // wcferry,itchat ntchat
}) => {
  const agent = body.account;
  if (!isEthAddress(agent)) throw new ApiError('agent not found', 404);

  return batchUpdateItchatGroups(body.account, body.groups, body.source);
};
/**
 * 更新wx group 的简单信息,不需要额外操作的内容
 * @param agent 
 * @param groupObjectId 
 * @param group 
 * @returns 
 */
const updateWxGroupSimpleInfo = async ({ agent, groupObjectId, group }: { agent: EthAddress, groupObjectId: string, group: WxGroupInfo })
  : Promise<boolean> => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('objectId', groupObjectId);
  const record = await query.first();
  if (!record) return undefined;

  // 遍历WxGroupInfo的成员
  const groupKeys = Object.keys(group);
  const wxGroupInfoKeys: (keyof WxGroupInfo)[] = [
    'groupUserName', 'groupNickName', 'groupHeadImgUrl', 'status', 'owner_ObjectId', 'owner_NickName',
    'owner_Account', 'description', 'joinWelcomeInfo', 'aiKeywords', 'memberCount', 'aiSwitch', 'switchs',
    'weightLossPlan', 'wxid'
  ];
  const data = filterToTypeWithKeys(group, wxGroupInfoKeys);
  record.set(data)

  const result = await record.save()
  return !!result
};

// 查询用户的群，无需权限验证
const servicesGetWxGroupInfo = async ({ groupId, detail = false }: { groupId: string, detail: boolean }) => {
  if (!groupId) return null;

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('groupUserName', groupId);
  query.descending('updatedAt');
  const record = await query.first();
  return getRecordJson(record, detail);
};


const servicesGetWxGroupInfoBody = async (params: {
  groupId: string;// itchat group username
  groupObjectId: string;// group objectId
  detail: boolean;
}) => {
  const { groupId, groupObjectId } = params
  if (!groupId && !groupObjectId) {
    logger.error('[servicesGetWxGroupInfoBody]groupId and groupObjectId not found');
    throw new ApiError('groupId and groupObjectId not found', 404);
  }

  const query = new ParseDB.Query(TABLE_NAME);
  if (groupObjectId)
    query.equalTo('objectId', groupObjectId);
  if (groupId)
    query.equalTo('groupUserName', groupId);
  query.descending('updatedAt');
  const record = await query.first();
  return getRecordJson(record, params.detail);
};


// 获取微信群的医生，无权限验证
const getDoctorOfGroup = async ({
  objectId,
  groupId,
  groupName,
}: {
  objectId?: string;
  groupId?: string;
  groupName?: string;
}) => {
  let group;
  if (isValidString(objectId)) {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('objectId', objectId);
    group = await query.first();
  }
  if (!group && isValidString(groupId)) {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupUserName', groupId);
    group = await query.first();
  }
  if (!group && isValidString(groupName)) {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupNickName', groupName);
    group = await query.first();
  }

  if (!group) return null;

  const account = group.get('owner_Account');
  return getDoctorInfoByAccount(account);
};
const servicesGetDoctorOfGroup = getDoctorOfGroup;

const setWxGroupKeywords = async (objectId: string, keywords: string[]) => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('objectId', objectId);
  const record = await query.first();
  if (!record) return;

  const groupName = record.get('groupNickName');
  const hasKeywords = record.get('aiKeywords') || [];

  if (hasKeywords.length) return;
  if (!keywords?.length) return

  record.set('aiKeywords', keywords);
  logger.warn(`设置微信群关键词:${groupName} ${keywords}`);
  await record.save();
}
const getWxGroupInfoByObjectId = async (objectId: string): Promise<WxGroupInfo> => {
  if (!objectId) return null;

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('objectId', objectId);
  const record = await query.first();
  return getRecordJson(record)
};

const isExistWxGroup = async (agent: EthAddress, group: WxGroupInfo) => {
  return getItchatGroupRecord(agent, group)
}
const getItchatGroupInfo = async (agent: EthAddress, group: WxGroupInfo, detail = false): Promise<WxGroupInfo> =>
  getRecordJson(await getItchatGroupRecord(agent, group), detail)


const getWxGroupMembers = async ({ groupObjectId }: { groupObjectId: string }): Promise<GROUP_MEMBER_INFO[]> => {
  if (!groupObjectId) return [];

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('objectId', groupObjectId);

  const record = await query.first();
  const members = record?.get('memberList')
  return members
};


/**
 * 将微信群加入到指定医生名下
 */
const assignWxGroupsToDoctor = async ({ account, name, objectId, source }:
  { account: EthAddress, name: string, objectId: string, source: string }): Promise<any[]> => {
  const addStatus = []
  if (!isEthAddress(account)) return addStatus

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('doctors.account', account);
  const results = await query.find();


  for (const group of results) {
    const retWxGroup = await getRecordJson(group)
    const data = {
      wxid: retWxGroup.wxid || retWxGroup.groupUserName,
      UserName: retWxGroup.groupUserName,
      NickName: retWxGroup.groupNickName,
      memberCount: retWxGroup.memberCount,
      account: retWxGroup.owner_Account,
      agent: retWxGroup.agent,
      groupObjectId: retWxGroup.objectId,

    };
    const ret = await addItchatGroupOfUser({ account, name, objectId }, data, source);

    if (ret) addStatus.push(ret)
  }
  return addStatus
}


export {
  makeWxGroupRef, updateGroups,
  addOrUpdateItchatGroup, assignWxGroupsToDoctor, updateWxGroupSimpleInfo,
  getDoctorOfGroup, getGroupsBySearch, getGroupsOfJoined,
  getItchatGroupInfo, getItchatGroupRecord, isExistWxGroup, getRecordJson, getWxGroupInfoByObjectId,
  getWxGroupMembers, isGroupMember, isGroupOwner2, servicesGetDoctorOfGroup,
  servicesGetWxGroupInfo, servicesGetWxGroupInfoBody, setWxGroupHealthExpired, setWxGroupKeywords
};

