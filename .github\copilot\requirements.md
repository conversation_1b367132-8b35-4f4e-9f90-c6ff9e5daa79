# GitHub Copilot 项目要求

## 代码风格要求

1. 函数命名与定义规范
- 服务层类名使用 `Services` 后缀
- 服务层方法使用 `services` 前缀
- API 层函数使用动词(get/post/put/delete)开头
- 工具函数使用动词开头的驼峰命名
-4. Header 验证规范
- 认证信息必须通过请求头传递并在路由层验证
- 使用 `headerStringLengthValidator` 验证 header 信息
- 验证规则：
  1. `session-id`: 必须是24-100字符
  2. `iknow-agent`: 必须是24-100字符
- Header 验证失败时返回 400 错误
- 验证器使用规范：
  ```typescript
  // session-id 验证
  validators.headerStringLengthValidator("session-id", 24, false, 100)
  
  // iknow-agent 验证
  validators.headerStringLengthValidator("iknow-agent", 24, false, 100)
  ```
- 常见组合：
  1. 仅 session-id
  2. 仅 iknow-agent
  3. 特殊情况下两者都需要


- 类中的函数必须使用箭头函数形式定义，示例：
```typescript
class ExampleServices extends BaseServices {
  createOrUpdateItem = async ({ sessionId, item }: { sessionId: string; item: Item }): Promise<GResponse<Item>> => {
    try {
      const sess = await this.checkSession(sessionId);
      // implementation
    } catch (error) {
      logger.error(`Error in createOrUpdateItem: ${error}`);
      throw error;
    }
  }
}
```

2. 错误处理
- 控制器层必须通过 `controlFunc` 实现具体功能
- 服务层必须继承 `BaseServices` 类
- 必须使用 `checkSession` 进行权限验证
- 错误日志使用 `logger` 记录
- 所有方法必须验证必要参数
- 处理对象时必须进行空值检查
- 更新数据时应跳过 undefined 和 null 值
示例:
```typescript
createItem = async ({ sessionId, item }: { sessionId: string; item: Item }): Promise<GResponse<Item>> => {
  try {
    if (!item) {
      throw new Error('Item data is required');
    }
    
    // 只更新非空值
    for (const [key, value] of Object.entries(item)) {
      if (value !== undefined && value !== null) {
        itemObj.set(key, value);
      }
    }
  } catch (error) {
    logger.error(`Error in createItem: ${error}`);
    throw error;
  }
}
```
3. 类型定义
- 所有接口参数必须定义 TypeScript 类型
- 使用 type 定义数据模型
- 避免使用 any 类型

4. 注释规范
- 所有公共函数必须包含 JSDoc 注释
- 注释必须包含参数说明和返回值说明
- 复杂逻辑必须添加中文注释说明

## 项目结构要求

1. 文件组织
- services/ - 业务逻辑层，所有服务类必须继承 BaseServices
- controllers/ - 控制器层，使用类实现，方法通过 controlFunc 调用服务层
- types/ - 类型定义
- utils/ - 工具函数

2. 数据库操作
- 使用 Parse Server SDK
- 必须使用 `useMasterKey: true` 选项
- 查询必须考虑性能优化

3. 响应格式
所有接口必须使用统一的响应格式:
```typescript
// 单个资源响应
interface GResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// 列表资源响应
interface GResponseList<T> {
  success: boolean;
  data?: {
    list: T[];
    total: number;
    page: number;
    limit: number;
  };
  error?: string;
}
```

## API 设计要求

1. RESTful 规范
- GET - 获取资源
- POST - 创建资源
- PUT - 更新资源
- DELETE - 删除资源

2. 路由认证规范
- 所有需要认证的路由必须在路由定义时进行 header 验证
- 认证支持两种方式：`session-id` 或 `iknow-agent`
- 如果使用 `session-id`，必须添加 header 验证器
- 如果没有 `session-id`，则必须添加 `iknow-agent` 的 header 验证器
- 当路由包含 `session-id` 验证时，无需添加 `iknow-agent` 验证
- header 验证必须使用 `headerStringLengthValidator`

示例:
```typescript
// 使用 session-id 验证的路由
router.get("/survey/:account", 
  validators.headerStringLengthValidator("session-id", 24, false, 100),
  validators.paramAddressValidator("account"), 
  control.getUserHealthSurvey
);

// 使用 iknow-agent 验证的路由
router.post("/public/data", 
  validators.headerStringLengthValidator("iknow-agent", 24, false, 100),
  control.getPublicData
);

// 同时需要两种验证的特殊路由
router.get("/special/route", 
  validators.headerStringLengthValidator("session-id", 24, false, 100),
  validators.headerStringLengthValidator("iknow-agent", 24, false, 100),
  control.specialRoute
);
```

3. 控制器实现
- 必须使用类实现控制器
- 所有方法必须使用 `controlFunc` 调用服务层方法
- 从请求头获取并传递认证信息到服务层
示例:
```typescript
class HealthController {
  async getUserHealthSurvey(req: Request, res: Response): Promise<void> {
    controlFunc(req, res, healthSurveyServices.getSurvey, {
      sessionId: req.headers['session-id'],
      agent: req.headers['iknow-agent'],
      account: req.params.account
    });
  }
}
```

注意：
- Header 验证在路由层已完成，控制器只负责传递值
- 服务层的业务逻辑可以根据需要使用这些值
- 使用 `controlFunc` 统一处理响应格式

4. 权限验证
- 必须在请求头中包含 session-id
- 使用 checkSession 验证权限
- 无权限访问返回 401

## 测试要求

1. 单元测试框架
- 使用 Mocha 测试框架
- 所有测试文件存放在 `test/` 目录下
- 测试文件必须以 `.test.ts` 结尾
- 按模块分组组织测试文件

2. 测试规范
- 使用 `describe` 描述测试套件
- 使用 `it` 描述具体测试用例
- 必须包含成功和失败场景的测试
示例:
```typescript
describe('HealthSurveyServices', () => {
  describe('getSurvey', () => {
    it('should return survey when valid session and account', async () => {
      // test implementation
    });
    
    it('should throw error when invalid session', async () => {
      // test implementation
    });
  });
});
```

3. 测试覆盖率要求
- 业务逻辑必须有单元测试
- API 接口必须有集成测试
- 关键功能必须有端到端测试
- 总体覆盖率不低于 80%
