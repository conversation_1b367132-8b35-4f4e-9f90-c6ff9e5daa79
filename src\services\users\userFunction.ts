// groupx 独立的用户库,微信扫码登录或拥有account的用户才会出现在这个表(Users)中

import { enableEncryptedUser } from 'parse';
import { ParseDB, ParseObject } from '../../database/database';
import usersPlatformUserTB from '../../database/usersPlatformUsserTable';
import { PlatformName } from '../../types/platformReward';
import { DoctorInfo, PhoneInfo, User } from '../../types/prisma';
import { EthAddress, EthZero, PlatformType } from '../../types/types';
import ApiError from '../../utils/api_error';
import logger from '../../utils/logger';
import { isEthAddress, isNumber, isValidString } from '../../utils/utils';
import gateAdminApi from '../gateSSO/gateSSOAdminApi';
import { PlatformBody } from '../platformServices';
import { getSkipNumber } from '../services';
import { assignWxGroupsToDoctor } from '../wechat/cowGroupFunction';
import { WxUserInfo } from '../wechat/types';
import { getItchatUserInfo, getWxUserByNickNameJustOne, getWxUserByObjectId } from '../wechat/cowUserFunctions';


const TABLE_NAME = 'Users';
const getUserJsonSimple = (record: ParseDB.Object): User => record ? {
  objectId: record.id,
  id: record.get('id'),

  ssoID: record.get('ssoID'),
  account: record.get('account'),
  agent: record.get('agent') || EthZero, // 用于区分用户来自不同的系统和终端及渠道
  userObjectId: record.get('userObjectId'),
  wxid: record.get('wxid'),
  alias: record.get('alias'),
  balanceScore: record.get('banlanceScore') || 0,
  balanceCNY: record.get('balanceCNY') || 0,

  balanceAITokens: record.get('balanceAITokens') || 0,
  usedAITokens: record.get('usedAITokens') || 0,
  totalAITokens: record.get('totalAITokens') || 0,

  addresses: record.get('addresses'),
  avatar: record.get('avatar'),
  name: record.get('name'),
  nickName: record.get('nickName'),
  publicKey: record.get('publicKey'),
  cotaAddresses: record?.get('cotaAddresses'),

  gender: record.get('gender') || 'man',
  status: record.get('status') || '启用',// status: '启用' | '删除' ;
} : null;
const getUserJsonExt = (user: any) => user ? {
  phoneInfo: user.get('phoneInfo') || { "phoneNumber": "", "countryCode": "86", "phoneVerified": false },

  age: user.get('age'),
  era: user.get('era'),
  city: user.get('city'),
  district: user.get('district'),
  region: user.get('region'),
  country: user.get('country'),
  intro: user.get('intro'),
  skill: user.get('skill'),
  other: user.get('other'),
  department: user.get('department'),
  professionalPhoto: user.get('professionalPhoto'),
  professionalName: user.get('professionalName'),

  permissions: user.get('permissions') || [],
  roles: user.get('roles') || [],
  email: user.get('email'),
  phone: user.get('phone'),

} : null;
const getUserJson = async (record: ParseDB.Object, detail: boolean = false): Promise<User> => {
  if (!record || !record.id) return null; // hrow new Error("user not found");

  const userJson = getUserJsonSimple(record)
  if (detail) {
    const extrInfo = getUserJsonExt(record)

    const platformUsers = await usersPlatformUserTB.getPlatsByAccount(userJson.account);
    return {
      ...userJson,
      ...extrInfo,
      isSuperAdmin: record?.get('isSuperAdmin'),
      platformUsers,
      nickName: userJson.nickName || platformUsers?.[0]?.displayName
    };
  }
  return userJson;
};


const getDoctorJson = (user: any): DoctorInfo =>
  user
    ? {
      id: user.get('id'),
      objectId: user.id,
      ssoID: user.get('ssoID'),
      agent: user.get('agent') || EthZero, // 用于区分用户来自不同的系统和终端及渠道
      userObjectId: user.get('userObjectId'),
      wxid: user.get('wxid'),

      account: user.get('account'),
      addresses: user.get('addresses'),

      avatar: user.get('avatar'),
      name: user.get('name'),
      nickName: user.get('nickName'),

      publicKey: user.get('publicKey'),
      cotaAddresses: user?.get('cotaAddresses'),

      gender: user.get('gender'),
      age: user.get('age'),
      country: user.get('country'),
      intro: user.get('intro'),
      skill: user.get('skill'),
      other: user.get('other'),
      department: user.get('department'),
      professionalPhoto: user.get('professionalPhoto'),
      professionalName: user.get('professionalName'),

      balanceAITokens: user.get('balanceAITokens'),
      balanceCNY: user.get('balanceCNY'),
      balanceScore: user.get('balanceScore'),
    }
    : null;

// 没有nickName是可以的
const setUserObjectId = async (objectId: string, userObjectId: string, nickName: string, force = false) => {
  if (!objectId || !userObjectId) return null

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('objectId', objectId)
  const result = await query.first()
  if (!result) return null

  const wxUser = await getWxUserByObjectId(userObjectId)
  if (!wxUser) {
    logger.error(`ItchatUser表中没有${userObjectId}指定的记录`)
    return null
  }

  const userNickName = result.get('nickName')
  if (force
    || wxUser.NickName == userNickName // Users 和 ItchatUser 表的 nickname相同,直接覆盖
    || (nickName && (userNickName === nickName))) // Users昵称与输入昵称相同,则直接覆盖
  {
    logger.warn(result.get('userObjectId') ? '覆盖已经存在的userObjectId...' : '新设置userObjectId', userObjectId, nickName)
    result.set('userObjectId', userObjectId)
    return result.save()
  }

  return undefined
}

const updateUser = async (user: User) => {
  if (!user) return undefined
  const record = await getUser(user.account);
  if (!record) return undefined
  const UsersTB = ParseDB.Object.extend(TABLE_NAME);
  const userObj = new UsersTB();
  userObj.id = record.id;
  userObj.set('status', '启用')
  userObj.set('ssoID', user.ssoID);
  userObj.set('account', user.account);
  userObj.set('agent', user.agent || EthZero); // 用于区分用户来自不同的系统和终端及渠道
  userObj.set('addresses', user.addresses);
  userObj.set('avatar', user.avatar);
  userObj.set('name', user.name);
  userObj.set('nickName', user.nickName);
  userObj.set('roles', user.roles);
  userObj.set('publicKey', user.publicKey);
  userObj.set('cotaAddresses', user.cotaAddresses);
  userObj.set("platformUsers", user.platformUsers);
  userObj.set('phoneInfo', user.phoneInfo);
  userObj.set("wxid", user.wxid);
  userObj.set('openid', user.openid);
  userObj.set('refPlats', user.openid);
  // user.set(body); //会出现未授权写入
  return userObj.save();
}

// 创建用户
const createUser = async (inUser: User) => {
  if (!inUser) return undefined

  let isExist = false
  if (inUser.account && inUser.account != EthZero) {
    isExist = !!(await getUser(inUser.account))
  }
  if (!isExist && inUser.userObjectId) {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('userObjectId', inUser.userObjectId);
    isExist = !!(await query.first());
  }
  if (isExist) return undefined;

  // 用户确实不存在，需要创建
  const UsersTB = ParseDB.Object.extend(TABLE_NAME);
  const user = new UsersTB();

  user.set('status', '启用')
  user.set('ssoID', inUser.ssoID);
  user.set('account', inUser.account);
  user.set('agent', inUser.agent); // 用于区分用户来自不同的系统和终端及渠道
  user.set('addresses', inUser.addresses);
  user.set('avatar', inUser.avatar);
  user.set('name', inUser.name);
  user.set('nickName', inUser.nickName);
  user.set('roles', inUser.roles);
  user.set('publicKey', inUser.publicKey);
  user.set('cotaAddresses', inUser.cotaAddresses);

  user.set("platformUsers", inUser.platformUsers);
  user.set('phoneInfo', inUser.phoneInfo);

  user.set("wxid", inUser.wxid);
  user.set('openid', inUser.openid);
  user.set('refPlats', inUser.refPlats);
  // user.set(body); //会出现未授权写入
  return user.save();
}

// 通过 id 或者 addresses 取用户记录
const getUser = async (idOrAccount: string | number): Promise<ParseObject> => {
  if (isNumber(idOrAccount) && Number(idOrAccount) > 0) {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('id', idOrAccount);
    query.ascending('createdAt');
    return query.first();
  } else if (typeof idOrAccount === 'string') {
    const addressQuery = new ParseDB.Query(TABLE_NAME);
    addressQuery.containedIn('addresses', [idOrAccount]);

    const accountQuery = new ParseDB.Query(TABLE_NAME);
    accountQuery.equalTo('account', idOrAccount);

    const query = ParseDB.Query.or(addressQuery, accountQuery);
    query.ascending('createdAt');
    return query.first();
  } else {
    return undefined;
  }
};

// 通过 id 或者 addresses 取用户信息
const getUserInfo = async (idOrAccount: string | number, detail: boolean = false): Promise<User> => {
  const record = await getUser(idOrAccount);
  return getUserJson(record, detail);
};
// 通过真实姓名获取用户
const getUserInfoByProName = async (professionalName: string) => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('professionalName', professionalName);
  query.ascending('createdAt');
  return getUserJson(await query.first());
};

const getUserinfoByUserOID = async (userObjectId: string): Promise<User> => {
  if (!userObjectId) return undefined;

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('userObjectId', userObjectId);
  query.ascending('createdAt');
  const result = await query.first();
  return getUserJson(result, true)
};
// 小程序获取验证过的手机号
const getUserInfoByPhone = async (phone: PhoneInfo) => {
  if (!phone?.phoneNumber) return undefined;
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('phoneInfo.phoneNumber', phone.phoneNumber);
  query.equalTo('phoneInfo.countryCode', phone.countryCode);
  query.equalTo('phoneInfo.phoneVerified', phone.phoneVerified);
  query.ascending('createdAt');
  return getUserJson(await query.first());
};

const getDoctorInfoByProName = getUserInfoByProName;
const getDoctorInfoByAccount = async (account: EthAddress): Promise<DoctorInfo> => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('account', account);

  return getDoctorJson(await query.first());
};
// 返回群成员中的医生列表
const getDoctorsOfAccounts = async (accounts: Array<string>): Promise<Array<DoctorInfo>> => {
  if (accounts?.length) {
    // 获取添加医生信息
    const users = new ParseDB.Query(TABLE_NAME);
    users.equalTo('isDoctor', true);
    users.ascending('createdAt');
    users.containedIn('account', accounts);

    const doctorList = await users.find();
    return doctorList?.map(getDoctorJson);
  }

  return [];
};
const isDoctor = async (account: EthAddress): Promise<boolean> => {
  if (!account) return false;

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('account', account);
  query.equalTo('isDoctor', true); // 只有医生才有isDoct
  const user = await query.first();

  return !!user;
};

/**
 * 管理员也算进去
 * @param userObjectId 
 * @returns 
 */
const isDoctorByWxUserOId = async (userObjectId: string): Promise<boolean> => {
  if (!userObjectId) return false;

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('userObjectId', userObjectId);
  query.equalTo('isDoctor', true);
  const result = await query.first();
  return !!result;
};

const getUserPlatformInfo = async (account: EthAddress, platformName: PlatformName) =>
  usersPlatformUserTB.getPlatformInfoByAccount(account, platformName);

const getUserByOpenid = async (openid: string) => {
  if (!openid) return undefined;
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('openid', openid);
  query.ascending('createdAt');
  return query.first();
};
const getUserBySSO = async (ssoID: string) => {
  if (!ssoID) return undefined;
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('ssoID', ssoID);
  query.ascending('createdAt');
  return query.first();
};

const getPlatformUserId = async (account: EthAddress, platformName: PlatformName): Promise<string> =>
  usersPlatformUserTB
    .getPlatformInfoByAccount(account, platformName)
    .then(platformUser => platformUser?.platformUserId);



// 通过itchat用户信息(AIItchatUsers)获取用户账号
const getGroupxUserByItchat = async ({
  objectId,
  UserName,
  NickName,
  account,
}: WxUserInfo): Promise<User> => {
  let result = null;
  let query = null;
  if (objectId) {
    query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('objectId', objectId);
    result = await query.first();
  }

  if (!result && isEthAddress(account)) {
    query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('account', account);
    result = await query.first();
  }
  if (!result && UserName) {
    query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('wxUserName', UserName);
    result = await query.first();
  }
  if (!result && NickName) {
    query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('name', NickName);
    result = await query.first();
  }
  if (result) return getUserJson(result);
  return undefined;
};


// 通过SSO的权限设置,获取医生列表
const getDoctorRecordsBySSO = async () => {
  const perm = await gateAdminApi.getPermission(process.env.GATE_SSO_PERMISSION_DOCTOR);
  if (!perm || perm.length < 1) {
    logger.error('permission is error', 60002);
    return undefined;
  }

  const gateUsers = await Promise.all(perm.map((item: string) => gateAdminApi.getUserInfo(item)));
  const ssoIds = gateUsers.map((item: any) => item.id);
  // logger.info('从gate获取doctor ssoIds:', ssoIds);
  // logger.info('从gate获取doctor perm:', perm);

  const query = new ParseDB.Query(TABLE_NAME);
  query.containedIn('ssoID', ssoIds);
  return query.find();
};
// pageNum 从1开始
const getUserList = async (pageNum: number, pageSize: number) => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.notEqualTo('status', '删除');
  query.descending('createdAt')

  const total = await query.count();
  query.skip(getSkipNumber(total, pageNum, pageSize)).limit(pageSize);
  const results = await query.find();
  const list = await Promise.all(results?.map(record => getUserJson(record, true)));
  return { total, list };
};

// 无权限及sess检查,获取医生列表,医生权限由sso gate设置,doctor 在 user上扩展字段而来
const getDoctorList = async (): Promise<DoctorInfo[]> => {
  try {
    const records = await getDoctorRecordsBySSO();
    return records?.map(getDoctorJson);
  }
  catch (e) {
    logger.error('getDoctorList error:', e);
    return [];
  }
};
// gate sso oauth 登录成功并获得用户相关信息，将用户信息保存到用户表的 platformUsers
// platformName: GateSSO,Nervos
// author:akun.yunqi
// date:2023-01-13
const pushSSOGatePlatforminfo2UserDB = async (account: EthAddress, userinfo: PlatformBody) =>
  usersPlatformUserTB.insertOrUpdate({
    account,
    platformType: PlatformType.GATESSO,
    platformName: 'GATESSO',
    platformUserId: userinfo.platformUserId,
    platformUserData: userinfo.platformUserData || userinfo,

    username: userinfo.username,
    displayName: userinfo.platformUserData?.displayName || userinfo.username,
    avatar: userinfo.avatar,
  });
const updatePlatforminfo = async (account: EthAddress, platformUsers: Array<PlatformBody>) => {
  platformUsers?.forEach((platformUser: PlatformBody) => {
    pushSSOGatePlatforminfo2UserDB(account, platformUser);
  });
};
/**
 * 创建用户
 * 1. 如果用户已经存在，那只是更改信息即可
 * 3. 如果用户不存在，则创建用户
 * @param address 
 * @param body 
 * @returns 
 */

const isAdmin = (user: User): boolean => user?.roles?.includes('role_iknow_admin')


// 更新用户AITokens
const updateUserAITokens = async (account: EthAddress, consumeAmount: number)
  : Promise<boolean> => {
  try {
    if (consumeAmount < 0 || consumeAmount > 30012) throw new Error('consumeAmount is error');
    if (!account) return false;

    const user = await getUser(account);
    if (user?.id) {
      //user.set('balanceAITokens', data.balanceAITokens);
      //user.set('usedAITokens', data.usedAITokens);
      user.increment('balanceAITokens', -consumeAmount);
      user.increment('usedAITokens', consumeAmount);
      return !!(await user.save());
    }
  }
  catch (e) {
    logger.error('updateUserAITokens error:', e);
  }
  return false;
}

const pushNervosPlatforminfo2UserDB = async (account: EthAddress, userinfo: any) =>
  usersPlatformUserTB.insertOrUpdate({
    account,
    platformType: PlatformType.NERVOS,
    platformName: 'NERVOS',
    platformUserId: userinfo.sub,
    platformUserData: userinfo,
    username: userinfo.name,
    displayName: userinfo.data?.displayName || userinfo.name,
    avatar: userinfo.data?.avatar,
  });
// google oauth 登录成功并获得用户相关信息，将用户信息保存到用户表的 platformUsers
const pushGooglePlatforminfo2UserDB = async (account: EthAddress, userinfo: any) =>
  usersPlatformUserTB.insertOrUpdate({
    account,
    platformType: PlatformType.GOOGLE,
    platformName: 'GOOGLE',
    platformUserId: userinfo.email,
    platformUserData: userinfo,
    displayName: `${userinfo.family_name}.${userinfo.given_name}`,
    username: userinfo.name,
    avatar: userinfo.picture,
  });

const pushDiscordPlatforminfo2UserDB = async (account: EthAddress, userinfo: any) =>
  usersPlatformUserTB.insertOrUpdate({
    account,
    username: userinfo.username,

    displayName: userinfo.displayName,
    avatar: `https://cdn.discordapp.com/avatars/${userinfo.id}/${userinfo.avatar}`,

    platformType: PlatformType.DISCORD,
    platformName: 'DISCORD',

    platformUserData: userinfo,
    platformUserId: userinfo.id,
  });

const pushTelegramPlatforminfo2UserDB = async (account: EthAddress, userinfo: any) =>
  usersPlatformUserTB.insertOrUpdate({
    account,
    platformType: PlatformType.TELEGRAM,
    platformName: 'TELEGRAM',
    platformUserId: userinfo.id.toString(),
    platformUserData: userinfo,
    displayName: `${userinfo.first_name}.${userinfo.last_name}`,
    username: `${userinfo.first_name}.${userinfo.last_name}`,
    avatar: userinfo.photo_url,
  });

const pushTwitterPlatforminfo2UserDB = async (account: EthAddress, userinfo: any) =>
  usersPlatformUserTB.insertOrUpdate({
    account,
    platformType: PlatformType.TWITTER,
    platformName: 'TWITTER',
    platformUserId: userinfo.id,
    platformUserData: userinfo,
    displayName: userinfo.name,
    username: userinfo.username,
    avatar: userinfo.profile_image_url,
  });
const pushGithubPlatforminfo2UserDB = async (account: EthAddress, userinfo: any) =>
  usersPlatformUserTB.insertOrUpdate({
    account,
    platformType: PlatformType.GITHUB,
    platformName: 'GITHUB',
    platformUserId: userinfo.id?.toString(),
    platformUserData: userinfo,
    username: userinfo.login,
    displayName: userinfo.name,
    avatar: userinfo.avatar_url,
  });

const removePlatforminfoFromUserDB = async (account: EthAddress, platformName: PlatformName): Promise<ParseObject> =>
  usersPlatformUserTB.removeInfo(account, platformName);

const pushPlatformInfo2UserDB: { [key: string]: any } = {
  GATESSO: pushSSOGatePlatforminfo2UserDB,
  DISCORD: pushDiscordPlatforminfo2UserDB,
  GITHUB: pushGithubPlatforminfo2UserDB,
  TWITTER: pushTwitterPlatforminfo2UserDB,
  TELEGRAM: pushTelegramPlatforminfo2UserDB,
  NERVOS: pushNervosPlatforminfo2UserDB,
  GOOGLE: pushGooglePlatforminfo2UserDB,
};
// 支付AITokens，返回余额，会出现返回负值
const paymentAITokens = async (
  account: EthAddress,
  amount: number,
  paymentId: string,
  paymentSign: string,
): Promise<number> => {
  const user = await getUser(account);
  if (!isValidString(user?.id)) throw new ApiError('用户不存在');
  if (!isValidString(paymentId) || !isValidString(paymentSign)) throw new ApiError('支付信息不完整');

  const balanceAITokens = user?.get('balanceAITokens') || 0 - amount;
  user.set('balanceAITokens', balanceAITokens);
  await user.save();
  return balanceAITokens + amount;
};

// 刷新用户的isDoctor属性
const cronRefreshDoctors = async () => {
  try {
    // 通过sso权限列表获取医生列表
    const doctors = await getDoctorRecordsBySSO();
    if (!doctors) return;
    // 设置isDoctor标志
    for (const doctor of doctors) {
      doctor.set('isDoctor', true);
      const result = await doctor.save();

      const doctorInfo = {
        account: doctor.get('account'),
        name: doctor.get('name'),
        objectId: doctor.id
      }
      // 为医生添加拥有的微信群
      const results = await assignWxGroupsToDoctor({ ...doctorInfo, source: "cronRefreshDoctors" })
      logger.warn(`[医生]: ${doctorInfo.name} 新添加拥有的微信群: ${results.length}`);
    };
    // 设置 isDoctor 标志并返回account列表
    const doctorAccounts = doctors.map(result => result.get('account'));

    // 遍历所有已经设置isDoctor的用户
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('isDoctor', true);
    const results = await query.find();

    // 根据SSO最新的doctor列表，更新用户列表，去除isDoctor权限
    const disableAry: string[] = [];

    for (const user of results) {
      const userAcc = user.get('account');
      if (!doctorAccounts.includes(userAcc)) {
        user.set('isDoctor', false);
        logger.warn(`user ${user.get('name')} 不再拥有医生权限,设置isDoctor为false`);
        disableAry.push(userAcc);
        await user.save();
      }
    }

    if (disableAry.length > 0) logger.warn(`userServices: ${disableAry} 用户不再拥有医生权限`);
  } catch (err) {
    logger.error('cronRefreshDoctors err: ', err);
  }
};
export {
  cronRefreshDoctors, getDoctorInfoByAccount,
  createUser, updateUser, setUserObjectId,
  getDoctorInfoByProName, getDoctorJson, getDoctorList,
  getDoctorsOfAccounts,
  getGroupxUserByItchat, getPlatformUserId, getUser, getUserBySSO, getUserByOpenid,
  getUserInfo, getUserInfoByProName, getUserInfoByPhone, getUserinfoByUserOID,
  getUserJson, getUserList, getUserPlatformInfo,
  isDoctor, isDoctorByWxUserOId, paymentAITokens, pushPlatformInfo2UserDB,
  pushSSOGatePlatforminfo2UserDB, removePlatforminfoFromUserDB, updatePlatforminfo, updateUserAITokens
};

