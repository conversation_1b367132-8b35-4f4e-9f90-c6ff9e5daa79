import { ParseDB } from "../../database/database";
import { EthAddress } from "../../types/types";
import logger from "../../utils/logger";
import BaseWxNotificationServices, { NotificationType } from "./baseWxNotificationServices";

/**
 * 用户微信通知服务类
 * 
 * 功能：
 * 1. 处理普通用户消息的发送
 * 2. 提供周报发送状态的检查
 * 3. 直接使用微信群ID发送消息，不涉及群组管理
 * 
 * 说明：
 * - 该类用于发送普通用户级别的通知
 * - 不包含每日发送限制
 * - 不负责群组管理，直接使用群组ID
 * - 支持周报发送状态的跟踪
 */
export class UserWxNotificationServices extends BaseWxNotificationServices {
    // 发送用户通知消息
    async sendUserNotification(wxMsgChannel: string, wxids: Array<string>, msg: string, type: NotificationType, test: boolean = false): Promise<boolean> {
        if (!msg) {
            logger.error(`[UserWxNotificationServices] sendUserNotification failed: msg is empty`);
            return false;
        }
        if (type == 'group')
            return this.sendMsgToGroups(wxMsgChannel, wxids, msg, type, test);
        return this.sendMsgToUsers(wxMsgChannel, wxids, msg, type, test);
    }

    // 是否已发送过本周的周报
    async isWeeklyReportSent(agent: EthAddress, groupWxid: string, weekOfYear: number): Promise<boolean> {
        const query = new ParseDB.Query("NotificationWXGroupsPatients");
        query.equalTo('agent', agent);
        query.equalTo('groupWxid', groupWxid);
        query.equalTo('weekOfYear', weekOfYear);
        const results = await query.find();
        return results && results.length > 0;
    }
}

export default new UserWxNotificationServices(); 