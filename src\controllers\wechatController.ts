import { Request, Response } from 'express';
import inviteCodeServices from '../services/wechat/inviteCodeServices';
import itchatGroups from '../services/wechat/cowGroupClass';
import itchatServices from '../services/wechat/cowUserClass';
import { wechatServices as services } from '../services/wechat/wechatServices';
import wechatServices from '../services/wechat/wechatServices';
import { controlFunc } from './controller';

const getWxOpenId = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.getWxOpenId, req.query);
const getUserInfo = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.getUserInfo, req.body);

const getUserProfile = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, wechatServices.servicesGetUserProfile, req.body?.payload);
const getUserPhoneNumber = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, wechatServices.servicesGetUserPhoneNumber, req.body);
const getPaySignature = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.getPaySignature, req.body.payload);
const getItchatUserInfo = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, itchatServices.servicesGetItchatUserInfo, req.body?.payload);
const getItchatUserList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, itchatServices.servicesGetItchatUserList, {
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
    pageSize: Number(req.params.pageSize),
    pageNum: Number(req.params.pageNum),
    search: req.query
  });

const getItchatUserInfoByObjectId = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, itchatServices.servicesGetItchatUserInfo, {
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
    user: { objectId: req.params.objectId }
  });
const logout = async (req: Request, res: Response): Promise<void> => controlFunc(req, res, services.logout, req.body);

const postUpdateFriends = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, itchatServices.servicesPostUpdateFriends, req.body?.payload);

const postUpdateGroups = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, itchatGroups.servicesPostUpdateGroups, req.body?.payload);

const postWxNotice = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, itchatServices.postWxNotice, { toUser: req.params.toUser, body: req.body });
const postWxNoticeGroups = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, itchatServices.serviceWxNoticeGroups, {
    body: req.body, sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
  });
const getItchatGroupsAll = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, itchatGroups.servicesGetGroupsAll, {
    ...req.params,
    search: req.query,
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
  });
// 微信群邀请码
const getInviteCodeList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, inviteCodeServices.servicesGetInviteCodeList, {
    ...req.query,
    sessionId: req.headers['session-id'],
    groupObjectId: req.params.groupObjectId,
  });
const postGenerateInviteCode = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, inviteCodeServices.servicesGenerateInviteCode, {
    pageNum: Number(req.query.pageNum),
    pageSize: Number(req.query.pageSize),
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
    ...req.body.payload,
  });
const postUseInviteCode = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, inviteCodeServices.servicesUseInviteCode, {
    ...req.body.payload,
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
  });
const deleteInviteCode = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, inviteCodeServices.servicesDeleteInviteCode, {
    ...req.body.payload,
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
  });
const getInviteCodeAll = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, inviteCodeServices.servicesGetInviteCodeAll, {
    pageNum: Number(req.query.pageNum),
    pageSize: Number(req.query.pageSize),
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
  });
//-----------------------------------------
// // 获取加入群消息列表(通用)
// const getJoinGroupMsgList = async (req: Request, res: Response): Promise<void> =>
//   controlFunc(req, res, services.servicesGetJoinGroupMsgList, { account: req.params.account, body: req.body?.payload })
// // 设置加入群消息列表(通用)
// const postJoinGroupMsgList = async (req: Request, res: Response): Promise<void> =>
//   controlFunc(req, res, services.servicesSetJoinGroupMsgList, { account: req.params.account, body: req.body?.payload })


export default getWxOpenId;
export {
  getInviteCodeAll, getInviteCodeList, getItchatGroupsAll,
  getItchatUserInfo, getItchatUserInfoByObjectId, getItchatUserList,
  getPaySignature, getUserInfo,
  getUserProfile, getUserPhoneNumber, getWxOpenId,
  logout, postGenerateInviteCode, postUpdateFriends,
  postUpdateGroups, postUseInviteCode, postWxNotice, postWxNoticeGroups, deleteInviteCode
};

