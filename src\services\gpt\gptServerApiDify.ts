/**
 * 通过dify 编写一个gpt访问类
 * 支持文本生成和对话两种模式
 */

import axios from 'axios'
import logger from '../../utils/logger'
import GPTServerComm, { IGPTResponse } from './gptServerComm'

// Dify API 响应接口
interface DifyResponse {
    id: string
    message_id: string
    conversation_id?: string
    mode: string
    answer: string
    metadata: {
        usage: {
            prompt_tokens: number
            completion_tokens: number
            total_tokens: number
            prompt_unit_price?: string
            prompt_price_unit?: string
            prompt_price?: string
            completion_unit_price?: string
            completion_price_unit?: string
            completion_price?: string
            total_price?: string
            currency?: string
            latency?: number
        }
        retriever_resources?: Array<{
            position: number
            dataset_id: string
            dataset_name: string
            document_id: string
            document_name: string
            segment_id: string
            score: number
            content: string
        }>
    }
    created_at: number
}

class GPTServerApiDify extends GPTServerComm {

    /**
     * 发送对话消息 (chat-messages API)
     * @param userid 用户ID
     * @param prompt 用户输入
     * @param conversationId 会话ID，为空时创建新会话
     * @param inputs 输入变量
     * @returns GPT响应
     */
    async chatQuery<T>(
        userid: string,
        prompt: string,
        conversationId?: string,
        inputs: Record<string, any> = {}
    ): Promise<IGPTResponse<T>> {
        const apiKey = process.env.DIFY_API_KEY
        const url = process.env.DIFY_CHAT_URL

        if (!apiKey || !url) {
            logger.error("Dify API配置缺失: DIFY_API_KEY 或 DIFY_CHAT_URL 未设置")
            return null
        }

        const requestData = {
            inputs,
            query: prompt,
            response_mode: "blocking", // 使用阻塞模式
            user: userid,
            ...(conversationId && { conversation_id: conversationId })
        }

        const options = {
            method: "POST",
            url,
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${apiKey}`,
            },
            data: requestData,
        }

        try {
            logger.info(`发送Dify对话请求: ${userid}, conversationId: ${conversationId}`)
            const response = await axios.request(options)

            if (response.status === 200) {
                const data: DifyResponse = response.data

                return {
                    model: "dify-chat",
                    showText: data.answer,
                    aiTokens: data.metadata?.usage?.total_tokens || 0,
                    completionTokens: data.metadata?.usage?.completion_tokens || 0,
                    promptTokens: data.metadata?.usage?.prompt_tokens || 0,
                    json: this.parseJson(data.answer) as T
                }
            } else {
                logger.error(`Dify API 返回错误状态: ${response.status}`, response.data)
            }
        } catch (error: any) {
            logger.error("Dify 对话API 请求失败:", error?.response?.data || error.message)
        }
        return null
    }

    /**
     * 发送文本生成消息 (completion-messages API)
     * @param userid 用户ID
     * @param prompt 用户输入
     * @param inputs 输入变量
     * @returns GPT响应
     */
    async completionQuery<T>(
        userid: string,
        prompt: string,
        inputs: Record<string, any> = {}
    ): Promise<IGPTResponse<T>> {
        const apiKey = process.env.DIFI_API_KEY
        const completionUrl = process.env.DIFI_API_URL

        if (!apiKey || !completionUrl) {
            logger.error("Dify API配置缺失: DIFY_API_KEY 或 DIFY_COMPLETION_URL 未设置")
            return null
        }

        // 将prompt作为inputs的一部分传递
        const requestInputs = {
            ...inputs,
            query: prompt
        }

        const requestData = {
            inputs: requestInputs,
            response_mode: "blocking", // 使用阻塞模式
            user: userid
        }

        const options = {
            method: "POST",
            url: completionUrl,
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${apiKey}`,
            },
            data: requestData,
        }

        try {
            logger.info(`发送Dify文本生成请求: ${userid}`)
            const response = await axios.request(options)

            if (response.status === 200) {
                const data: DifyResponse = response.data

                return {
                    model: "dify-completion",
                    showText: data.answer,
                    aiTokens: data.metadata?.usage?.total_tokens || 0,
                    completionTokens: data.metadata?.usage?.completion_tokens || 0,
                    promptTokens: data.metadata?.usage?.prompt_tokens || 0,
                    json: this.parseJson(data.answer) as T
                }
            } else {
                logger.error(`Dify API 返回错误状态: ${response.status}`, response.data)
            }
        } catch (error: any) {
            logger.error("Dify 文本生成API 请求失败:", error?.response?.data || error.message)
        }
        return null
    }

    /**
     * 兼容原有的difyQuery方法，默认使用对话模式
     * @param userid 用户ID
     * @param prompt 用户输入
     * @returns GPT响应
     */
    async difyQuery<T>(userid: string, prompt: string): Promise<IGPTResponse<T>> {
        // 优先使用对话模式，如果失败则尝试文本生成模式
        const chatResult = await this.chatQuery<T>(userid, prompt)
        if (chatResult) {
            return chatResult
        }

        // 如果对话模式失败，尝试文本生成模式
        logger.warn("对话模式失败，尝试文本生成模式")
        return await this.completionQuery<T>(userid, prompt)
    }
}

export default new GPTServerApiDify()


