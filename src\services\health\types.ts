import { IFood, IMealType } from "../../types/health"
import { EthAddress } from "../../types/types"

type TasksCommon = 'HAS_IGNORE' | 'HAS_KEYWORD' | 'NOT_WEIGHT_LOSS_GROUP' | 'DELETED' | 'NORMAL' | 'UNKNOWN_ERR'
type TasksCaloriesCheck = 'NOT_FOOD_CALORIE_CHECKIN' | 'EXTRACT_CALORIES_ERR' | 'EXTRACT_CALORIES_SUCESS' | 'IS_CALORIES_DUPLICATE'
type TasksWeightLossCheck = 'NOT_WEIGHT_LOSS_CHECKIN' | 'EXTRACT_WEIGHT_LOSS_ERR' | 'EXTRACT_WEIGHT_LOSS_SUCESS' | 'IS_DUPLICATE' | 'NOT_WEIGHT_LOSS_GROUP'
// 任务处理结果
type NotAtMsgTasks = TasksCommon | TasksWeightLossCheck | TasksCaloriesCheck | 'WEIGHT_LOSS_CMD'

type NotAtMsgTasksAiResponse<T = unknown> = {
    objectId: string;
    account: EthAddress;
    balanceAITokens: number;
    hash: string;
    showText: string; // 返回给用户的说明文本
    showText2?: string; // 返回给用户的说明文本2
    doneTasks: NotAtMsgTasks[]; // 已经完成的任务
    json?: T; // ai生成的json数据    
    error?: string;
}
type HealthItem = {
    name: string,
    value: number,
    unit: string
}
type HealthRecordStatistics = {
    userObjectId: string,
    userName: string,
    groupObjectId: string,
    groupName: string,
    totalRecords: number,
    weight: { weightStart: number, weightEnd: number, unit: string },
    water: { totalWaterIntake: number, countWater: number, unit: string },
    sleep: { totalSleepTime: number, countSleep: number, unit: string },
    aerobic: { totalAerobicExercise: number, countAerobic: number, unit: string },
    anaerobic: { totalAnaerobicExercise: number, countAnaerobic: number, unit: string },
    flexibility: { totalFlexibilityExercise: number, countFlexibility: number, unit: string }
}
//分析得出的食物列表
type IAnalyzedFood = { mealType: IMealType, foods: IFood[] }
export type {
    TasksCommon, TasksCaloriesCheck, TasksWeightLossCheck,
    NotAtMsgTasks,
    NotAtMsgTasksAiResponse,
    HealthRecordStatistics, HealthItem,
    IAnalyzedFood
}