import mocha from "mocha";
const { describe } = mocha;
const { it } = mocha;
const { assert } = require("chai");

import initParseServer from "../../src/database/database";
import { makeUserStatisticsHome } from "../../src/services/chatlist/userWxMsgStatistics";
import { EthAddress } from "../../src/types/types";
import moment from "moment";
import { getStats, getStatsByTimeRange, getSystemAlertList } from "../../src/services/systemInfoServices";
import { getUserInfo } from "../../src/services/users/userFunction";

// **************
// initParseServer({
//     appId: "nftweb2",
//     serverURL: "http://**************:1337/parse",
//     javascriptKey: "123456aaa",
//     masterKey: "123456aaa"
// });
// const agent = "******************************************";
// const agent1 = "******************************************";

// **************用221导出的徐子军真实数据库
initParseServer({
    appId: "nftweb2",
    serverURL: "http://**************:1339/parse",
    javascriptKey: "123456aaa",
    masterKey: "123456aaa"
});
const agent = "******************************************";
const agent1 = "******************************************";
const account_xuzijun = "******************************************";
const account_akun = "******************************************";




// // **************
// initParseServer();
// const agent1 = "******************************************"; // 徐子军
// const agent = "******************************************"; // Akun


describe("SystemInfo test getSystemAlertList", () => {

    it("测试基本功能getSystemAlertList", done => {
        try {
            const startStamp = moment("2024-01-01").valueOf();
            const endStamp = moment("2024-12-31").valueOf();
            getSystemAlertList({ agent: agent, pageNum: 1, pageSize: 10, isReadObjectIds: [], allReadTimestamp: startStamp.toString() }).then(res => {
                console.log("返回系统报警信息列表记录数:", res.results.length);
                assert.isNotEmpty(res.results);
                assert.isArray(res.results);
                assert.isAtLeast(res.results.length, 1);
                done();
            });
        } catch (error) {
            done(error);
        }
    });

    it("测试getSystemAlertList传入无效的agent", done => {
        try {
            const startStamp = moment("2024-01-01").valueOf();
            const endStamp = moment("2024-12-31").valueOf();
            getSystemAlertList({ agent: "******************************************", pageNum: 1, pageSize: 10, isReadObjectIds: [], allReadTimestamp: startStamp.toString() }).then(res => {
                console.log("返回系统报警信息列表记录数:", res.results.length);
                assert.isEmpty(res.results);
                done();
            });
        } catch (error) {
            done(error);
        }
    });
    it("测试getSystemAlertList传入无效的start", done => {
        try {
            const endStamp = moment().add(1, 'day').valueOf();
            getSystemAlertList({ agent: agent, pageNum: 1, pageSize: 10, isReadObjectIds: [], allReadTimestamp: "0" }).then(res => {
                console.log("返回系统报警信息列表记录数:", res.results.length);
                // 验证返回的数据是否在有效的时间范围内
                if (res.results.length > 0) {
                    res.results.forEach(alert => {
                        const alertDate = moment(alert.createdAt);
                        assert.isTrue(alertDate.isValid(), "日期应该是有效的");
                        assert.isTrue(alertDate.valueOf() > 0, "日期应该大于0");
                        assert.isTrue(alertDate.valueOf() <= endStamp, "日期应该小于等于结束时间");
                    });
                }
                done();
            }).catch(error => {
                done(error);
            });
        } catch (error) {
            done(error);
        }
    });

    it("测试getStatsByTimeRange", done => {
        const startStamp = moment("2024-10-01").valueOf();
        const endStamp = moment("2024-12-31").valueOf();
        getStatsByTimeRange({ agent: agent, params: { start: startStamp, end: endStamp } }).then(res => {
            console.log("返回用户增长曲线数据:", res.length);
            console.log(res);
            assert.isNotEmpty(res);
            assert.isArray(res);
            assert.isAtLeast(res.length, 1);
            const firstItem = res[0];
            assert.property(firstItem, 'date');
            assert.property(firstItem, 'totalUsers');
            assert.property(firstItem, 'activeUsers');
            assert.match(firstItem.date, /^\d{4}-\d{2}-\d{2}$/);
            assert.isNumber(firstItem.totalUsers);
            assert.isNumber(firstItem.activeUsers);
            done();
        });
    });

    it("测试getStatsByTimeRange 使用其他agent ", done => {
        const startStamp = moment().subtract(7, 'days').valueOf();
        const endStamp = moment().valueOf();
        getStatsByTimeRange({ agent: "******************************************", params: { start: startStamp, end: endStamp } }).then(res => {
            console.log("返回用户增长曲线数据:", res.length);
            console.log(res);
            assert.isArray(res);
            done();
        }).catch(error => {
            done(error);
        });
    });

    it("测试getStatsByTimeRange 无效的时间范围", done => {
        const startStamp = 0;
        const endStamp = moment("2024-12-31").valueOf();
        getStatsByTimeRange({ agent: agent, params: { start: startStamp, end: endStamp } }).then(res => {
            assert.isArray(res);
            assert.equal(res.length, 8);
            done();
        });
    });

    it("测试getStatsByTimeRange 开始时间晚于结束时间", done => {
        const startStamp = moment("2024-12-31").valueOf();
        const endStamp = moment("2024-10-01").valueOf();
        getStatsByTimeRange({ agent: agent, params: { start: startStamp, end: endStamp } }).then(res => {
            assert.isArray(res);
            assert.equal(res.length, 0);
            done();
        });
    });

    it("测试getStatsByTimeRange 相同的开始和结束时间", done => {
        const sameDate = moment("2024-10-01").valueOf();
        getStatsByTimeRange({ agent: agent, params: { start: sameDate, end: sameDate } }).then(res => {
            assert.isArray(res);
            assert.equal(res.length, 1);
            const item = res[0];
            assert.property(item, 'date');
            assert.property(item, 'totalUsers');
            assert.property(item, 'activeUsers');
            done();
        });
    });

    it("测试getStatsByTimeRange 数据连续性", done => {
        const startStamp = moment("2024-10-01").valueOf();
        const endStamp = moment("2024-10-10").valueOf();
        getStatsByTimeRange({ agent: agent, params: { start: startStamp, end: endStamp } }).then(res => {
            assert.isArray(res);
            for (let i = 1; i < res.length; i++) {
                const prevDate = moment(res[i - 1].date);
                const currDate = moment(res[i].date);
                const dayDiff = currDate.diff(prevDate, 'days');
                assert.equal(dayDiff, 1, '日期应该是连续的');
            }
            for (let i = 1; i < res.length; i++) {
                assert.isAtLeast(res[i].totalUsers, res[i - 1].totalUsers, 'totalUsers应该是非递减的');
                assert.isAtLeast(res[i].activeUsers, 0, 'activeUsers不应该为负数');
                assert.isAtMost(res[i].activeUsers, res[i].totalUsers, 'activeUsers不应该超过totalUsers');
            }
            done();
        });
    });

    it("测试getStatsByTimeRange 缓存功能", done => {
        const startStamp = moment("2024-10-01").valueOf();
        const endStamp = moment("2024-10-10").valueOf();
        getStatsByTimeRange({ agent: agent, params: { start: startStamp, end: endStamp } }).then(firstRes => {
            getStatsByTimeRange({ agent: agent, params: { start: startStamp, end: endStamp } }).then(secondRes => {
                assert.deepEqual(firstRes, secondRes);
                done();
            });
        });
    });

    it("测试getStats", done => {
        getStats({ agent: agent, body: {} }).then(res => {
            console.log("返回系统统计信息:", res);
            assert.isNotEmpty(res);
            done();
        });
    });
});


// 注释掉使用 userServicesOld 的测试
describe("SystemInfo 测试用户首页统计信息(akun)", () => {
    let user: any = { userObjectId: 'test', account: 'test', name: 'test' };

    before(done => {
        getUserInfo(account_akun).then(userInfo => {
            user = userInfo;
            console.log("用户信息:", user.nickName);
            assert.equal(user.name, "Akun~~~");
            done();
        });
    });

    it("测试 makeUserStatisticsHome 用户首页统计信息 - 基本功能(使用缓存)", done => {
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [0, 0],
            isCache: true
        }).then(res => {
            console.log("返回用户统计信息:", res);
            assert.isNotEmpty(res);
            assert.property(res, 'totalMyWxGroups');
            assert.property(res, 'totalMsgCountMyGroups');
            assert.property(res, 'totalWeightLossCheckInMyGroups');
            assert.property(res, 'totalCaloriesCheckInMyGroups');
            assert.property(res, 'totalRevisittToday');
            assert.property(res, 'totalSpurCheckInToday');
            assert.isNumber(res.totalMyWxGroups);
            assert.isNumber(res.totalMsgCountMyGroups);
            assert.isNumber(res.totalWeightLossCheckInMyGroups);
            assert.isNumber(res.totalCaloriesCheckInMyGroups);
            assert.isNumber(res.totalRevisittToday);
            assert.isNumber(res.totalSpurCheckInToday);
            done();
        });
    });

    it("测试 makeUserStatisticsHome 用户首页统计信息 - 基本功能(不使用缓存)", done => {
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [0, 0],
            isCache: false
        }).then(res => {
            assert.isNotEmpty(res);
            assert.property(res, 'totalMyWxGroups');
            assert.property(res, 'totalMsgCountMyGroups');
            assert.property(res, 'totalWeightLossCheckInMyGroups');
            assert.property(res, 'totalCaloriesCheckInMyGroups');
            assert.property(res, 'totalRevisittToday');
            assert.property(res, 'totalSpurCheckInToday');
            assert.isNumber(res.totalMyWxGroups);
            assert.isNumber(res.totalMsgCountMyGroups);
            assert.isNumber(res.totalWeightLossCheckInMyGroups);
            assert.isNumber(res.totalCaloriesCheckInMyGroups);
            assert.isNumber(res.totalRevisittToday);
            assert.isNumber(res.totalSpurCheckInToday);
            done();
        });
    });

    it("测试 makeUserStatisticsHome - 无效的 agent 地址", done => {
        makeUserStatisticsHome({
            agent: "******************************************" as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [0, 0],
            isCache: true
        }).then(res => {
            assert.isObject(res);
            assert.equal(res.totalMyWxGroups, 0);
            assert.equal(res.totalMsgCountMyGroups, 0);
            assert.equal(res.totalWeightLossCheckInMyGroups, 0);
            assert.equal(res.totalCaloriesCheckInMyGroups, 0);
            assert.equal(res.totalRevisittToday, 0);
            assert.equal(res.totalSpurCheckInToday, 0);
            done();
        });
    });

    it("测试 makeUserStatisticsHome - 无效的用户ID", done => {
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: "invalid_user_id",
            account: "******************************************" as EthAddress,
            range: [0, 0],
            isCache: true
        }).then(res => {
            assert.isObject(res);
            assert.equal(res.totalMyWxGroups, 0);
            assert.equal(res.totalMsgCountMyGroups, 0);
            assert.equal(res.totalWeightLossCheckInMyGroups, 0);
            assert.equal(res.totalCaloriesCheckInMyGroups, 0);
            assert.equal(res.totalRevisittToday, 0);
            assert.equal(res.totalSpurCheckInToday, 0);
            done();
        });
    });

    it("测试 makeUserStatisticsHome - 指定时间范围", done => {
        const now = Date.now();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [oneDayAgo, now],
            isCache: true
        }).then(res => {
            assert.isObject(res);
            assert.isNumber(res.totalMyWxGroups);
            assert.isNumber(res.totalMsgCountMyGroups);
            assert.isNumber(res.totalWeightLossCheckInMyGroups);
            assert.isNumber(res.totalCaloriesCheckInMyGroups);
            assert.isNumber(res.totalRevisittToday);
            assert.isNumber(res.totalSpurCheckInToday);
            assert.isAtLeast(res.totalMyWxGroups, 0);
            assert.isAtLeast(res.totalMsgCountMyGroups, 0);
            assert.isAtLeast(res.totalWeightLossCheckInMyGroups, 0);
            assert.isAtLeast(res.totalCaloriesCheckInMyGroups, 0);
            assert.isAtLeast(res.totalRevisittToday, 0);
            assert.isAtLeast(res.totalSpurCheckInToday, 0);
            done();
        });
    });

    it("测试 makeUserStatisticsHome - 缓存功能比较", done => {
        const now = Date.now();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;
        // 先使用缓存获取数据
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [oneDayAgo, now],
            isCache: true
        }).then(cachedRes => {
            // 不使用缓存获取数据
            makeUserStatisticsHome({
                agent: agent as EthAddress,
                wxUserObjectId: user.userObjectId,
                account: user.account,
                range: [oneDayAgo, now],
                isCache: false
            }).then(freshRes => {
                // 比较两次结果
                assert.deepEqual(cachedRes, freshRes, "缓存数据与实时数据应该相同");
                done();
            });
        });
    });
});

// 注释掉使用 userServicesOld 的测试
describe("SystemInfo 测试用户首页统计信息(徐子军)", () => {
    let user: any = { userObjectId: 'test', account: account_xuzijun, name: 'nnn1', nickName: 'nnn1' };

    before(done => {
        // userServicesOld.getUserInfo(account_xuzijun).then(userInfo => {
        //     try {
        //         user = userInfo;
        //         assert.isNotEmpty(user);
        //         console.log("用户信息:", user);
        //         assert.equal(user.nickName, "nnn1");
        //         assert.equal(user.name, "nnn1");
        //         done();
        //     } catch (error) {
        //         done(error);
        //     }
        // }).catch(error => {
        //     done(error);
        // });
        done();
    });

    it("测试 makeUserStatisticsHome 用户首页统计信息 - 基本功能(使用缓存)", done => {
        makeUserStatisticsHome({
            agent: agent1 as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [0, 0],
            isCache: true
        }).then(res => {
            try {
                console.log("返回用户统计信息:", res);
                assert.isNotEmpty(res);
                assert.property(res, 'totalMyWxGroups');
                assert.property(res, 'totalMsgCountMyGroups');
                assert.property(res, 'totalWeightLossCheckInMyGroups');
                assert.property(res, 'totalCaloriesCheckInMyGroups');
                assert.property(res, 'totalRevisittToday');
                assert.property(res, 'totalSpurCheckInToday');
                assert.isNumber(res.totalMyWxGroups);
                assert.isNumber(res.totalMsgCountMyGroups);
                assert.isNumber(res.totalWeightLossCheckInMyGroups);
                assert.isNumber(res.totalCaloriesCheckInMyGroups);
                assert.isNumber(res.totalRevisittToday);
                assert.isNumber(res.totalSpurCheckInToday);
                done();
            } catch (error) {
                done(error);
            }
        }).catch(error => {
            done(error);
        });
    });

    it("测试 makeUserStatisticsHome 用户首页统计信息 - 基本功能(不使用缓存)", done => {
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [0, 0],
            isCache: false
        }).then(res => {
            try {
                console.log("不使用缓存用户统计信息:", res);
                assert.isNotEmpty(res);
                assert.property(res, 'totalMyWxGroups');
                assert.property(res, 'totalMsgCountMyGroups');
                assert.property(res, 'totalWeightLossCheckInMyGroups');
                assert.property(res, 'totalCaloriesCheckInMyGroups');
                assert.property(res, 'totalRevisittToday');
                assert.property(res, 'totalSpurCheckInToday');
                assert.isNumber(res.totalMyWxGroups);
                assert.isNumber(res.totalMsgCountMyGroups);
                assert.isNumber(res.totalWeightLossCheckInMyGroups);
                assert.isNumber(res.totalCaloriesCheckInMyGroups);
                assert.isNumber(res.totalRevisittToday);
                assert.isNumber(res.totalSpurCheckInToday);
                done();
            } catch (error) {
                done(error);
            }
        }).catch(error => {
            done(error);
        });
    });

    it("测试 makeUserStatisticsHome - 无效的 agent 地址", done => {
        makeUserStatisticsHome({
            agent: "******************************************" as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [0, 0],
            isCache: true
        }).then(res => {
            try {
                assert.isObject(res);
                assert.equal(res.totalMyWxGroups, 0);
                assert.equal(res.totalMsgCountMyGroups, 0);
                assert.equal(res.totalWeightLossCheckInMyGroups, 0);
                assert.equal(res.totalCaloriesCheckInMyGroups, 0);
                assert.equal(res.totalRevisittToday, 0);
                assert.equal(res.totalSpurCheckInToday, 0);
                done();
            } catch (error) {
                done(error);
            }
        }).catch(error => {
            done(error);
        });
    });

    it("测试 makeUserStatisticsHome - 无效的用户ID", done => {
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: "invalid_user_id",
            account: "******************************************" as EthAddress,
            range: [0, 0],
            isCache: true
        }).then(res => {
            try {
                assert.isObject(res);
                assert.equal(res.totalMyWxGroups, 0);
                assert.equal(res.totalMsgCountMyGroups, 0);
                assert.equal(res.totalWeightLossCheckInMyGroups, 0);
                assert.equal(res.totalCaloriesCheckInMyGroups, 0);
                assert.equal(res.totalRevisittToday, 0);
                assert.equal(res.totalSpurCheckInToday, 0);
                done();
            } catch (error) {
                done(error);
            }
        }).catch(error => {
            done(error);
        });
    });

    it("测试 makeUserStatisticsHome - 指定时间范围", done => {
        const now = Date.now();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [oneDayAgo, now],
            isCache: true
        }).then(res => {
            try {
                assert.isObject(res);
                assert.isNumber(res.totalMyWxGroups);
                assert.isNumber(res.totalMsgCountMyGroups);
                assert.isNumber(res.totalWeightLossCheckInMyGroups);
                assert.isNumber(res.totalCaloriesCheckInMyGroups);
                assert.isNumber(res.totalRevisittToday);
                assert.isNumber(res.totalSpurCheckInToday);
                assert.isAtLeast(res.totalMyWxGroups, 0);
                assert.isAtLeast(res.totalMsgCountMyGroups, 0);
                assert.isAtLeast(res.totalWeightLossCheckInMyGroups, 0);
                assert.isAtLeast(res.totalCaloriesCheckInMyGroups, 0);
                assert.isAtLeast(res.totalRevisittToday, 0);
                assert.isAtLeast(res.totalSpurCheckInToday, 0);
                done();
            } catch (error) {
                done(error);
            }
        }).catch(error => {
            done(error);
        });
    });

    it("测试 makeUserStatisticsHome - 缓存功能比较", done => {
        const now = Date.now();
        const oneDayAgo = now - 24 * 60 * 60 * 1000;
        // 先使用缓存获取数据
        makeUserStatisticsHome({
            agent: agent as EthAddress,
            wxUserObjectId: user.userObjectId,
            account: user.account,
            range: [oneDayAgo, now],
            isCache: true
        }).then(cachedRes => {
            // 不使用缓存获取数据
            makeUserStatisticsHome({
                agent: agent as EthAddress,
                wxUserObjectId: user.userObjectId,
                account: user.account,
                range: [oneDayAgo, now],
                isCache: false
            }).then(freshRes => {
                try {
                    // 比较两次结果
                    assert.deepEqual(cachedRes, freshRes, "缓存数据与实时数据应该相同");
                    done();
                } catch (error) {
                    done(error);
                }
            }).catch(error => {
                done(error);
            });
        }).catch(error => {
            done(error);
        });
    });
});


