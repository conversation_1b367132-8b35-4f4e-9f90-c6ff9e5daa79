import { gateSSOConfig } from '../../config';
import { fetcherGate } from '../../utils/fetcher';
import logger from '../../utils/logger';

type GateToken = {
  access_token: string;
  expires_in: number; // token有效期，以分钟为单位。一般为10080分钟，等于 7 天（7 * 24 * 60 分钟）
  id_token: string;
  refresh_token: string;
  scope: string;
  token_type: string;
};

class GateSSOBaseApi {
  token: GateToken = null;

  tokenExpiresAt = new Date(Date.now() + 1000 * 3600 * 24 * 12); // 12天后过期

  async getLoginCode(
    data: {
      application: string;
      username: string;
      password: string;
      autoSignin?: boolean;
      type?: string;
    },
    clientId?: string,
  ) {
    const cliId = clientId || gateSSOConfig.clientId;
    const result = await fetcherGate(
      `/api/login?clientId=${cliId}&responseType=code&redirectUri=http://localhost:3000/callback`,
      {
        body: data,
      },
    );
    if (result && result.status === 'ok') {
      return result.data;
    }
    logger.error('getLoginCode error:', result);
    throw new Error('Gate SSO Login Failed');
  }

  async getToken(id: string) {
    return fetcherGate(`/api/get-token?id=${id}&accessToken=${this.token?.access_token}`);
  }
  async getSession(id: string) {
    return fetcherGate(`/api/get-token?id=${id}&accessToken=${this.token?.access_token}`);
  }
  async getAccessToken(code: string, clientId?: string, clientSecret?: string, tag?: string): Promise<GateToken> {
    const cliId = clientId || gateSSOConfig.clientId;
    const cliSecret = clientSecret || gateSSOConfig.clientSecret;
    const result = await fetcherGate(
      `/api/login/oauth/access_token?code=${code}&grant_type=authorization_code&client_id=${cliId}&client_secret=${cliSecret}&tag=${tag}`,
      {
        body: {},
      },
    );

    if (result && result.expires_in && result.id_token) {
      this.token = result;
      return result;
    }
    logger.error('Gate SSO Admin Login Failed', result);
    throw new Error(`Gate SSO Admin Login Failed${result.access_token}`);
  }

  async getUser(accessToken: string) {
    const t = accessToken || this.token?.access_token;
    return fetcherGate(`/api/get-account?accessToken=${t}`);
  }
}

export default GateSSOBaseApi;
