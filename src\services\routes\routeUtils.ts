/**
 * 路由和菜单工具函数
 *
 * 本文件包含与路由和菜单相关的工具函数，这些函数可以被routeServices和menuServices调用
 */

import { ParseDB } from '../../database/database';
import logger from '../../utils/logger';

// 常量定义
export const TABLE_NAME_ROUTER = 'SYS_ROUTER';
export const TABLE_NAME_MENU = 'SYS_MENU';

// 类型定义
export type RouteItem = {
    name: string;
    path: string;
    component: string;
    meta: any;
    children?: RouteItem[];
    props?: boolean;
};

export type RoutesInfo = {
    constantRoutes: RouteItem[],
    authRoutes: RouteItem[]
};

export type MenuType = '目录' | '菜单';
export type MenuStatus = '启用' | '禁用';
export type IconType = '1' | '2';

export type MenuButton = {
    /**
     * button code
     *
     * it can be used to control the button permission
     */
    code: string;
    /** button description */
    desc: string;
};

export type RouteMeta = {
    i18nKey: string;
    keepAlive: boolean;
    constant: boolean;
    order: number;
    href: string;
    hideInMenu: boolean;
    activeMenu: string;
    multiTab: boolean;
    fixedIndexInTab: boolean;
    query: Record<
        string,
        string | number | boolean | null | undefined
    >;
};

export type Menu = {
    objectId?: string;
    id?: number,
    uid: number;
    /** parent menu id */
    parentId: number;
    status: MenuStatus;
    /** menu type */
    menuType: MenuType;
    /** menu name */
    menuName: string;
    roles: string[];
    /** route name */
    routeName: string;
    /** route path */
    routePath: string;
    /** component */
    component?: string;
    /** iconify icon name or local icon name */
    icon: string;
    /** icon type */
    iconType: IconType;
    /** buttons */
    buttons?: MenuButton[] | null;
    /** children menu */
    children?: Menu[] | null;
    tags?: string[];
} & Partial<RouteMeta>;

// 默认路由配置
export const constRouters: RouteItem[] = [{
    "name": "403",
    "path": "/403",
    "component": "layout.blank$view.403",
    "meta": {
        "title": "403 Forbidden",
        "i18nKey": "route.403",
        "icon": "mdi:alert-circle",
        "constant": true,
        "hideInMenu": true
    }
}, {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
        title: 'home',
        i18nKey: 'route.home',
        icon: 'mdi:monitor-dashboard',
        order: 1
    }
}, {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat|mp-wechat-login)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
        title: 'login',
        i18nKey: 'route.login',
        icon: 'mdi:login',
        constant: true,
        hideInMenu: true
    }
}];

export const adminRoutesFirst: RouteItem[] = [
    {
        name: 'manage',
        path: '/manage',
        component: 'layout.base',
        meta: {
            title: 'manage',
            i18nKey: 'route.manage',
            icon: 'carbon:cloud-service-management',
            order: 9,
            roles: ['R_SUPER', 'R_ADMIN', 'role_iknow_admin', 'ROLE_IKNOW_ADMIN', 'R_FROM_PC']
        },
        children: [
            {
                name: 'manage_menu',
                path: '/manage/menu',
                component: 'view.manage_menu',
                meta: {
                    title: 'manage_menu',
                    i18nKey: 'route.manage_menu',
                    icon: 'material-symbols:route',
                    order: 3,
                    roles: ['R_SUPER', 'R_ADMIN', 'role_iknow_admin', 'ROLE_IKNOW_ADMIN'],
                    keepAlive: true
                }
            },
            {
                name: 'manage_role',
                path: '/manage/role',
                component: 'view.manage_role',
                meta: {
                    title: 'manage_role',
                    i18nKey: 'route.manage_role',
                    icon: 'carbon:user-role',
                    order: 2,
                    roles: ['R_SUPER', 'R_ADMIN', 'role_iknow_admin', 'ROLE_IKNOW_ADMIN']
                }
            },
            {
                name: 'manage_session',
                path: '/manage/session',
                component: 'view.manage_session',
                meta: {
                    title: 'manage_session',
                    i18nKey: 'route.manage_session',
                    roles: ['R_SUPER', 'R_ADMIN', 'role_iknow_admin', 'ROLE_IKNOW_ADMIN'],
                    icon: 'mdi:account-key'
                }
            },
            {
                name: 'manage_user',
                path: '/manage/user',
                component: 'view.manage_user',
                meta: {
                    title: 'manage_user',
                    i18nKey: 'route.manage_user',
                    icon: 'ic:round-manage-accounts',
                    order: 1,
                    roles: ['R_SUPER', 'R_ADMIN', 'role_iknow_admin', 'ROLE_IKNOW_ADMIN'],
                }
            }
        ]
    }
];

/**
 * 将菜单记录转换为JSON对象
 * @param record 菜单记录
 * @returns 菜单JSON对象
 */
export const getMenuRecordJson = (record: ParseDB.Object): Menu => {
    if (!record || !record.id) return null;
    return {
        objectId: record.id,
        uid: record.get('uid'),
        id: record.get('uid'),
        parentId: record.get('parentId'),
        menuType: record.get('menuType') || '菜单',
        menuName: record.get('menuName'),
        routeName: record.get('routeName'),
        routePath: record.get('routePath'),
        component: record.get('component'),
        icon: record.get('icon'),
        iconType: record.get('iconType'),
        buttons: record.get('buttons'),
        children: record.get('children'),
        i18nKey: record.get('i18nKey'),
        status: record.get('status') || '启用',
        hideInMenu: record.get('hideInMenu') || false,
        order: record.get('order') || 0,
        tags: record.get('tags') || [],
        roles: record.get('roles') || [],
    };
};

/**
 * 递归获取所有子菜单
 * @param agent 代理
 * @param parentId 父菜单ID
 * @returns 子菜单列表
 */
export const getMenuChildren = async (agent: string, parentId: number): Promise<Menu[]> => {
    const query = new ParseDB.Query(TABLE_NAME_MENU);
    query.equalTo('agent', agent);
    query.equalTo('parentId', parentId);
    const results = await query.find();

    if (results && results.length) {
        // 对每个子菜单，递归获取其子菜单
        const childrenWithSubMenus = await Promise.all(results.map(async item => {
            const menuItem = getMenuRecordJson(item);
            // 递归获取子菜单
            const subChildren = await getMenuChildren(agent, item.get('uid'));

            // 如果有子菜单，添加到children属性
            if (subChildren && subChildren.length > 0) {
                menuItem.children = subChildren;
            }

            return menuItem;
        }));

        return childrenWithSubMenus;
    }

    return [];
};

/**
 * 创建菜单项
 * @param agent 代理
 * @param menuData 菜单数据
 * @returns 创建的菜单
 */
export const createMenuItem = async (agent: string, menuData: Menu): Promise<ParseDB.Object> => {
    // 创建新菜单
    const Menu = ParseDB.Object.extend(TABLE_NAME_MENU);
    const menu = new Menu();

    // 设置基本字段
    menu.set('agent', agent);
    menu.set('uid', menuData.uid);
    menu.set('menuType', menuData.menuType);
    menu.set('menuName', menuData.menuName);
    menu.set('routeName', menuData.routeName);
    menu.set('routePath', menuData.routePath);
    menu.set('icon', menuData.icon || '');
    menu.set('iconType', menuData.iconType || '1');
    menu.set('parentId', menuData.parentId || 0);
    menu.set('status', menuData.status || '启用');
    menu.set('roles', menuData.roles || []);

    // 设置可选字段
    if (menuData.component) menu.set('component', menuData.component);
    if (menuData.buttons) menu.set('buttons', menuData.buttons);
    if (menuData.children) menu.set('children', menuData.children);
    if (menuData.hideInMenu !== undefined) menu.set('hideInMenu', menuData.hideInMenu);
    if (menuData.order !== undefined) menu.set('order', menuData.order);
    if (menuData.tags) menu.set('tags', menuData.tags);

    // 设置其他RouteMeta字段
    if (menuData.i18nKey) menu.set('i18nKey', menuData.i18nKey);
    if (menuData.keepAlive !== undefined) menu.set('keepAlive', menuData.keepAlive);
    if (menuData.constant !== undefined) menu.set('constant', menuData.constant);
    if (menuData.href) menu.set('href', menuData.href);
    if (menuData.activeMenu) menu.set('activeMenu', menuData.activeMenu);
    if (menuData.multiTab !== undefined) menu.set('multiTab', menuData.multiTab);
    if (menuData.fixedIndexInTab !== undefined) menu.set('fixedIndexInTab', menuData.fixedIndexInTab);
    if (menuData.query) menu.set('query', menuData.query);

    return await menu.save();
};

/**
 * 递归创建菜单及其子菜单
 * @param agent 代理
 * @param route 路由项
 * @param parentId 父菜单ID
 * @param level 层级
 * @param uidCounter UID计数器
 * @returns 创建的菜单数据
 */
export const createMenuRecursive = async (
    agent: string,
    route: RouteItem,
    parentId = 0,
    level = 0,
    uidCounter: { value: number }
): Promise<any> => {
    if (!route.name) return null;

    try {
        // 生成唯一的UID，将层级信息编码到UID中
        // 顶级菜单从100开始，每一层子菜单都会有一个偏移量
        const levelOffset = level * 100; // 每一层级偏移100
        const currentUid = uidCounter.value++ + levelOffset;
        const isDirectory = route.children && route.children.length > 0;

        const menuData: Menu = {
            uid: currentUid,
            parentId: parentId,
            status: '启用',
            menuType: isDirectory ? '目录' : '菜单',
            menuName: route.meta?.title || route.name,
            routeName: route.name,
            routePath: route.path,
            component: route.component || '',
            icon: route.meta?.icon || '',
            iconType: '1',
            buttons: [],
            roles: route.meta?.roles || [],
            // RouteMeta字段
            i18nKey: route.meta?.i18nKey,
            keepAlive: route.meta?.keepAlive,
            constant: route.meta?.constant,
            order: route.meta?.order,
            hideInMenu: route.meta?.hideInMenu,
            activeMenu: route.meta?.activeMenu,
            multiTab: route.meta?.multiTab,
            fixedIndexInTab: route.meta?.fixedIndexInTab,
        };

        const savedMenu = await createMenuItem(agent, menuData);
        const savedMenuData = savedMenu.toJSON();

        // 如果有子菜单，递归创建
        if (isDirectory) {
            const children = [];
            for (const childRoute of route.children) {
                const childMenu = await createMenuRecursive(agent, childRoute, currentUid, level + 1, uidCounter);
                if (childMenu) {
                    children.push(childMenu);
                }
            }
            savedMenuData.children = children;
        }

        return savedMenuData;
    } catch (error) {
        logger.error(`创建菜单失败: ${route.name}, 层级: ${level}`, error);
        return null;
    }
};

/**
 * 检查菜单是否存在
 * @param agent 代理
 * @param routeName 路由名称
 * @returns 是否存在
 */
export const isMenuExists = async (agent: string, routeName: string): Promise<boolean> => {
    const query = new ParseDB.Query(TABLE_NAME_MENU);
    query.equalTo('agent', agent);
    query.equalTo('routeName', routeName);
    const existingMenu = await query.first();
    return !!existingMenu;
};

/**
 * 检查菜单是否存在（排除指定ID）
 * @param agent 代理
 * @param routeName 路由名称
 * @param excludeObjectId 排除的对象ID
 * @returns 是否存在
 */
export const isMenuExistsExcludeId = async (agent: string, routeName: string, excludeObjectId: string): Promise<boolean> => {
    const query = new ParseDB.Query(TABLE_NAME_MENU);
    query.equalTo('agent', agent);
    query.equalTo('routeName', routeName);
    query.notEqualTo('objectId', excludeObjectId);
    const existingMenu = await query.first();
    return !!existingMenu;
};

/**
 * 获取菜单
 * @param agent 代理
 * @param objectId 对象ID
 * @returns 菜单对象
 */
export const getMenu = async (agent: string, objectId: string): Promise<ParseDB.Object> => {
    const query = new ParseDB.Query(TABLE_NAME_MENU);
    query.equalTo('agent', agent);
    query.equalTo('objectId', objectId);
    return await query.first();
};

/**
 * 检查菜单是否有子菜单
 * @param agent 代理
 * @param menuId 菜单ID
 * @returns 是否有子菜单
 */
export const hasChildMenus = async (agent: string, menuId: string): Promise<boolean> => {
    const childQuery = new ParseDB.Query(TABLE_NAME_MENU);
    childQuery.equalTo('agent', agent);
    childQuery.equalTo('parentId', menuId);
    const childCount = await childQuery.count();
    return childCount > 0;
};

/**
 * 获取路由数据
 * @param agent 代理
 * @param user 用户类型（admin或normal_user）
 * @returns 路由数据
 */
export const getRouteData = async (agent: string, user: string): Promise<ParseDB.Object> => {
    const query = new ParseDB.Query(TABLE_NAME_ROUTER);
    query.equalTo('agent', agent);
    query.equalTo('user', user);
    return await query.first();
};

/**
 * 保存路由数据
 * @param agent 代理
 * @param user 用户类型
 * @param routes 路由信息
 * @param operation 操作者
 * @returns 保存的记录
 */
export const saveRouteData = async (
    agent: string,
    user: string,
    routes: RoutesInfo,
    operation: string
): Promise<ParseDB.Object> => {
    const query = new ParseDB.Query(TABLE_NAME_ROUTER);
    query.equalTo('agent', agent);
    query.equalTo('user', user);
    let record = await query.first();

    if (record) {
        logger.warn(`更新路由 constantRoutes:${routes.constantRoutes?.length} authRoutes:${routes.authRoutes?.length}`);
        record.set('constantRoutes', routes.constantRoutes);
        record.set('authRoutes', routes.authRoutes);
        record.set('operation', operation);
        record.set('user', user);
    } else {
        // 没保存过，创建新记录
        logger.warn(`创建路由 constantRoutes:${routes.constantRoutes?.length} authRoutes:${routes.authRoutes?.length}`);
        const RouterTB = ParseDB.Object.extend(TABLE_NAME_ROUTER);
        record = new RouterTB();
        record.set('agent', agent);
        record.set('constantRoutes', routes.constantRoutes);
        record.set('authRoutes', routes.authRoutes);
        record.set('operation', operation);
        record.set('user', user);
    }

    return await record.save();
};

/**
 * 合并和去重路由数据
 * @param routes1 路由数据1
 * @param routes2 路由数据2
 * @returns 合并后的路由数据
 */
export const mergeAndDeduplicateRoutes = (routes1: RouteItem[], routes2: RouteItem[]): RouteItem[] => {
    let routes = [];

    if (routes1) {
        routes = Array.isArray(routes1) ? routes1 : [];
    }

    if (routes2) {
        routes = [...routes, ...routes2];
    }

    // 去重
    const uniqueRoutes = [];
    const routeNames = new Set();
    for (const route of routes) {
        if (!routeNames.has(route.name)) {
            routeNames.add(route.name);
            uniqueRoutes.push(route);
        }
    }

    return uniqueRoutes;
};

/**
 * 清空菜单
 * @param agent 代理
 * @returns 清空的菜单数量
 */
export const clearMenus = async (agent: string): Promise<number> => {
    const query = new ParseDB.Query(TABLE_NAME_MENU);
    query.equalTo('agent', agent);
    const existingMenus = await query.find();

    if (existingMenus && existingMenus.length > 0) {
        await ParseDB.Object.destroyAll(existingMenus);
        logger.info(`已清空现有菜单，共 ${existingMenus.length} 条`);
        return existingMenus.length;
    }

    return 0;
};


/**
 * 根据客户端类型和标签获取菜单
 * @param agent 代理
 * @param isAdmin 是否为管理员
 * @param clientType 客户端类型对象，包含isWechat、isMobile、isPc
 * @param userRoles 用户角色列表
 * @returns 菜单数据和路由数据
 */
export const getMenuByClientType = async (
    agent: string,
    isAdmin: boolean,
    clientType: { isWechat: boolean; isMobile: boolean; isPc: boolean },
    userRoles: string[] = []
): Promise<{ menus: Menu[]; routes: RouteItem[] }> => {
    // 构建查询条件
    let query: ParseDB.Query;

    // 根据客户端类型构建标签过滤条件
    const tagFilters = [];
    if (clientType.isWechat) tagFilters.push('isWechat');
    if (clientType.isMobile) tagFilters.push('isMobile');
    if (clientType.isPc) tagFilters.push('isPc');

    // 创建基本查询
    query = new ParseDB.Query(TABLE_NAME_MENU);
    query.equalTo('agent', agent);
    query.equalTo('parentId', 0); // 只查询顶级菜单

    // 我们不使用复杂的标签过滤逻辑，而是获取所有菜单后在内存中过滤
    // 这样可以避免复杂查询可能导致的错误

    // 如果有标签过滤条件，记录下来，后面在内存中过滤
    const hasTagFilters = tagFilters.length > 0;

    // 执行查询
    let menuResults = await query.find();

    // 如果有标签过滤条件，在内存中过滤菜单
    if (menuResults && menuResults.length > 0 && hasTagFilters) {
        menuResults = menuResults.filter(item => {
            const itemTags = item.get('tags');

            // 如果菜单没有tags字段或者tags为空数组，则所有用户都可以访问
            if (!itemTags || itemTags.length === 0) {
                return true;
            }

            // 检查菜单标签是否包含客户端标签
            return tagFilters.some(tag => itemTags.includes(tag));
        });
    }

    // 如果没有结果，返回默认数据
    if (!menuResults || menuResults.length === 0) {
        // 尝试从路由表获取数据
        const routeData = await getRouteData(agent, isAdmin ? 'admin' : 'normal_user');
        if (routeData) {
            const authRoutes = routeData.get('authRoutes') || [];
            return { menus: [], routes: authRoutes };
        }
        return { menus: [], routes: clientType.isWechat ? [constRouters[1]] : adminRoutesFirst }; // 返回默认路由
    }

    // 处理查询结果
    const menus = [];
    const routes = [];

    /**
     * 检查用户是否有权限访问菜单
     * @param menuRoles 菜单角色列表
     * @returns 是否有权限
     */
    const hasPermission = (menuRoles: string[]): boolean => {
        // 如果菜单没有设置roles或者roles为空数组，则所有用户都可以访问
        if (!menuRoles || menuRoles.length === 0) {
            return true;
        }

        // 如果用户是管理员，则可以访问所有菜单
        if (isAdmin) {
            return true;
        }

        // 检查用户角色是否与菜单角色有交集
        return userRoles.some(role => menuRoles.includes(role));
    };

    /**
     * 递归过滤菜单及其子菜单
     * @param menuItem 菜单项
     * @returns 过滤后的菜单项，如果没有权限则返回null
     */
    const filterMenuByRoles = (menuItem: Menu): Menu => {
        // 检查当前菜单的权限
        if (!hasPermission(menuItem.roles)) {
            return null;
        }

        // 如果有子菜单，递归过滤
        if (menuItem.children && menuItem.children.length > 0) {
            const filteredChildren = menuItem.children
                .map(child => filterMenuByRoles(child))
                .filter(Boolean);

            // 更新子菜单
            menuItem.children = filteredChildren.length > 0 ? filteredChildren : null;
        }

        return menuItem;
    };

    // 递归获取子菜单并转换为路由格式
    for (const item of menuResults) {
        const menuItem = getMenuRecordJson(item);

        // 获取子菜单
        const children = await getMenuChildren(agent, item.get('uid'));
        if (children && children.length > 0) {
            menuItem.children = children;
        }

        // 根据用户角色过滤菜单
        const filteredMenu = filterMenuByRoles(menuItem);
        if (filteredMenu) {
            menus.push(filteredMenu);

            // 将菜单转换为路由格式
            const route = menuToRoute(filteredMenu);
            if (route) {
                routes.push(route);
            }
        }
    }

    return { menus, routes };
};

/**
 * 将菜单对象转换为路由对象
 * @param menu 菜单对象
 * @returns 路由对象
 */
export const menuToRoute = (menu: Menu): RouteItem => {
    if (!menu) return null;

    const route: RouteItem = {
        name: menu.routeName,
        path: menu.routePath,
        component: menu.component || '',
        meta: {
            title: menu.menuName,
            i18nKey: menu.i18nKey,
            icon: menu.icon,
            order: menu.order,
            hideInMenu: menu.hideInMenu,
            constant: menu.constant,
            keepAlive: menu.keepAlive,
            activeMenu: menu.activeMenu,
            multiTab: menu.multiTab,
            fixedIndexInTab: menu.fixedIndexInTab,
            roles: menu.roles
        }
    };

    // 处理子路由
    if (menu.children && menu.children.length > 0) {
        route.children = menu.children.map(child => menuToRoute(child)).filter(Boolean);
    }

    return route;
};

/**
 * 将路由对象转换为菜单对象
 * @param route 路由对象
 * @param parentId 父菜单ID
 * @param level 层级
 * @param uidCounter UID计数器
 * @returns 菜单对象
 */
export const routeToMenu = (route: RouteItem, parentId = 0, level = 0, uidCounter: { value: number }): Menu => {
    if (!route || !route.name) return null;

    // 生成唯一的UID，将层级信息编码到UID中
    const levelOffset = level * 100; // 每一层级偏移100
    const currentUid = uidCounter.value++ + levelOffset;
    const isDirectory = route.children && route.children.length > 0;

    // 创建菜单对象
    const menu: Menu = {
        uid: currentUid,
        parentId: parentId,
        status: '启用',
        menuType: isDirectory ? '目录' : '菜单',
        menuName: route.meta?.title || route.name,
        routeName: route.name,
        routePath: route.path,
        component: route.component || '',
        icon: route.meta?.icon || '',
        iconType: '1',
        buttons: [],
        roles: route.meta?.roles || [],
        // RouteMeta字段
        i18nKey: route.meta?.i18nKey,
        keepAlive: route.meta?.keepAlive,
        constant: route.meta?.constant,
        order: route.meta?.order,
        hideInMenu: route.meta?.hideInMenu,
        activeMenu: route.meta?.activeMenu,
        multiTab: route.meta?.multiTab,
        fixedIndexInTab: route.meta?.fixedIndexInTab,
    };

    // 处理子菜单
    if (isDirectory) {
        const children = [];
        for (const childRoute of route.children) {
            const childMenu = routeToMenu(childRoute, currentUid, level + 1, uidCounter);
            if (childMenu) {
                children.push(childMenu);
            }
        }
        menu.children = children.length > 0 ? children : null;
    }

    return menu;
};

/**
 * 将路由数据保存到菜单表中
 * @param agent 代理
 * @param routes 路由数据
 * @returns 保存的菜单数量
 */
export const saveRoutesToMenu = async (agent: string, routes: RouteItem[]): Promise<number> => {
    if (!routes || routes.length === 0) {
        return 0;
    }

    // 初始化UID计数器
    const uidCounter = { value: 1000 };
    const savedMenus = [];

    // 递归保存菜单
    const saveMenuRecursive = async (menu: Menu): Promise<void> => {
        try {
            // 检查是否已存在相同的菜单
            const query = new ParseDB.Query(TABLE_NAME_MENU);
            query.equalTo('agent', agent);
            query.equalTo('routeName', menu.routeName);
            const existingMenu = await query.first();

            if (existingMenu) {
                // 如果已存在，则更新字段，但不覆盖未提供值的属性
                Object.entries(menu).forEach(([key, value]) => {
                    if (value !== undefined && value !== null) {
                        existingMenu.set(key, value);
                    }
                });
                await existingMenu.save();
                savedMenus.push(existingMenu.toJSON());
            } else {
                // 如果不存在，则创建新菜单
                const savedMenu = await createMenuItem(agent, menu);
                savedMenus.push(savedMenu.toJSON());
            }

            // 递归保存子菜单
            if (menu.children && menu.children.length > 0) {
                for (const childMenu of menu.children) {
                    await saveMenuRecursive(childMenu);
                }
            }
        } catch (error) {
            logger.error(`保存菜单失败: ${menu.routeName}`, error);
        }
    };

    // 将路由转换为菜单并保存
    for (const route of routes) {
        const menu = routeToMenu(route, 0, 0, uidCounter);
        if (menu) {
            await saveMenuRecursive(menu);
        }
    }

    return savedMenus.length;
};
