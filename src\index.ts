import cors from "cors";
import express, { Application, RequestHandler } from "express";
import morgan from "morgan";
import path from "path";
import { format } from "util";
import routes from "./api";
import config from "./config";
import initParseServer from "./database/database";
import startCronJobServices from "./services/cronjob/cronjobServices";
import logger from "./utils/logger";
import { getProjectVersion, getVersionInfo } from "./api/routes/root";

const app: Application = express();

app.disable("x-powered-by");

app.use(express.json({ limit: "6mb" }) as RequestHandler);
app.use(
  cors({
    origin(_origin, callback) {
      callback(null, true);
    },
  }),
);

// 打印http请求日志
// app.use(morgan("tiny"));
// 所有的路由都在这里配置
app.use("/v1", routes());

// http://localhost:8989/default_twitter_icon.png
app.use("/", express.static(path.join(__dirname, "../public")));
app.use("/", getProjectVersion);

// Parse数据库
initParseServer();
// logger
logger.level = process.env.LOGGER_LEVEL || "debug";

logger.info(`logger level:${logger.level},GATE_SSO_CLIENT_ID:${process.env.GATE_SSO_CLIENT_ID}`);

const { name, version, buildDateString } = getVersionInfo();
app.listen(config.port, () => {
  logger.info(
    format(
      "\n%s\n\tGroup API Server started on port %s\n%s\n\tName: %s\n\tVersion: %s\n\tBuild Time: %s\n%s\n",
      "=".repeat(60),
      config.port,
      "-".repeat(60),
      name,
      version,
      buildDateString,
      "=".repeat(60)
    )
  );
  // Start scheduled tasks
  startCronJobServices();
});
