
import { getLogger } from '../../utils/logger';
import notAtServices from "../chatlist/groupNotAtMsgServices";
import friendServices from "../health/healthPatientsServices";
import { cronJobHealthPatientsWxGroup } from "../health/healthPatientsWxGroup";
import { cronJobIssueAwardProcess } from "../rewardServices";
import { ParseDB } from "../services";
import session from "../sessionManager";
import { processNoticTimers } from "../timer/timerFunction";
import { cronRefreshDoctors } from "../users/userFunction";

const logger = getLogger('cronjobSrv');
const checkGuildMemberAccess = () => {
  logger.info("Process of guild membership and award ...");

  cronJobIssueAwardProcess()
    .then((result: any) => {
      logger.warn("cronJobIssueAwardProcess result:", result);
    })
    .catch((err: any) => {
      logger.error("cronJobIssueAwardProcess err:", err);
    });
};

// 个人关联信息汇总
const cronJobMyTotalInfo = async () => {
  logger.info("cronJobMyTotalInfo:");
  // 用户信息表
  const query = new ParseDB.Query("Users");

  query.exists("account");

  const results = await query.find();

  const accounts = results.map(record => record.get("account"));

  const totalInfo = await Promise.all(

    accounts?.map(async account => {
      //---------------------
      // 我发送的
      const msg = new ParseDB.Query("AIChatRecord");
      msg.equalTo("account", account);

      const mySendMsg = await msg.count();
      // 发送给我
      const toMe = new ParseDB.Query("AIChatRecord");
      toMe.equalTo("receiver", account);
      const sendToMe = await toMe.count();
      // 所有消息
      const mainQuery = ParseDB.Query.or(msg, toMe);
      const myAllMsg = await mainQuery.count();
      // 发送给我+未读
      toMe.equalTo("status", "first");
      const unRead = await toMe.count();
      //---------------------
      // 应答信息表
      const reply = new ParseDB.Query("AIMyReply");
      reply.equalTo("account", account);
      const myReply = await reply.count();
      //---------------------
      // 知识库
      const know = new ParseDB.Query("AIMyKnowledge");
      know.equalTo("account", account);
      const myKnowledge = await know.count();

      return {
        account,
        mySendMsg, // 我发送出去的
        sendToMe, // 发送给我
        myAllMsg, // 我发送的+发送给我的
        unRead, // 发送给我，但是未读

        myReply, // 我回复
        mySubject: mySendMsg + sendToMe + myKnowledge,

        myKnowledge,
      };
    })
  );

  totalInfo?.forEach(async info => {
    const t = new ParseDB.Query(ParseDB.Object.extend("AIMyTotalInfo"));

    t.equalTo("account", info.account);
    t.descending("updatedAt");

    let table = await t.first();
    if (!table) {
      table = new ParseDB.Object("AIMyTotalInfo");
    }
    table.set(info);
    await table.save();
  });
};

const { CronJob } = require("cron");

const startCronJobServices = () => {
  // 程序启动时，都执行一遍
  logger.info("===>startCronJobServices:start");

  cronJobMyTotalInfo();
  session.cronClearSession()
  //summaryServices.cronChatSummary()
  cronRefreshDoctors()
  friendServices.cronRefreshMyPatients()
  notAtServices.cronChatAIGenContent()
  cronJobHealthPatientsWxGroup()
  // 执行一次即可,启动医生定制的定时器(发微信通知给病人)
  processNoticTimers();

  logger.info("===>startCronJobServices:2");
  // 秒0-59 分钟0-59 小时0-23 天1-31 月份0-11 星期几:0-6(0是星期天)
  // 10 03 * * * * （每次秒为10 分为 3 时候执行）

  // 每小时执行一次,群聊信息AI整理
  const job2 = new CronJob("41 * * * *", notAtServices.cronChatAIGenContent, null, true, "Asia/Taipei");
  logger.info("cronJobMyTotalInfo:", JSON.stringify(job2?.cronTime));

  // 每小时执行一次,个人信息数量统计
  const job3 = new CronJob("51 * * * *", cronJobMyTotalInfo, null, true, "Asia/Taipei");
  logger.info("cronJobMyTotalInfo:", JSON.stringify(job3?.cronTime));

  // 每小时执行一次,session清除
  const job4 = new CronJob("38 * * * *", session.cronClearSession, null, true, "Asia/Taipei");
  logger.info("cronClearSession:", JSON.stringify(job4?.cronTime));
  // 每小时执行一次,聊天总结
  //const jobSummary = new CronJob("12 * * * *", summaryServices.cronChatSummary, null, true, "Asia/Taipei");
  //logger.info("cronChatSummary:", JSON.stringify(jobSummary?.cronTime));

  // 每小时执行一次,修改Users表,更新isDoctor 字段
  const jobDoctorFlag = new CronJob("1 * * * *", cronRefreshDoctors, null, true, "Asia/Taipei");
  logger.info("cronRefreshDoctors:", JSON.stringify(jobDoctorFlag?.cronTime));
  // 每小时执行一次,为医生归总患者列表
  const jobFriends = new CronJob("22 * * * *", friendServices.cronRefreshMyPatients, null, true, "Asia/Taipei");
  logger.info("cronRefreshMyPatients:", JSON.stringify(jobFriends?.cronTime));

  // 每小时执行一次,健康打卡群定时通知发送
  const jobHealthPatientsWxGroup = new CronJob("31 * * * *", cronJobHealthPatientsWxGroup, null, true, "Asia/Taipei");
  logger.info("cronJobHealthPatientsWxGroup:", JSON.stringify(jobHealthPatientsWxGroup?.cronTime));



  // 每一分的30秒时执行
  // new CronJob("30 * * * * *", checkGuildMemberAccess, null, true, "America/Los_Angeles");

  // 查看服务器支持的时区
  // cd /usr/share/zoneinfo/

  // // 每三分钟执行一次
  // const job1 = new CronJob("*/3  * * * *", checkGuildMemberAccess, null, true, "Asia/Taipei");
  // logger.info("cron job1:", JSON.stringify(job1?.cronTime));
  // // 每小时执行一次
  // // const job2 =new CronJob("25 * * * *", cronJobRefreshAllGuild, null, true, "Asia/Taipei");
  // const job2 =new CronJob("*/6 * * * *", cronJobRefreshAllGuild, null, true, "Asia/Taipei");
  // logger.info("cron job2:", JSON.stringify(job2?.cronTime));

  // 每小时执行一次
  // const job3 = new CronJob("25 * * * *", ipfsSpeedTest, null, true, "Asia/Taipei");
  // logger.info("cron job3:", JSON.stringify(job3?.cronTime));

  // 每分钟执行一次
  //  const job4 = new CronJob("6 * * * * *", nftInfoTB.cronJobRefreshNftInfo, null, true, "Asia/Taipei");
  //  logger.info("cron job4:", JSON.stringify(job4?.cronTime));
  // 每分钟执行一次
  // const job5 = new CronJob("46 * * * * *", nftInfoTB.cronRefreshNftTraits, null, true, "Asia/Taipei");
  // logger.info("cron job5:", JSON.stringify(job5?.cronTime));
};
export default startCronJobServices;
