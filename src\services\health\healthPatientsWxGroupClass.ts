/**
 * 健康打卡微信群管理服务
 * 功能包括:
 * 1. 健康打卡群的创建与管理(3个月/6个月)
 * 2. 群打卡提醒与统计
 * 3. 阶段性通知的发送
 * 4. 复诊提醒管理
 */

import { GResponseList } from '../../types/types';
import { getLogger } from '../../utils/logger';

import { IStandardizedHealthGroup } from '../../types/health';
import { BaseServices } from '../baseServices';
import { ParseDB } from '../services';
import { getRecordJson, cronJobHealthPatientsWxGroup } from './healthPatientsWxGroup';



const TABLE_NAME = 'AIHealthPatientsWxGroup';


class HealthPatientsWxGroup extends BaseServices {
  constructor() {
    super();
    this.TABLE_NAME = TABLE_NAME;
  }
  servicesGetPatientsWxGroup = async (sessionId: string) => {
    const sess = await this.checkSession(sessionId);

    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('ownerAccount', sess.account);

    const results = await query.find();
    return results.map(this.getRecordJson);
  };

  servicesGetGroupList = async ({ sessionId, pageNum, pageSize, search, }: {
    sessionId: string; pageNum: number; pageSize: number; search: any;
  }): Promise<GResponseList<IStandardizedHealthGroup>> => {
    const sess = await this.checkPermission({ sessionId, roles: "HEALTH_MANAGER" });

    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('agent', sess.agent);

    if (search.groupStatus) query.equalTo('groupStatus', search.groupStatus);
    // 群名搜索,部份匹配,不区分大小写
    if (search.groupName) query.matches('groupName', new RegExp(String(search.groupName), 'i'));
    if (search.type) query.equalTo('type', search.type);

    const total = await query.count();
    query.skip(this.getSkipNumber(total, pageNum, pageSize));
    query.limit(pageSize);
    query.descending('createdAt');

    const results = await query.find();
    return this.makeGResponseList(results.map(getRecordJson), pageNum, pageSize, total);
  };

  // 测试
  servicesTest = async (sessionId: string) => {
    const sess = await this.isSuperMan(sessionId);
    if (!sess) return { ret: false, msg: '没有权限' };

    cronJobHealthPatientsWxGroup(true);
    return { ret: true, msg: '测试成功' };
  }
}

export default new HealthPatientsWxGroup();

