// deploy.ts 
// (远程运行 ) 可在windows开发机上,1.运行 git bash ,2.运行脚本 node deploy-mfull.cn.ts makedist

const fs = require("fs");
const path = require("path");
const archiver = require("archiver");
const { Client } = require("ssh2");
const sd = require("silly-datetime");
const { execSync } = require("child_process")

const args = process.argv.splice(2);
const isRollback = args.includes("rollback");
const makeDist = args.includes("makedist");
const test = args.includes("test");

//const privateKey = fs.readFileSync("/home/<USER>/.ssh/id_ecdsa");
const privateKey = fs.readFileSync("C:/Users/<USER>/.ssh/id_ecdsa");
//const privateKey = fs.readFileSync("/Users/<USER>/.ssh/id_ecdsa");
//const privateKey = fs.readFileSync("C:\\Users\\<USER>\\.ssh\\id_ecdsa");
// 远程服务器配置信息
const config = {
  host: "bt.mfull.cn",
  port: 22,
  username: "web25",
  privateKey,
  pathUrl: "/home/<USER>/group-srv-bin",
  // passphrase: "123456",
};
// 当前时间
const curTime = sd.format(new Date(), "YYYYMMDDHH");
const localZipFile = `${__dirname}/dist-${curTime}.zip`;
const remoteZipFile = `${config.pathUrl}/dist-${curTime}.zip`;
// 当前时间格式化
console.log((isRollback ? "回滚" : "部署") + "时间:" + curTime);

// 设置本地 dist 文件路径
const distPath = path.resolve(__dirname, "dist");

const ssh2 = new Client();
// 本地文件上传至远程服务器
function uploadFile(filePath) {
  ssh2.connect(config);
  ssh2.on("ready", () => {
    console.log("SSH login success");
    ssh2.sftp(function (err, sftp) {
      if (err) {
        console.log("Error, problem starting SFTP: %s", err);
        process.exit(2);
      }

      console.log("- SFTP started");

      // upload file
      var readStream = fs.createReadStream(filePath);
      var writeStream = sftp.createWriteStream(remoteZipFile);

      // what to do when transfer finishes
      writeStream.on("close", function () {
        console.log("- file transferred");
        sftp.end();
        process.exit(0);
      });

      // initiate transfer of file
      // readStream.pipe(writeStream)
      sftp.fastPut(
        filePath,
        remoteZipFile,
        {
          step: function (total_transferred, chunk, total) {
            process.stdout.write(
              "\r" +
              "Total transferred: " +
              total_transferred +
              " out of " +
              total,
            );
          },
        },
        function (err) {
          if (err) throw err;

          console.log("\r\nFile transferred successfully!");

          remoteFileUpdate();
        },
      );
    });
  });
  ssh2.on("error", function (err) {
    console.log("- connection error: %s", err);
    process.exit(1);
  });
  ssh2.on("end", function () {
    console.log("ssh connection end");
    process.exit(0);
  });
}

const execCmd = (cmd) => {
  return new Promise((resolve, reject) => {
    ssh2.exec(cmd, (err, stream) => {
      if (err) return reject(err);
      stream.on('close', (code, signal) => {
        if (code === 0) {
          resolve(1);
        } else {
          reject(new Error(`Command failed with code ${code}, signal ${signal}`));
        }
      }).on('data', (data) => {
        process.stdout.write('STDOUT: ' + data);
      }).stderr.on('data', (data) => {
        console.log('STDERR: ' + data);
      });
    });
  });
};
// 远端文件更新
const remoteFileUpdate = async () => {
  const cmd = isRollback
    ? `cd ${config.pathUrl} && rm -rf dist  || true && mv dist.bak${curTime} dist  || true`
    : `cd ${config.pathUrl} && (rm -rf dist.bak${curTime} || true) && (mv dist dist.bak${curTime} || true) && unzip -o -u dist-${curTime}.zip`;

  let conn = new Client();
  conn.connect(config);
  conn.on("ready", async () => {
    console.log("SSH login success");
    try {
      // 备份老的dist,解压新的dist
      await execCmd(cmd);
      console.log("exec cmd success:", cmd);
    } catch (err) {
      console.error("执行命令失败:", err);
      return; // 如果执行命令失败，终止执行后续命令
    }
    // 安装依赖
    const pnpmInstallCmd = `cd ${config.pathUrl}/dist && pnpm install`;
    try {
      console.log(pnpmInstallCmd);
      await execCmd(pnpmInstallCmd);
      console.log("安装依赖成功!");
    } catch (err) {
      console.error("安装依赖失败:", err);
      return; // 如果安装依赖失败，终止执行后续命令
    }
    // 拷贝最新配置文件和私钥
    const copyCmd = `cp ${config.pathUrl}/prod.dist.env ${config.pathUrl}/dist/.env.development`;
    const copyCmd2 = `cp ${config.pathUrl}/meal_checkin_prompt.txt ${config.pathUrl}/dist/`;
    const copyCmd3 = `cp ${config.pathUrl}/health_check_prompt.txt ${config.pathUrl}/dist/`;
    const copyKeys = `cp -R ${config.pathUrl}/keys ${config.pathUrl}/dist/`;
    try {
      console.log(copyCmd);
      await execCmd(copyCmd);
      await execCmd(copyCmd2);
      await execCmd(copyCmd3);
      console.log("拷贝配置文件成功!");
      // 拷贝 keys
      await execCmd(copyKeys);
      console.log("拷贝 keys 成功!");
    } catch (err) {
      console.error("拷贝配置文件失败:", err);
    }
    // 重启 index.js
    try {
      await execCmd(`cd ${config.pathUrl} && pm2 restart group-srv`);
      console.log("exec pm2 restart group-srv success");
    } catch (err) {
      console.error("重启服务失败:", err);
    } finally {
      conn.end();
      process.exit(0);
    }
  });
};
// 本地文件压缩
const zipDirector = () => {
  console.log("zipDirector:");
  const zipFile = localZipFile;
  const output = fs.createWriteStream(zipFile);
  const archive = archiver("zip", { zlib: { level: 9 } }).on("error", err => {
    console.log("archiver error:", err);
    throw err;
  });
  output.on("close", err => {
    if (err) {
      console.log("something error width the zip process:", err);
      return;
    }
    console.log(`${archive.pointer()} total bytes`);
    console.log(
      "archiver has been finalized and the output file descriptor has closed.",
    );
    console.log("The zip file is ", `zipFile`);

    uploadFile(zipFile);
  });
  output.on("end", () => {
    console.log("Data has been drained");
  });
  archive.pipe(output);
  archive.directory(distPath, "./dist");
  archive.finalize();
};

const makeDistDir = () => {
  // 打包
  const { execSync } = require("child_process");

  execSync("rm -rf dist", { stdio: "inherit" });
  execSync("pnpm run build", { stdio: "inherit" });
  // 删除旧的dist.tar.gz
  //execSync("rm -rf dist.tar.gz", { stdio: "inherit" })
  // 复制文件
  // 源文件数组
  const files = ["package.json", "tsconfig.build.json", ".env", "public"];
  // 目标目录
  const targetDir = "dist";
  // 拷贝命令
  const cmd = `cp -r ${files.join(" ")} ${targetDir}`;
  try {
    console.log(cmd);
    // 执行拷贝
    execSync(cmd, { stdio: "inherit" });
    console.log("拷贝完成!");
  } catch (err) {
    console.error("拷贝失败:", err);
  }
  // execSync("cp {package.json,tsconfig.build.json,.env.mfull.cn} dist", { stdio: "inherit" });
  // 检查文件是否存在,且文件创建时间在10分钟内
  const { statSync } = require("fs");
  const { mtime } = statSync("dist");
  const diff = new Date().getTime() - new Date(mtime).getTime();
  if (diff > 10 * 60 * 1000) {
    throw new Error("dist文件创建时间超过10分钟");
  }
  console.log("dist文件夹生成成功...");
};
function main() {
  console.log(
    "\t1. 部署: node deploy.js \n \
    \t2. 回滚: node deploy.js rollback \n \
    \t3. 编译: node deploy.js makedist",
  );
  console.log("\t4. 按照依赖 pnpm install")
  console.log("\t5. 拷贝最新配置文件和私钥")
  console.log("\t6. 重启 pm2 restart group-srv")
  if (test) {
    console.log("test mode");
    remoteFileUpdate();
    return;
  }
  // 回滚代码
  if (isRollback) {
    remoteFileUpdate();
  } else {
    if (makeDist) makeDistDir();
    zipDirector();
  }
}

main();
