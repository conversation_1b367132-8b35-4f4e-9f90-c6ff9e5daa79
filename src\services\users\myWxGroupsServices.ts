import { EthAddress, GResponse, GResponseList } from '../../types/types';
import ApiError from '../../utils/api_error';
import { getLogger } from '../../utils/logger';
import { BaseServices } from '../baseServices';
import { ParseDB } from '../services';
import { addGroupsTimer, addGroupTimer, addGroupTimerPublic, delGroupTimer, getMyGroupTimers } from '../timer/timerFunction';
import { TIMER_CLIENT } from '../timer/types';
import { getWxGroupMembers } from '../wechat/cowGroupFunction';
import { addItchatGroupOfUser, getGroupsOfUser, getGroupsOfUserExt } from './myWxGroupsFunctions';
import { GROUP_MEMBER_INFO } from './types';

const logger = getLogger('wxGroups');
// 用户拥有的微信群，只有作为医生才能拥有微信群
const TABLE_NAME = 'AIItchatGroupsOfUser';
class MyWxGroupsServices extends BaseServices {
  TABLE_NAME: string;  // 添加属性声明

  constructor() {
    super();
    this.TABLE_NAME = TABLE_NAME;
  }
  // 获取我拥有的微信群
  servicesGetMyGroups = async ({ agent, sessionId, sign, pageNum = 1, pageSize = 50 }: { agent: EthAddress, sessionId: string; sign: string, pageNum: number, pageSize: number }) => {
    const sess = await this.checkSession(sessionId);
    return getGroupsOfUser(sess.account, sess.agent, pageNum, pageSize);
  };

  // 获取我拥有的微信群
  servicesGetMyGroupsOwn = async ({
    sessionId,
    search,
    pageNum,
    pageSize,
  }: {
    search: any;
    sessionId: string;
    agent: EthAddress;
    pageNum: number;
    pageSize: number;
  }) => {
    const sess = await this.checkSession(sessionId);
    if (!sess) throw new ApiError('sessionId is not valid', 400);

    return getGroupsOfUserExt(sess.account, sess.agent, pageNum, pageSize, search);
  };

  // 获取我拥有的所有微信群的用户
  servicesGetMyWxUsersOwn = async ({
    sessionId,
    search,
    pageNum,
    pageSize,
  }: {
    search: any;
    sessionId: string;
    agent: EthAddress;
    pageNum: number;
    pageSize: number;
  }): Promise<GResponseList<GROUP_MEMBER_INFO>> => {
    const sess = await this.checkSession(sessionId);
    if (!sess) throw new ApiError('sessionId is not valid', 400);

    const groups = await getGroupsOfUserExt(sess.account, sess.agent, 1, 500, search);
    if (groups?.list?.length) {
      const results: GROUP_MEMBER_INFO[][] = await Promise.all(groups.list.flatMap(group => getWxGroupMembers({ groupObjectId: group.groupObjectId })));

      const regex = new RegExp(search.memberNickName, "i");
      const results2: GROUP_MEMBER_INFO[] = results.flat().filter((user, index, self) =>
        index === self.findIndex((t) => t.UserName === user.UserName && regex.test(t.NickName))
      );

      let results3 = results2;
      const total = results2.length;
      const skipStep = (pageNum - 1) * pageSize
      if (total > skipStep && skipStep)
        results3 = results3.slice(skipStep);
      results3 = results3.slice(0, pageSize);
      return this.makeGResponseList(results3, pageNum, pageSize, total)
    }
    return this.makeGResponseListError()
  };

  addItchatGroupOfUser = addItchatGroupOfUser;

  servicesGetMyGroupTimers = async ({ account, data }: { account: EthAddress; data: any }) =>
    getMyGroupTimers(account, data);

  servicesAddGroupTimer = addGroupTimer;

  servicesAddGroupTimerPublic = addGroupTimerPublic;

  servicesAddGroupsTimer = async ({ sessionId, data }: { sessionId: string, data: any }) => {
    const sess = await this.checkSession(sessionId);

    return addGroupsTimer({ account: sess.account, data })
  }



  async servicesDelGroupTimer({ sessionId, data }: { sessionId: string, data: TIMER_CLIENT }) {
    const sess = await this.checkSession(sessionId);
    return delGroupTimer({ account: sess.account, data });
  }

  // servicesUpdateWxGroup = async ({
  //   account,
  //   agent,
  //   data,
  // }: {
  //   account: EthAddress;
  //   agent: EthAddress;
  //   data: MY_GROUP_INFO;
  // }) => {
  //   servicesUpdateWxGroupInfo({ account, agent, data });
  // };

  servicesDelMyWxGroup = async ({ account, sessionId, objectId, groupObjectId }:
    { account: EthAddress, sessionId: string, objectId: string, groupObjectId: string }):
    Promise<GResponse<boolean>> => {
    const sess = await this.checkSession(sessionId);
    if (sess.account !== account) throw new Error('account error');

    const groupPointer = {
      __type: 'Pointer',
      className: 'AIItchatGroups',
      objectId: groupObjectId,
    };

    const query = new ParseDB.Query(TABLE_NAME);

    query.notEqualTo('isDeleted', true);
    query.equalTo('objectId', objectId);
    query.equalTo('account', account);
    query.equalTo('refGroups', groupPointer);
    query.equalTo('agent', sess.agent);

    const record = await query.first();
    if (record) {
      record.set('isDeleted', true)
      await record.save();
      return this.makeGResponse(true);
    }
    return this.makeGResponse(false)
  };

  isGroupOwner2 = async (agent: EthAddress, groupObjectId: string, account: EthAddress) => {
    if (!groupObjectId) return undefined;

    const refGroups = {
      __type: 'Pointer',
      className: 'AIItchatGroups',
      objectId: groupObjectId,
    }

    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo("agent", agent);
    query.equalTo("refGroups", refGroups);
    query.equalTo("account", account);
    const result = await query.first();
    if (result) return result.toJSON()
    return undefined;
  };

  isGroupOwner3 = async (groupObjectId: string, userObjectId: string) => {
    if (!groupObjectId || !userObjectId) return false;
    const refGroups = {
      __type: 'Pointer',
      className: 'AIItchatGroups',
      objectId: groupObjectId,
    }
    const refUser = {
      __type: 'Pointer',
      className: 'Users',
      objectId: userObjectId,
    }

    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo("refGroups", refGroups);
    query.equalTo("refUser", refUser);
    const result = await query.first();
    if (result) return result.toJSON()

    const query2 = new ParseDB.Query("AIItchatGroups");
    query2.equalTo("objectId", groupObjectId);
    query2.equalTo("owner_ObjectId", userObjectId);
    const result2 = await query2.first();
    if (result2) return result2.toJSON()
    return undefined;

  }
}


export default new MyWxGroupsServices();

