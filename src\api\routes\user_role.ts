import { Router } from "express";
import { body } from "express-validator";
import validators from "../validators";
import * as userRoleController from "../../controllers/userRoleController";
import controller from "../../controllers/userRoleController";

const router: Router = Router();

export default (app: Router) => {
  app.use("/user_roles", router);

  // 获取角色列表
  router.get("/", userRoleController.getRoles);

  // 获取角色列表（带分页参数）
  router.get("/list/:pageNum/:pageSize",
    validators.paramNumberValidator("pageNum", 1, false, 100),
    validators.paramNumberValidator("pageSize", 1, false, 100),
    userRoleController.getRoles
  );

  // 获取单个角色
  router.get("/:objectId", validators.paramStringLengthValidator("objectId", 1, false), userRoleController.getRole);

  // 创建角色
  router.post("/create",
    [
      validators.bodyStringLengthValidator("roleName", 1, false, 50),
      validators.bodyStringLengthValidator("roleCode", 1, false, 20),
      validators.bodyStringLengthValidator("roleDesc", 0, true, 200),
      body("status").isIn(["启用", "禁用"]),
    ],
    userRoleController.createRole
  );

  // 更新角色
  router.put("/update/:objectId",
    [
      validators.paramStringLengthValidator("objectId", 1, false),
      validators.bodyStringLengthValidator("roleName", 1, false, 50),
      validators.bodyStringLengthValidator("roleCode", 1, false, 20),
      validators.bodyStringLengthValidator("roleDesc", 0, true, 200),
      body("status").optional().isIn(["启用", "禁用"]),
    ],
    userRoleController.updateRole
  );

  // 删除角色
  router.delete("/:objectId",
    validators.paramStringLengthValidator("objectId", 1, false),
    userRoleController.deleteRole
  );
  //--------------------------------------
  // 路由
  router.get("/route/getConstantRoutes", controller.getConstantRoutes);
  router.get("/route/getUserRoutes", controller.getUserRoutes);
  router.post("/route/uploadRoutes", controller.uploadRoutes);
  router.post("/route/saveRoutesToMenu", controller.saveRoutesToMenu);
  //--------------------------------------
  // 菜单
  router.get("/menu/getMenuList", controller.getMenuList);
  router.get("/menu/getAllPages", controller.getAllPages);
  router.post("/menu/create", [
    validators.bodyStringLengthValidator("menuName", 1, false, 50),
    validators.bodyStringLengthValidator("routeName", 1, false, 50),
    validators.bodyStringLengthValidator("routePath", 1, false, 100),
  ], controller.addMenu);
  router.put("/menu/update/:objectId", [
    validators.bodyStringLengthValidator("objectId", 1, false),
  ], controller.updateMenu);
  router.delete("/menu/delete/:objectId", validators.paramStringLengthValidator("objectId", 1, false), controller.deleteMenu);
  router.post("/menu/initMenu", controller.initMenu);
  //--------------------------------------
};
