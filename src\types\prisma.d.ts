import { PlatformBody } from '../services/platformServices';
import { EthAddress } from './types';
/* eslint-disable no-unused-vars */
/* eslint-disable no-use-before-define */

// export type Guild = {
//   id: number;
//   name: string;
//   urlName: string;
//   description: string | null;
//   imageUrl: string | null;
//   createdAt: Date;
//   showMembers: boolean;
//   hideFromExplorer: boolean;
// };

export type JsonValue = string | number | boolean | null | JsonObject | JsonArray;

export const LogicType: {
  AND: 'AND';
  OR: 'OR';
  NAND: 'NAND';
  NOR: 'NOR';
};
// eslint-disable-next-line no-redeclare
export type LogicType = (typeof LogicType)[keyof typeof LogicType];

export type JsonObject = { [Key in string]?: JsonValue };

/**
 * From https://github.com/sindresorhus/type-fest/
 * Matches a JSON array.
 */
export interface JsonArray extends Array<JsonValue> { }

export const Chain: {
  ETHEREUM: 'ETHEREUM';
  BSC: 'BSC';
  POLYGON: 'POLYGON';
  XDAI: 'XDAI';
  AVALANCHE: 'AVALANCHE';
  FANTOM: 'FANTOM';
  ARBITRUM: 'ARBITRUM';
  CELO: 'CELO';
  HARMONY: 'HARMONY';
  JUICEBOX: 'JUICEBOX';
  GOERLI: 'GOERLI';
  OPTIMISM: 'OPTIMISM';
  MOONRIVER: 'MOONRIVER';
  GNOSIS: 'GNOSIS';
};
// eslint-disable-next-line no-redeclare
export type Chain = (typeof Chain)[keyof typeof Chain];
export type RequirementType =
  | 'ERC20'
  | 'ERC721'
  | 'ERC1155'
  | 'COIN'
  | 'SNAPSHOT'
  | 'ALLOWLIST'
  | 'POAP'
  | 'MIRROR'
  | 'UNLOCK'
  | 'JUICEBOX'
  | 'FREE'
  | 'DISCORD_JOIN_FROM_NOW'
  | 'TWITTER_FOLLOWER_COUNT'
  | 'TWITTER_FOLLOW'
  | 'GUILD'
  | 'GUILD_ROLE'
  | 'GUILD_MINGUILDS'
  | 'GUILD_ADMIN'
  | 'GUILD_USER_SINCE'
  | 'CONTRACT';
export type Requirement = {
  objectId: string;
  id: number;
  type: RequirementType | null;
  address: string | null;
  symbol: string | null;
  name: string | null;
  chain: Chain;
  roleId: number;

  data: any | null;
};

export const ThemeMode: {
  LIGHT: 'LIGHT';
  DARK: 'DARK';
};
// eslint-disable-next-line no-redeclare
export type ThemeMode = (typeof ThemeMode)[keyof typeof ThemeMode];

export type Theme = {
  guildId: number;
  mode: ThemeMode;
  color: string | null;
  backgroundImage: string | null;
  backgroundCss: string | null;
};

// export type PlatformAccountDetails = PlatformAccount & {
//   platformUserId: string;
//   username: string;
//   avatar: string;
//   platformUserData?: Record<string, any>; // TODO: better types once we decide which properties will we store in this object on the backend
// };
type PlatformUserData = {
  acessToken?: string;
  scope?: string;
  expiresIn?: number;
  invalidToken?: boolean;
  refreshToken?: string;
  avatar?: string;
  username?: string;
  readonly?: boolean;
};
// type PlatformAccountDetails = {
//   platformType: number;
//   platformName: PlatformName;
//   platformUserId: string;
//   platformUserData?: PlatformUserData;
// };
export type PhoneInfo = {
  countryCode: string;
  phoneNumber: string;
  phoneVerified: boolean;
};
export type UserBase = {
  id?: number;
  ssoID: string;
  objectId?: string;
  userObjectId: string;// WxUserInfo 表中的用户id
  wxid?: string; // wcferry 微信ID,大部分情况唯一
  alias?: string; // 微信号,可确保唯一
  openid?: string; // 微信平台赋予公司的微信用户唯一ID


  name: string;
  nickName?: string;
  avatar: string;

  phoneInfo?: PhoneInfo;

  account: EthAddress;
  addresses: EthAddress[]; // 你可能需要更具体的类型
  cotaAddresses?: string[]; // 可能为 undefined
  publicKey: string;

  balanceAITokens: number;
  balanceScore: number;
  balanceCNY: number;

  email?: string
};

export type UserGender = 'man' | 'woman' | 'other' | undefined;
export type User = UserBase & {
  platformUsers?: Array<PlatformBody>;
  isSuperAdmin?: boolean;
  isDeleted?: boolean;
  status?: string;
  //------------------------------
  // 额外的用户信息
  gender?: UserGender; // 性别
  age?: number; // 年龄
  era?: string; // 时代
  country?: string; // 国家
  city?: string; // 城市
  region?: string; // 省份(区域)
  district?: string; // 区
  intro?: string; // 简介
  skill?: string; // 擅长
  other?: string; // 其他
  department?: string; // 科室,部门
  professionalPhoto?: string; // 职业照片(真实照片)
  professionalName?: string; // 职业名字(真实名字)
  //------------------------------
  permissions?: string[]; // SSO拥有的权限
  roles?: string[]; // SSO所属角色
  refPlats?: any[];//其他平台的用户

  telegramId?: string | null;
  discordId?: string | null;
  createdAt?: Date;
  pwd?: string; // 密码
  //------------------------------
  balanceAITokens: number; // 我的AI代币余额
  usedAITokens: number; // 我的AI代币消耗
  totalAITokens: number; // 我的AI代币总数
};
export type DoctorInfo = UserBase & {
  gender: string;
  age: number;
  country: string;
  intro: string;
  skill: string;
  other: string;
  department: string;
  professionalPhoto: string;
  professionalName: string;
};
