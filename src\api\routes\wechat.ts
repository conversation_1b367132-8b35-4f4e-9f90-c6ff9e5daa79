/**
 * 微信相关API路由
 * 
 * 提供微信功能的路由配置:
 * - 微信登录接口
 * - 用户信息接口
 * - 支付接口
 * - 群管理接口
 * - 消息通知接口
 * - 邀请码接口
 * 
 * <AUTHOR> <<EMAIL>>
 * @date 2024
 */

/* eslint-disable no-unused-expressions */
import { Router } from 'express';
import * as controller from '../../controllers/wechatController';
import validators from '../validators';

const router: Router = Router();

export default (app: Router) => {
  app.use('/wechat', router);

  router.get('/getopenid', controller.getWxOpenId);
  router.post('/getuserinfo', controller.getUserInfo);
  router.post('/get-user-profile', controller.getUserProfile);// 微信小程序获取用户信息按钮
  router.post('/get-user-phonenumber', controller.getUserPhoneNumber);// 微信小程序获取手机号按钮
  router.post('/get-pay-signature', controller.getPaySignature);
  router.post('/logout', controller.logout);

  router.get(
    '/itchat/groups/all/:pageNum/:pageSize',
    validators.headerStringLengthValidator('Session-Id', 4, false, 50),
    validators.headerStringLengthValidator('IKnow-Agent', 4, false, 50),
    validators.paramNumberValidator('pageNum', 0, false, 50),
    validators.paramNumberValidator('pageSize', 0, false, 50),
    controller.getItchatGroupsAll,
  );
  router.get('/itchat/user/info/:objectId', controller.getItchatUserInfoByObjectId)
  router.get('/itchat/user/list/:pageNum/:pageSize', controller.getItchatUserList);
  router.post('/itchat/user/get/:account', controller.getItchatUserInfo);
  router.post('/itchat/user/friends', controller.postUpdateFriends);
  router.post('/itchat/user/groups', controller.postUpdateGroups);

  router.post('/notice/:toUser', validators.postWxNotice(), controller.postWxNotice);
  router.post('/notice/groups/send', validators.postWxNoticeGrups(), controller.postWxNoticeGroups);
  // 微信群邀请码
  router.get('/wxgroups/invite-code/list/:groupObjectId', controller.getInviteCodeList);
  router.post('/wxgroups/invite-code/generate', controller.postGenerateInviteCode);
  router.post('/wxgroups/invite-code/use', controller.postUseInviteCode);
  router.delete('/wxgroups/invite-code/delete', controller.deleteInviteCode);
  router.get('/wxgroups/invite-code/all', controller.getInviteCodeAll);
  //-----------------------------------------------------
  // // 获取加入群消息列表(通用)
  // router.get('/group/join/msg/list', controller.getJoinGroupMsgList);
  // // 设置加入群消息列表(通用)
  // router.post('/group/join/msg/list', controller.postJoinGroupMsgList);
  //-----------------------------------------------------
};
