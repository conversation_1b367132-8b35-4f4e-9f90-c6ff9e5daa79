import { Request, Response } from "express";
import services from "../services/routes/userRoleServices";
import routeServices from "../services/routes/routeServices";
import menuServices from "../services/routes/menuServices";
import { controlFunc } from "./controller";

export const getRoles = async (req: Request, res: Response): Promise<void> => {
  // 优先使用URL参数，如果没有则使用查询参数
  const pageNum = req.params.pageNum ? Number(req.params.pageNum) :
    req.query.pageNum ? Number(req.query.pageNum) : 1;
  const pageSize = req.params.pageSize ? Number(req.params.pageSize) :
    req.query.pageSize ? Number(req.query.pageSize) : 20;

  return controlFunc(req, res, services.serviceGetRoles, {
    sessionId: req.headers['session-id'],
    pageNum,
    pageSize
  });
};

export const getRole = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceGetRole, {
    sessionId: req.headers['session-id'],
    objectId: req.params.objectId
  });

export const createRole = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceCreateRole, {
    sessionId: req.headers['session-id'],
    ...req.body
  });

export const updateRole = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceUpdateRole, {
    sessionId: req.headers['session-id'],
    objectId: req.params.objectId,
    ...req.body
  });

export const deleteRole = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceDeleteRole, {
    sessionId: req.headers['session-id'],
    objectId: req.params.objectId
  });

class UserRoleController {
  getConstantRoutes = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, routeServices.servicesGetConstantRoutes, {
      sessionId: req.headers['session-id']
    });
  };

  getUserRoutes = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, routeServices.servicesGetUserRoutes, {
      sessionId: req.headers['session-id']
    });
  };
  uploadRoutes = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, routeServices.servicesUploadRoutes, {
      sessionId: req.headers['session-id'],
      user: req.body?.user,
      routes: req.body?.routes
    });
  };

  // 将路由数据保存到菜单表中
  saveRoutesToMenu = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, routeServices.servicesSaveRoutesToMenu, {
      sessionId: req.headers['session-id'],
      routes: req.body?.routes
    });
  };
  //---------------------------------
  getMenuList = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, menuServices.servicesGetMenuList, {
      sessionId: req.headers['session-id']
    });
  };
  getAllPages = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, menuServices.servicesGetAllPages, {
      sessionId: req.headers['session-id']
    });
  };

  // 添加菜单
  addMenu = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, menuServices.servicesAddMenu, {
      sessionId: req.headers['session-id'],
      menuData: req.body
    });
  };

  // 更新菜单
  updateMenu = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, menuServices.servicesUpdateMenu, {
      sessionId: req.headers['session-id'],
      objectId: req.body.objectId,
      menuData: req.body
    });
  };

  // 删除菜单
  deleteMenu = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, menuServices.servicesDeleteMenu, {
      sessionId: req.headers['session-id'],
      objectId: req.params.objectId
    });
  };

  // 初始化菜单
  initMenu = async (req: Request, res: Response): Promise<void> => {
    controlFunc(req, res, menuServices.servicesInitMenu, {
      sessionId: req.headers['session-id']
    });
  };
  //---------------------------------
}

export default new UserRoleController();
