import { EthAddress, GResponse } from "../types/types";
import { isValidDate } from "../utils/date_tools";
import logger from "../utils/logger";
import { BaseServices } from "./baseServices";
import { getSkipNumber, ParseDB } from "./services";
import itchatWechatGroup from "./wechat/cowGroupClass";
import CacheService from "../utils/cacheService";
import { WxGroupInfo } from "./wechat/types";

// Cache configuration for stats
const STATS_CACHE_PREFIX = 'stats:';
const STATS_CACHE_DURATION = 30 * 60; // 30 minutes in seconds
const statsCache = new CacheService(STATS_CACHE_PREFIX, STATS_CACHE_DURATION);

// Intermediate table name for storing stats
const STATS_TABLE_NAME = 'BUF_AISystemStats';

type SystemStatistics = {
    // 用户统计
    totalUsers: number;           // 总用户数
    activeUsers: number;          // 活跃用户（本月有打卡）
    inactiveUsers: number;        // 非活跃用户（本月无打卡）
    noCheckInUsers: number;       // 从未打卡用户
    zombieUsers: number;          // 僵尸用户

    // 群组统计
    totalGroups: number;          // 总群组数

    // 打卡统计
    totalWeightLossUsers: number; // 减重打卡总人数
    totalCaloriesUsers: number;   // 卡路里打卡总人数

    // 系统统计
    totalSystemAlert: number;     // 系统报警总数
    totalSystemAlertToday: number;// 今日系统报警数
    totalWxMsgs: number;         // 微信消息总数
    totalWxMsgsToday: number;    // 今日微信消息数
}

type UsersCurveCount = {
    date: string;           // 日期，格式：YYYY-MM-DD
    totalUsers: number;     // 当日总用户数
    activeUsers: number;    // 当日活跃用户数
}

type SystemAlert = {
    id: string;
    groupWxid: string;
    groupOID: string;
    groupName: string;
    createdAt: Date;
    message: string;
    type: string;
    agent: string;
}

// 获取指定时间范围内的查询条件
const getTimeRangeQuery = (startTime: Date, endTime: Date) => ({
    greaterThan: ["createdAt", startTime],
    lessThan: ["createdAt", endTime]
});

// 获取今日时间范围
const getTodayRange = () => {
    const today = new Date();
    return {
        start: new Date(today.setHours(0, 0, 0, 0)),
        end: new Date(today.setHours(23, 59, 59, 999))
    };
};

// 基础查询构建器
const createBaseQuery = (tableName: string, agent: string) => {
    const query = new ParseDB.Query(tableName);
    query.equalTo("agent", agent);
    query.limit(1000000);
    return query;
};

// 获取消息统计:总消息数,今日消息
const getMessageStats = async (agent: string) => {
    const today = getTodayRange();
    const baseQuery = createBaseQuery(`AIGroupNotAtMsg_${agent}`, agent);

    const totalMsgs = await baseQuery.count();

    const todayQuery = createBaseQuery(`AIGroupNotAtMsg_${agent}`, agent);
    const { start, end } = today;
    todayQuery.greaterThan("createdAt", start);
    todayQuery.lessThan("createdAt", end);
    const todayMsgs = await todayQuery.count();

    return { totalMsgs, todayMsgs };
};

// 获取系统报警统计:总报警数,今日报警
const getAlertStats = async (agent: string) => {
    const today = getTodayRange();
    const baseQuery = createBaseQuery("NotificationWXGroupsMessages", agent);

    const totalAlerts = await baseQuery.count();

    const todayQuery = createBaseQuery("NotificationWXGroupsMessages", agent);
    const { start, end } = today;
    todayQuery.greaterThan("createdAt", start);
    todayQuery.lessThan("createdAt", end);
    const todayAlerts = await todayQuery.count();

    return { totalAlerts, todayAlerts };
};

// 获取打卡统计:减重打卡数,卡路里打卡数
const getCheckInStats = async (agent: string) => {
    const weightLossQuery = createBaseQuery("AI_WeightLoss", agent);
    const caloriesQuery = createBaseQuery("AIHealthCaloriesRecord", agent);

    const totalWeightLoss = await weightLossQuery.count();
    const totalCalories = await caloriesQuery.count();

    return { totalWeightLoss, totalCalories };
};

// 获取活跃用户数（本月有打卡）
const getActiveUsers = async ({ agent, body }: { agent: string, body: any }): Promise<number> => {
    try {
        const thisMonth = new Date(new Date().setMonth(new Date().getMonth() - 1));
        const weightLossQuery = createBaseQuery("AI_WeightLoss", agent);
        const caloriesQuery = createBaseQuery("AIHealthCaloriesRecord", agent);

        weightLossQuery.distinct("userName");
        weightLossQuery.greaterThan("createdAt", thisMonth);
        const weightLossUsers = await weightLossQuery.find();

        caloriesQuery.distinct("userName");
        caloriesQuery.greaterThan("createdAt", thisMonth);
        const caloriesUsers = await caloriesQuery.find();

        const activeUserSet = new Set([
            ...weightLossUsers.map(user => user.get("userName")),
            ...caloriesUsers.map(user => user.get("userName"))
        ]);

        return activeUserSet.size;
    } catch (error) {
        logger.error("getActiveUsers:", error);
        return 0;
    }
};

// 获取非活跃用户数（本月无打卡）
const getInactiveUsers = async ({ agent, body }: { agent: string, body: any }): Promise<number> => {
    try {
        const thisMonth = new Date(new Date().setMonth(new Date().getMonth() - 1));
        const allCheckedInUsers = new Set();
        const activeUsers = new Set();

        // 获取所有打卡用户
        const allWeightLossQuery = createBaseQuery("AI_WeightLoss", agent);
        const allCaloriesQuery = createBaseQuery("AIHealthCaloriesRecord", agent);
        allWeightLossQuery.distinct("userName");
        allCaloriesQuery.distinct("userName");

        const [weightLossUsers, caloriesUsers] = await Promise.all([
            allWeightLossQuery.find(),
            allCaloriesQuery.find()
        ]);

        weightLossUsers.forEach(user => allCheckedInUsers.add(user.get("userName")));
        caloriesUsers.forEach(user => allCheckedInUsers.add(user.get("userName")));

        // 获取活跃用户
        const activeWeightLossQuery = createBaseQuery("AI_WeightLoss", agent);
        const activeCaloriesQuery = createBaseQuery("AIHealthCaloriesRecord", agent);
        activeWeightLossQuery.distinct("userName");
        activeCaloriesQuery.distinct("userName");
        activeWeightLossQuery.greaterThan("createdAt", thisMonth);
        activeCaloriesQuery.greaterThan("createdAt", thisMonth);

        const [activeWeightLossUsers, activeCaloriesUsers] = await Promise.all([
            activeWeightLossQuery.find(),
            activeCaloriesQuery.find()
        ]);

        activeWeightLossUsers.forEach(user => activeUsers.add(user.get("userName")));
        activeCaloriesUsers.forEach(user => activeUsers.add(user.get("userName")));

        return Array.from(allCheckedInUsers).filter(user => !activeUsers.has(user)).length;
    } catch (error) {
        logger.error("getInactiveUsers:", error);
        return 0;
    }
};

// 获取从未打卡用户数
const getNoCheckInUsers = async ({ agent, body }: { agent: string, body: any }): Promise<number> => {
    try {
        const allUsersQuery = createBaseQuery("AIItchatUsers", agent);
        allUsersQuery.distinct("NickName");
        const allUsers = await allUsersQuery.find();

        const checkedInUsers = new Set();
        const weightLossQuery = createBaseQuery("AI_WeightLoss", agent);
        const caloriesQuery = createBaseQuery("AIHealthCaloriesRecord", agent);
        weightLossQuery.distinct("userName");
        caloriesQuery.distinct("userName");

        const [weightLossUsers, caloriesUsers] = await Promise.all([
            weightLossQuery.find(),
            caloriesQuery.find()
        ]);

        weightLossUsers.forEach(user => checkedInUsers.add(user.get("userName")));
        caloriesUsers.forEach(user => checkedInUsers.add(user.get("userName")));

        return allUsers.filter(user => !checkedInUsers.has(user.get("NickName"))).length;
    } catch (error) {
        logger.error("getNoCheckInUsers:", error);
        return 0;
    }
};

// 获取僵尸用户数（无打卡且无消息记录）
const getZombieUsers = async ({ agent, body }: { agent: string, body: any }): Promise<number> => {
    try {
        // 获取所有用户
        const allUsersQuery = createBaseQuery("AIItchatUsers", agent);
        allUsersQuery.distinct("NickName");
        const allUsers = await allUsersQuery.find();
        const allUserNames = new Set(allUsers.map(user => user.get("NickName")));

        // 获取所有有打卡记录的用户
        const weightLossQuery = createBaseQuery("AI_WeightLoss", agent);
        const caloriesQuery = createBaseQuery("AIHealthCaloriesRecord", agent);
        weightLossQuery.distinct("userName");
        caloriesQuery.distinct("userName");
        const [weightLossUsers, caloriesUsers] = await Promise.all([
            weightLossQuery.find(),
            caloriesQuery.find()
        ]);

        // 获所有有消息记录的用户
        const messageQuery = createBaseQuery(`AIGroupNotAtMsg_${agent}`, agent);
        messageQuery.distinct("userName");
        const usersWithMessages = await messageQuery.find();

        // 合并所有活跃用户（有打卡或有消息）
        const activeUserSet = new Set([
            ...weightLossUsers.map(user => user.get("userName")),
            ...caloriesUsers.map(user => user.get("userName")),
            ...usersWithMessages.map(user => user.get("userName"))
        ]);

        // 僵尸用户 = 所有用户 - 活跃用户
        const zombieCount = Array.from(allUserNames)
            .filter(userName => !activeUserSet.has(userName))
            .length;

        return zombieCount;
    } catch (error) {
        logger.error("getZombieUsers:", error);
        return 0;
    }
};

// 计算系统统计信息的具体实现
const calculateStats = async ({ agent, body }: { agent: string, body: any }): Promise<SystemStatistics> => {
    try {
        // 用户统计
        const activeUsers = await getActiveUsers({ agent, body });
        const inactiveUsers = await getInactiveUsers({ agent, body });
        const noCheckInUsers = await getNoCheckInUsers({ agent, body });
        const zombieUsers = await getZombieUsers({ agent, body });

        // 基础查询
        const userQuery = createBaseQuery("AIItchatUsers", agent);
        userQuery.distinct("NickName");
        const totalUsers = await userQuery.count();

        const groupQuery = createBaseQuery("AIItchatGroups", agent);
        groupQuery.distinct("groupNickName");
        const totalGroups = await groupQuery.count();

        // 获取消息和报警统计
        const { totalMsgs, todayMsgs } = await getMessageStats(agent);
        const { totalAlerts, todayAlerts } = await getAlertStats(agent);
        const { totalWeightLoss, totalCalories } = await getCheckInStats(agent);

        return {
            totalUsers,
            activeUsers,
            inactiveUsers,
            noCheckInUsers,
            zombieUsers,
            totalGroups,
            totalWeightLossUsers: totalWeightLoss,
            totalCaloriesUsers: totalCalories,
            totalSystemAlert: totalAlerts,
            totalSystemAlertToday: todayAlerts,
            totalWxMsgs: totalMsgs,
            totalWxMsgsToday: todayMsgs
        };
    } catch (error) {
        logger.error("calculateStats:", error);
        throw error;
    }
}

// 获取系统统计信息:
// 总用户数,活跃用户数,非活跃用户数,从未打卡用户数,僵尸用户数
// 总群组数,减重打卡数,卡���里打卡数,系统报警数,今日系统报警数,总消息数,今日消息数
const getStats = async ({ agent, body }: { agent: string, body: any }): Promise<SystemStatistics> => {
    try {
        // 首先尝试从缓存中获取统计数据
        const cacheKey = `${agent}:stats`;
        const cachedStats = await statsCache.get<SystemStatistics>(cacheKey);
        if (cachedStats) {
            logger.info(`从缓存中获取代理 ${agent} 的统计数据`);
            return cachedStats;
        }

        // 如果缓存中没有,则计算统计数据
        const stats = await calculateStats({ agent, body });

        // 将结果存入缓存
        await statsCache.set(cacheKey, stats);
        logger.info(`将代理 ${agent} 的统计数据存入缓存`);

        return stats;
    } catch (error) {
        logger.error("getStats:", error);
        throw error;
    }
}

const getStartAndEnd = (params: any): { startDate: Date, endDate: Date } => {
    const { start, end } = params;
    let startDate = new Date(Number(start));
    let endDate = new Date(Number(end));
    const minDate = new Date('2000-01-01');

    if (!isValidDate(startDate) || !isValidDate(endDate) || startDate < minDate || endDate < minDate) {
        startDate = new Date(new Date().setDate(new Date().getDate() - 7));
        endDate = new Date();
        logger.error("getStartAndEnd:", "开始时间或结束时间无效或早于2000年, 使用默认7天,从", startDate, "到", endDate);
    }
    return { startDate, endDate };
}

/**
 * 计算指定时间范围内的用户统计数据
 * @param agent 代理标识
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 每日用户统计数据数组
 */
const calculateStatsByTimeRange = async (
    agent: string,
    startDate: Date,
    endDate: Date
): Promise<UsersCurveCount[]> => {
    try {
        // 计算需要获取的天数
        const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

        // 尝试从中间表获取数据
        const query = new ParseDB.Query(STATS_TABLE_NAME);
        query.equalTo('agent', agent);
        query.greaterThanOrEqualTo('date', startDate);
        query.lessThanOrEqualTo('date', endDate);
        query.ascending('date');
        const storedStats = await query.find();

        // 检查数据是否完整并创建日期到统计数据的映射
        const statsMap = new Map<string, { totalUsers: number, activeUsers: number }>();
        storedStats.forEach(stat => {
            const dateStr = stat.get('date').toISOString().split('T')[0];
            statsMap.set(dateStr, {
                totalUsers: stat.get('totalUsers'),
                activeUsers: stat.get('activeUsers')
            });
        });

        // 如果中间表中有完整数据，直接使用
        if (statsMap.size > 0 && statsMap.size === daysDiff) {
            return storedStats.map(stat => ({
                date: stat.get('date').toISOString().split('T')[0],
                totalUsers: stat.get('totalUsers'),
                activeUsers: stat.get('activeUsers')
            }));
        }

        // 数据不完整，需要重新计算
        if (statsMap.size > 0) {
            logger.info(`中间表数据不完整，需要补全数据。现有 ${statsMap.size} 条，需要 ${daysDiff} 条`);
        }

        const dailyStats: UsersCurveCount[] = [];
        const savePromises: Promise<any>[] = [];

        // 获取在结束日期之前创建的所有用户
        const userQuery = createBaseQuery("AIItchatUsers", agent);
        userQuery.distinct("NickName");
        userQuery.lessThanOrEqualTo("createdAt", endDate);
        userQuery.ascending("createdAt");
        userQuery.select("NickName", "createdAt");
        const allUsers = await userQuery.find();

        // 处理用户创建日期数据
        const userCreationDates = new Map<string, number>();
        let runningTotal = 0;
        allUsers.forEach(user => {
            const createdAt = user.get("createdAt");
            if (createdAt) {
                const dateStr = new Date(createdAt).toISOString().split('T')[0];
                if (!userCreationDates.has(dateStr)) {
                    userCreationDates.set(dateStr, runningTotal);
                }
                runningTotal++;
            }
        });

        // 获取日期范围内的活动记录
        const [weightLossRecords, caloriesRecords] = await Promise.all([
            (async () => {
                const query = createBaseQuery("AI_WeightLoss", agent);
                query.greaterThanOrEqualTo("createdAt", startDate);
                query.lessThanOrEqualTo("createdAt", endDate);
                query.distinct("userName");
                query.select("userName", "createdAt");
                return query.find();
            })(),
            (async () => {
                const query = createBaseQuery("AIHealthCaloriesRecord", agent);
                query.greaterThanOrEqualTo("createdAt", startDate);
                query.lessThanOrEqualTo("createdAt", endDate);
                query.distinct("userName");
                query.select("userName", "createdAt");
                return query.find();
            })()
        ]);

        // 处理活跃用户数据
        const activeUsersByDate = new Map<string, Set<string>>();
        const processRecord = (record: any) => {
            const createdAt = record.get("createdAt");
            const userName = record.get("userName");
            if (createdAt && userName) {
                const dateStr = new Date(createdAt).toISOString().split('T')[0];
                if (!activeUsersByDate.has(dateStr)) {
                    activeUsersByDate.set(dateStr, new Set());
                }
                activeUsersByDate.get(dateStr)!.add(userName);
            }
        };

        weightLossRecords.forEach(processRecord);
        caloriesRecords.forEach(processRecord);

        // 生成每日统计数据
        const Stats = ParseDB.Object.extend(STATS_TABLE_NAME);
        let lastTotalUsers = 0;
        const currentDate = new Date(startDate);

        while (currentDate <= endDate) {
            const dateStr = currentDate.toISOString().split('T')[0];

            // 如果该日期的数据已存在于中间表中，使用已有数据
            const existingStat = statsMap.get(dateStr);
            if (existingStat) {
                dailyStats.push({
                    date: dateStr,
                    ...existingStat
                });
            } else {
                // 计算当天的统计数据
                const dayTotal = userCreationDates.get(dateStr);
                if (dayTotal !== undefined) {
                    lastTotalUsers = dayTotal;
                }
                const activeUsers = activeUsersByDate.get(dateStr)?.size || 0;

                // 创建统计对象
                const dailyStat = {
                    date: dateStr,
                    totalUsers: lastTotalUsers,
                    activeUsers
                };
                dailyStats.push(dailyStat);

                // 保存到中间表
                const statObject = new Stats();
                statObject.set('agent', agent);
                statObject.set('date', new Date(dateStr));
                statObject.set('totalUsers', lastTotalUsers);
                statObject.set('activeUsers', activeUsers);
                savePromises.push(statObject.save(null, { useMasterKey: true }));
            }

            currentDate.setDate(currentDate.getDate() + 1);
        }

        // 将新计算的数据保存到中间表
        if (savePromises.length > 0) {
            logger.info(`需要补充保存 ${savePromises.length} 条数据到中间表`);
            await Promise.all(savePromises);
        }

        return dailyStats;
    } catch (error) {
        logger.error("calculateStatsByTimeRange:", error);
        throw error;
    }
}

/**
 * 获取指定时间段内的用户统计数据，优先从缓存获取
 * 1. 先尝试从缓存获取数据
 * 2. 如果缓存没有，调用计算函数获取数据
 * 3. 将计算结果存入缓存
 * @param agent 代理标识
 * @param params 包含开始和结束时间的参数对象
 * @returns 用户统计数据数组
 */
const getStatsByTimeRange = async ({ agent, params }: { agent: string, params: { start: number, end: number } }): Promise<UsersCurveCount[]> => {
    try {
        const { startDate, endDate } = getStartAndEnd(params);

        // 根据代理和日期范围生成缓存键
        const cacheKey = `${agent}:${startDate.getTime()}:${endDate.getTime()}`;

        // 首先尝试从缓存获取数据
        const cachedData = await statsCache.get<UsersCurveCount[]>(cacheKey);
        if (cachedData) {
            logger.info(`从缓存中获取到代理 ${agent} 的统计数据`);
            return cachedData;
        }

        // 如果缓存中没有，计算统计数据
        const stats = await calculateStatsByTimeRange(agent, startDate, endDate);

        // 将结果存入缓存
        await statsCache.set(cacheKey, stats);
        logger.info(`将代理 ${agent} 的统计数据存入缓存`);

        return stats;
    } catch (error) {
        logger.error("getStatsByTimeRange:", error);
        throw error;
    }
}

// 获取系统报警信息列表
const getSystemAlertList = async ({ agent, pageNum, pageSize, isReadObjectIds, allReadTimestamp }
    : { agent: string, pageNum: number, pageSize: number, isReadObjectIds: string[], allReadTimestamp: string }):
    Promise<{ total: number, results: SystemAlert[] }> => {
    //   const { startDate, endDate } = getStartAndEnd(params);
    const alertQuery = createBaseQuery("NotificationWXGroupsMessages", agent);
    alertQuery.equalTo("type", "system");
    alertQuery.descending("createdAt");

    if (isReadObjectIds?.length) alertQuery.notContainedIn("objectId", isReadObjectIds)
    if (allReadTimestamp) {
        const start = new Date(allReadTimestamp);
        const end = new Date();
        alertQuery.greaterThan("createdAt", start);
        alertQuery.lessThan("createdAt", end);
    }

    const total = await alertQuery.count();
    alertQuery.skip(getSkipNumber(total, pageNum, pageSize));
    alertQuery.limit(pageSize);

    const alerts = await alertQuery.find();
    const alertPromises = alerts.map(async (alert, index) => {
        const groupWxid = alert.get("groupWxid");
        if (!groupWxid) {
            logger.error(`Alert ${alert.id} has no groupWxid`);
            return null;
        }
        const group: WxGroupInfo = { wxid: groupWxid, agent: agent as EthAddress, groupUserName: '', groupNickName: '', groupHeadImgUrl: '' };
        const groupInfo = await itchatWechatGroup.getWxGroupInfo(agent as EthAddress, group);
        return {
            objectId: alert.id,
            id: alert.id,
            agent,
            groupWxid: alert.get("groupWxid"),
            groupName: groupInfo?.groupNickName,
            groupOID: groupInfo?.objectId,
            createdAt: alert.get("createdAt"),
            message: alert.get("message"),
            type: alert.get("type")
        };
    });
    return { total, results: await Promise.all(alertPromises) };
}

class SystemInfoServices extends BaseServices {
    servicesGetStatsHome = async ({ sessionId, body }: { sessionId: string, body: any }): Promise<GResponse<SystemStatistics>> => {
        const sess = await this.checkSession(sessionId);
        if (!sess) throw new Error("Unauthorized");
        const data = await getStats({ agent: sess.agent, body });
        return this.makeGResponse(data);
    }

    // 获取指定时间段内容的总用户数据,活跃用户数据,用于绘制曲线
    servicesGetStatsByTimeRange = async ({ sessionId, params }: { sessionId: string, params: any }): Promise<GResponse<UsersCurveCount[]>> => {
        const sess = await this.checkSession(sessionId);
        if (!sess) throw new Error("Unauthorized");

        const data = await getStatsByTimeRange({ agent: sess.agent, params });
        return this.makeGResponse(data);
    }

    // 获取系统报警信息列表
    servicesGetAlerts = async ({ sessionId, pageNum, pageSize, isReadObjectIds, allReadTimestamp }
        : { sessionId: string, pageNum: number, pageSize: number, isReadObjectIds: string[], allReadTimestamp: string }) => {
        const sess = await this.isSuperMan(sessionId);
        if (!sess) throw new Error("Unauthorized");
        const { total, results } = await getSystemAlertList({ agent: sess.agent, pageNum, pageSize, isReadObjectIds, allReadTimestamp });
        return this.makeGResponseList(results, pageNum, pageSize, total);
    }
}

export default new SystemInfoServices();
export { getStats, getStatsByTimeRange, getSystemAlertList };