import { ParseDB } from "../../database/database";
import { EthAddress } from "../../types/types";
import ApiError from "../../utils/api_error";
import ApiSuccess from "../../utils/api_success";
import logger from "../../utils/logger";
import { shortenStringEnd } from "../../utils/utils";
import { BaseServices } from "../baseServices";
import services from "../users/userServices";
import { getWxGroupInfoByObjectId, isGroupOwner2 } from "./cowGroupFunction";
import { addOrUpdateWxUser, batchUpdateItchatUsers, getItchatUserInfo, getRecordJson, getWxUserByObjectId } from "./cowUserFunctions";
import onWechatApi, { WxMsgContent } from "./onWechatApi";

const TABLE_NAME = "AIItchatUsers"
class WxUserServices extends BaseServices {
    constructor() {
        super()
        this.TABLE_NAME = "AIItchatUsers"
    }

    // 发送消息到微信
    postWxNotice = async ({ toUser, body }: any) => {
        const { agent, payload, sessionId, wxMsgChannel } = body

        const sess = await this.checkSession(sessionId)
        const sender = await services.serviceGetUserDetails({
            account: sess.account,
        })

        const head = payload.isGroup ? `@${payload.toNickName}\n` : ""
        const message = shortenStringEnd(payload.message, 32)
        const tail = `\n${payload.url}`
        const doctorInfo = `来自: ${sender.professionalName}(${sender.department})`

        const toUserId = (await getItchatUserInfo(agent, {
            NickName: toUser,
            UserName: "",
            HeadImgUrl: ""
        }))?.UserName
        if (!toUserId) throw new ApiError("toUser not found", 404)
        const result = await onWechatApi.sendText(`${head}${message}\n${tail}\n${doctorInfo}`, toUserId, toUser, wxMsgChannel)
        if (result) {
            logger.info(`wexin Notice Send success to ${toUserId}`)
            return new ApiSuccess("ok")
        }

        return new ApiError("wexin Notice Send failed")
    };

    // 发送消息给多个群
    serviceWxNoticeGroups = async ({ agent, sessionId, body }:
        {
            agent: EthAddress, sessionId: string,
            body: {
                payload: {
                    groupObjectIds: Array<string>,
                    msg: WxMsgContent
                }
            }
        }) => {
        const { msg, groupObjectIds } = body.payload

        const sess = await this.checkSession(sessionId)
        const isSuperMan = await this.isSuperMan(sessionId);
        let groupWxids: Array<string> = [];
        if (!isSuperMan) {
            groupWxids = await Promise.all(groupObjectIds?.map(async (groupObjectId: string) => {
                const group = await isGroupOwner2(sess.agent, groupObjectId, sess.account)
                return group?.groupUserName || group?.groupId || group?.groupWxid
            }))

            if (!groupWxids || !groupWxids.length)
                throw new ApiError('无权限(群发群消息)');
        } else {
            groupWxids = await Promise.all(groupObjectIds?.map(async (groupObjectId: string) => {
                const group = await getWxGroupInfoByObjectId(groupObjectId)
                return group?.groupUserName || group?.wxid
            }))
        }
        groupWxids = groupWxids?.filter(wxid => wxid)
        if (!groupWxids || !groupWxids.length)
            throw new ApiError('微信群不存在');

        logger.info('sendMsgToGroups: ', groupWxids)
        switch (msg.type) {
            case "text":
            case "image":
            case "video_url":
            case "image_url":
            case "wx_link":
                {
                    const result = await onWechatApi.sendMsgToGroups(msg, groupWxids, false, agent)
                    if (result) {
                        logger.info(`wexin Notice Send success to ${groupObjectIds}`)
                        return new ApiSuccess("ok")
                    }
                    break;
                }
            default:
                throw new ApiError('不支持的消息类型', 400);
        }


        throw new ApiError("微信消息发送失败", 400)
    };

    // 查询用户,微信用户的 UserName 就是 id
    getItchatUserInfo = getItchatUserInfo;

    getWxUserInfo = getItchatUserInfo;

    getWxUserByObjectId = getWxUserByObjectId;

    getRecordJson = getRecordJson;

    addOrUpdateUser = addOrUpdateWxUser;

    servicesPostUpdateFriends = async (body: any) => {
        if (!body.NickName && !body.friends) {
            logger.error(`servicesPostUpdateFriends,没有friend:${JSON.stringify(body)}`)
            throw new ApiError("servicesPostUpdateFriends NickName not found", 404)
        }
        const { account: agent, friends } = body

        return batchUpdateItchatUsers(agent, friends)
    }

    servicesGetItchatUserInfo = (params: any) => {
        logger.info(`servicesGetItchatUserInfo:${JSON.stringify(params)}`)
        return getItchatUserInfo(params.agent, { account: params.account, ...params.user })
    }
    servicesGetItchatUserList = async ({ agent, sessionId, pageNum, pageSize, search }:
        { agent: EthAddress, sessionId: string, pageNum: number, pageSize: number, search: { NickName?: string, status?: string, startTime?: number, endTime?: number } }) => {
        const sess = await this.isSuperMan(sessionId)
        if (!sess) throw new ApiError("无权限", 401)
        logger.info(`servicesGetItchatUserList:${JSON.stringify(search)}`)

        const query = new ParseDB.Query(TABLE_NAME)
        query.equalTo('agent', agent)

        if (search?.NickName) query.matches('NickName', new RegExp(String(search.NickName), 'i'))
        if (search?.status) query.equalTo('status', search.status)
        if (search?.startTime) query.greaterThanOrEqualTo('createdAt', new Date(Number(search.startTime)))
        if (search?.endTime) query.lessThanOrEqualTo('createdAt', new Date(Number(search.endTime)))

        const total = await query.count()
        query.skip(this.getSkipNumber(total, pageNum, pageSize)).limit(pageSize)
        const results = await query.find()
        if (results) {
            const records = await Promise.all(results.map(result => getRecordJson(result, true)))
            return this.makeGResponseList(records, pageNum, pageSize, total)
        }
        return this.makeGResponseListError()

    }
}

export default new WxUserServices()