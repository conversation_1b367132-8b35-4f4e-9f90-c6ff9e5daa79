// deploy_ub.ts 需要在ubuntu下运行
// 运行脚本 ts-node deploy-api.js.mfull.cn.ts makedist
// 脚本中,可以设置多个服务器,用户选择服务器编号,发布到不同服务器

// cd /home/<USER>/bin-apf-srv/dist
// pm2 start src/index.js --name apf-srv-api

import * as fs from 'fs';
import * as path from 'path';
import { Client } from 'ssh2';
import * as sd from 'silly-datetime';

// 使用 require 导入模块，因为它们没有正确的类型定义
const archiver = require('archiver');

const args = process.argv.splice(2);
const isRollback = args.includes("rollback");
const makeDist = args.includes("makedist");
const test = args.includes("test");

// 定义发布目录名称，方便全局修改
const DIST_DIR_NAME = 'dist';

const privateKey = fs.readFileSync("/home/<USER>/.ssh/id_ecdsa");
//const privateKey = fs.readFileSync("C:/Users/<USER>/.ssh/id_ecdsa");
//const privateKey = fs.readFileSync("/Users/<USER>/.ssh/id_ecdsa");
//const privateKey = fs.readFileSync("C:\\Users\\<USER>\\.ssh\\id_ecdsa");

// 定义服务器配置类型
interface ServerConfig {
  name: string;
  host: string;
  port: number;
  username: string;
  privateKey: Buffer;
  pathUrl: string;
  pm2Name: string; // PM2 服务名称
}

// 远程服务器配置列表
const serverConfigs: ServerConfig[] = [
  {
    name: "渐随API服务器",
    host: "api.js.mfull.cn",
    port: 22,
    username: "jiansui",
    privateKey,
    pathUrl: "/home/<USER>/api-group-srv",
    pm2Name: "api-srv-jiansui",
  },
  {
    name: "Server-2",
    host: "**************",
    port: 22,
    username: "apf",
    privateKey,
    pathUrl: "/home/<USER>/bin",
    pm2Name: "api-srv-api",
  },
  {
    name: "Server-3",
    host: "**************",
    port: 22,
    username: "apf",
    privateKey,
    pathUrl: "/home/<USER>/bin",
    pm2Name: "api-srv-api",
  }
];

// 默认使用第一个配置
let config: ServerConfig = serverConfigs[0];
// 选择服务器配置
const selectServerConfig = (): Promise<ServerConfig> => {
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    console.log('\n请选择要连接的服务器:');
    serverConfigs.forEach((config, index) => {
      console.log(`${index + 1}. ${config.name} (${config.host})`);
    });
    console.log('直接回车将选择第一个服务器');

    readline.question('请输入服务器编号: ', (answer: string) => {
      readline.close();
      const index = parseInt(answer) - 1;

      if (isNaN(index) || index < 0 || index >= serverConfigs.length) {
        console.log(`选择默认服务器: ${serverConfigs[0].name} (${serverConfigs[0].host})`);
        resolve(serverConfigs[0]);
      } else {
        console.log(`已选择服务器: ${serverConfigs[index].name} (${serverConfigs[index].host})`);
        resolve(serverConfigs[index]);
      }
    });
  });
};

// 当前时间
const curTime = sd.format(new Date(), "YYYYMMDDHH");
const localZipFile = `${__dirname}/dist-${curTime}.zip`;
// remoteZipFile 将在选择服务器后设置
let remoteZipFile: string;
// 当前时间格式化
console.log((isRollback ? "回滚" : "部署") + "时间:" + curTime);

// 设置本地发布目录路径
const distPath = path.resolve(__dirname, DIST_DIR_NAME);

let ssh2: Client;

// 创建 SSH 连接函数
const createSSHConnection = (): Promise<Client> => {
  return new Promise((resolve, reject) => {
    const client = new Client();
    client.on("ready", () => {
      console.log("SSH 连接成功");
      resolve(client);
    }).on("error", (err) => {
      console.error("SSH 连接错误:", err);
      reject(err);
    });

    client.connect(config);
  });
};

// 添加目录检查逻辑
const checkRemoteDir = async () => {
  try {
    ssh2 = await createSSHConnection();
    await execCmd(`mkdir -p ${config.pathUrl}`);
    console.log(`确保远程目录 ${config.pathUrl} 存在`);
    return true;
  } catch (err) {
    console.error(`创建远程目录失败: ${err}`);
    return false;
  }
};

// 本地文件上传至远程服务器
async function uploadFile(filePath: string): Promise<void> {
  try {
    const dirExists = await checkRemoteDir();
    if (!dirExists) {
      console.error('远程目录创建失败，终止部署!');
      process.exit(1);
    }

    ssh2.connect(config);
    ssh2.on("ready", () => {
      console.log("SSH login success");
      ssh2.sftp(function (err: any, sftp: any) {
        if (err) {
          console.error("Error, problem starting SFTP: %s", err);
          console.error('启动 SFTP 失败，终止部署过程!');
          process.exit(2);
        }

        console.log("- SFTP started");

        // upload file
        // var readStream = fs.createReadStream(filePath);
        var writeStream = sftp.createWriteStream(remoteZipFile);

        // what to do when transfer finishes
        writeStream.on("close", function () {
          console.log("- file transferred");
          sftp.end();
          // 注意: 不在这里调用 remoteFileUpdate
        });

        // initiate transfer of file
        // readStream.pipe(writeStream)
        sftp.fastPut(
          filePath,
          remoteZipFile,
          {
            step: function (total_transferred: number, _chunk: any, total: number) {
              process.stdout.write(
                "\r" +
                "Total transferred: " +
                total_transferred +
                " out of " +
                total,
              );
            },
          },
          function (err: Error | null) {
            if (err) {
              console.error("\r\n文件传输失败:", err);
              console.error('终止部署过程!');
              process.exit(1);
            }

            console.log("\r\nFile transferred successfully!");
            remoteFileUpdate(); // 文件传输成功后进行远程更新
          },
        );
      });
    });
    ssh2.on("error", function (err: Error) {
      console.error("- connection error: %s", err);
      console.error('SSH 连接错误，终止部署过程!');
      process.exit(1);
    });
    ssh2.on("end", function () {
      console.log("ssh connection end");
    });
  } catch (err) {
    console.error('文件上传过程出错:', err);
    console.error('终止部署过程!');
    process.exit(1);
  }
}

const execCmd = (cmd: string): Promise<number> => {
  return new Promise((resolve, reject) => {
    ssh2.exec(cmd, (err: Error | null, stream: any) => {
      if (err) return reject(err);
      stream.on('close', (code: number, signal: string) => {
        if (code === 0) {
          resolve(1);
        } else {
          reject(new Error(`Command failed with code ${code}, signal ${signal}`));
        }
      }).on('data', (data: Buffer) => {
        process.stdout.write('STDOUT: ' + data);
      }).stderr.on('data', (data: Buffer) => {
        console.log('STDERR: ' + data);
      });
    });
  });
};
// 重启或创建 PM2 进程
const restartPm2Process = async (pm2AppName = config.pm2Name) => {
  try {
    // 检查进程是否存在
    const checkCmd = `cd ${config.pathUrl} && pm2 list | grep "${pm2AppName}"`;
    try {
      await execCmd(checkCmd);
      // 进程存在,执行重启
      await execCmd(`cd ${config.pathUrl} && pm2 restart ${pm2AppName}`);
      console.log(`PM2 进程 ${pm2AppName} 重启成功`);
    } catch (err) {
      // 进程不存在,创建新进程
      console.log(`PM2 进程 ${pm2AppName} 不存在,开始创建...`);
      const startCmd = `cd ${config.pathUrl}/${DIST_DIR_NAME} && pm2 start src/index.js --name ${pm2AppName} && pm2 save`;
      await execCmd(startCmd);
      console.log(`PM2 进程 ${pm2AppName} 创建成功`);
    }
  } catch (err) {
    console.error("PM2 操作失败:", err);
    throw err;
  }
};

// 远端文件更新
const remoteFileUpdate = async () => {
  const cmd = isRollback
    ? `cd ${config.pathUrl} && rm -rf ${DIST_DIR_NAME}  || true && mv ${DIST_DIR_NAME}.bak${curTime} ${DIST_DIR_NAME}  || true`
    : `cd ${config.pathUrl} && (rm -rf ${DIST_DIR_NAME}.bak${curTime} || true) && (mv ${DIST_DIR_NAME} ${DIST_DIR_NAME}.bak${curTime} || true) && unzip -o -u dist-${curTime}.zip`;

  let conn = new Client();
  conn.connect(config);
  conn.on("ready", async () => {
    console.log("SSH login success");
    try {
      // 备份老的dist,解压新的dist
      await execCmd(cmd);
      console.log("exec cmd success:", cmd);

      // 安装依赖
      const pnpmInstallCmd = `cd ${config.pathUrl}/${DIST_DIR_NAME} && pnpm install`;
      console.log(pnpmInstallCmd);
      await execCmd(pnpmInstallCmd);
      console.log("安装依赖成功!");

      // 拷贝最新配置文件和私钥
      const copyCmd = `cp ${config.pathUrl}/prod.dist.env ${config.pathUrl}/${DIST_DIR_NAME}/.env.development`;
      console.log(copyCmd);
      await execCmd(copyCmd);
      // 拷贝健康打卡提示文件
      const copyCmd2 = `cp ${config.pathUrl}/meal_checkin_prompt.txt ${config.pathUrl}/dist/`;
      console.log(copyCmd2);
      await execCmd(copyCmd2);
      // 拷贝健康检查提示文件
      console.log("拷贝健康检查提示文件...");
      const copyCmd3 = `cp ${config.pathUrl}/health_check_prompt.txt ${config.pathUrl}/dist/`;
      console.log(copyCmd3);
      await execCmd(copyCmd3);
      console.log("拷贝配置文件成功!");

      const copyKeys = `cp -R ${config.pathUrl}/keys ${config.pathUrl}/${DIST_DIR_NAME}/`;
      await execCmd(copyKeys);
      console.log("拷贝keys文件成功!");

      // 重启或创建 PM2 进程
      await restartPm2Process(config.pm2Name);
      console.log("部署全部完成!");
    } catch (err) {
      console.error("执行过程出错:", err);
      console.error("终止后续步骤的执行!");
    } finally {
      conn.end();
      process.exit(0);
    }
  });
};
// 本地文件压缩
const zipDirector = () => {
  try {
    console.log("zipDirector:");
    const zipFile = localZipFile;
    const output = fs.createWriteStream(zipFile);
    const archive = archiver("zip", { zlib: { level: 9 } }).on("error", (err: Error) => {
      console.error("archiver error:", err);
      console.error('压缩文件失败，终止部署过程!');
      process.exit(1);
    });

    output.on("close", (err: Error | null) => {
      if (err) {
        console.error("something error width the zip process:", err);
        console.error('压缩文件失败，终止部署过程!');
        process.exit(1);
      }
      console.log(`${archive.pointer()} total bytes`);
      console.log(
        "archiver has been finalized and the output file descriptor has closed.",
      );
      console.log("The zip file is ", `zipFile`);

      uploadFile(zipFile);
    });

    output.on("end", () => {
      console.log("Data has been drained");
    });

    archive.pipe(output);
    archive.directory(distPath, `./${DIST_DIR_NAME}`);
    archive.finalize();
  } catch (err) {
    console.error('压缩文件过程出错:', err);
    console.error('终止部署过程!');
    process.exit(1);
  }
};

const makeDistDir = () => {
  try {
    // 打包
    // 在函数内导入 execSync，避免全局导入未使用的警告
    const { execSync } = require("child_process");

    // 删除旧的发布目录
    console.log(`删除旧的${DIST_DIR_NAME}目录...`);
    execSync(`rm -rf ${DIST_DIR_NAME}`, { stdio: "inherit" });

    // 执行构建
    console.log('开始构建项目...');
    execSync("pnpm run build", { stdio: "inherit" });

    // 复制文件
    // 源文件数组
    const files = ["package.json", "tsconfig.build.json", ".env", "public"];
    // 目标目录
    const targetDir = DIST_DIR_NAME;
    // 拷贝命令
    const cmd = `cp -r ${files.join(" ")} ${targetDir}`;
    console.log(cmd);
    // 执行拷贝
    execSync(cmd, { stdio: "inherit" });
    console.log("拷贝完成!");

    // 检查文件是否存在,且文件创建时间在10分钟内
    const { statSync } = require("fs");
    const { mtime } = statSync(DIST_DIR_NAME);
    const diff = new Date().getTime() - new Date(mtime).getTime();
    if (diff > 10 * 60 * 1000) {
      throw new Error(`${DIST_DIR_NAME}文件创建时间超过10分钟`);
    }
    console.log(`${DIST_DIR_NAME}文件夹生成成功...`);
  } catch (err) {
    console.error(`创建${DIST_DIR_NAME}目录失败:`, err);
    console.error('终止部署过程!');
    process.exit(1); // 出错终止进程
  }
};
async function main() {
  console.log(
    "\t1. 部署: ts-node deploy_ub.ts \n \
    \t2. 回滚: ts-node deploy_ub.ts rollback \n \
    \t3. 编译: ts-node deploy_ub.ts makedist",
  );
  console.log("\t4. 安装依赖 pnpm install")
  console.log("\t5. 拷贝最新配置文件和私钥")
  console.log(`\t6. 重启 pm2 restart ${config.pm2Name}`)

  // 选择服务器配置
  config = await selectServerConfig();
  // 设置远程文件路径
  remoteZipFile = `${config.pathUrl}/dist-${curTime}.zip`;

  if (test) {
    console.log("test mode");
    remoteFileUpdate();
    return;
  }
  // 回滚代码
  if (isRollback) {
    remoteFileUpdate();
  } else {
    if (makeDist) makeDistDir();
    zipDirector();
  }
}

// # 编译并创建发布目录
// ts-node deploy_ub.ts makedist

// # 部署到服务器
// ts-node deploy_ub.ts

// # 回滚到之前的版本
// ts-node deploy_ub.ts rollback

// # 测试模式
// ts-node deploy_ub.ts test
main();
