import axios from "axios"
import { extractDate } from "../../utils/date_tools"
import logger from "../../utils/logger"
import { isValidString } from "../../utils/utils"
import { IWeightLossData } from "../health/weightLoss/weightLossTypes"
import GPTServerComm, { IGPTResponse, IWeightLossGPTResponse } from "./gptServerComm"


class GPTServerApiLinkAI extends GPTServerComm {

  // LINKAI 大模型
  async linkaiPostRequest<T>(userid: string, prompt: string): Promise<IGPTResponse<T>> {
    const apiKey = process.env.LINKAI_API_KEY
    const appCode = process.env.LINKAI_APP_CODE
    const url = process.env.LINKAI_CHAT_URL
    const system_prompt = process.env.SYSTEM_PROMPT || "你是一个医疗智能助手"

    logger.warn("====>开始调用 LINkAI post:", appCode, prompt)
    // 识别图片时，可以使用该模式：
    const json = [
      // 有appcode时,不需要system_prompt
      // {
      //   "role": "system",
      //   "content": system_prompt
      // },
      {
        role: "user",
        content: [
          {
            type: "text",
            text: prompt,
          },
        ],
      },
    ]

    const options = {
      method: "POST",
      url,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
      },
      data: {
        id: userid,//未确认
        user_id: userid,//未确认
        session_id: userid,//未确认
        app_code: appCode,
        messages: json,
        //question: prompt,
        frequency_penalty: -2,//设置为 -2 避免模型因频率惩罚而刻意改变输出，确保相同输入不会因重复性被调整。
        presence_penalty: -2, //惩罚已经出现过的概念或词汇。值越大，模型越倾向于引入新内容。
        temperature: 0,//将温度设为 0 会禁用随机性，模型将始终选择概率最高的输出，从而保证相同输入得到相同结果。
        top_p: 1,//当 temperature = 0 时，top_p 的值对输出影响较小，因为模型已经选择了最优路径。保持默认值 1 即可。
        stream: false,
        image_url: '',//图片url地址，需要进行图像识别时可传入，支持jpg、jpeg、png格式
        // model: "xunfei",
        // model: "wenxin"
        // 模型名称，非必填。目前支持 gpt-3.5-turbo, gpt-3.5-turbo-16k, gpt-4, wenxin(文心), wenxin(文心4.0), xunfei(讯飞3.0)
      },
    }

    try {
      const response = await axios.request(options)
      if (response.status === 200) {
        const jsonString = response?.data?.choices?.[0]?.message?.content
        logger.info("linkaiPostRequest===>:")
        const cleanedString = jsonString.replace(/'/g, '"') // 将字符串中的单引号替换为双引号
        const json = this.parseJson(cleanedString)
        return {
          model: "linkai",
          json,
          showText: cleanedString,
          aiTokens: response?.data?.usage?.total_tokens,
          completionTokens: response?.data?.usage?.completion_tokens,
          promptTokens: response?.data?.usage?.prompt_tokens,
        }
      }
      logger.error("linkaiPostReq:", response)
    } catch (error) {
      logger.error("linkaiPostReq 发生意外错误:", error)
    }
    return null
  }

  async linkaiWeightLoss(userid: string, inputStr: string): Promise<IWeightLossGPTResponse> {
    // 使用正则表达式和 replace() 方法删除空行
    const content: string = inputStr.replace(/^\s*[\r\n]/gm, "")
    logger.warn("====>开始调用大模型从文本获取减重信息:", inputStr, content)
    if (!isValidString(content) || content.length < 8) {
      logger.error("linkaiWeightLoss: 输入内容不合法")
      return null
    }
    let prompt = `'${content}'\n从上述内容中提取基本信息,通过JSON格式回复,不提供其他说明。\n- json每个元素是一个字典,包含"name"和"value","unit"三个键,"name","unit"为中文字符串，"value" 为数字.\n- 饮水,喝水的"name"为"喝水";\n- 睡眠时间的"name"为"睡眠";\n- 日期的name为"date",日期的输格式如下: "年-月-日"\n- 文本内容中日期格式支持:1. '年-月-日' 2. '月-日' 3. 年.月.日 4. 月.日;\n- 如果日期未提供则使用当前日期.\n- 所有元素都是字典,没有子元素和数组.`
    if (isValidString(process.env.HEALTH_PROMPT))
      prompt = process.env.HEALTH_PROMPT.replace("{content}", content)

    let jsonData

    // 调用大模型,分析减重 ------------------------
    const res = await this.linkaiPostRequest(userid, prompt)
    if (!res?.showText) {
      logger.error(`gptServerApi.getGptResult error: ${res}`)
      return null
    }
    const { showText: text, aiTokens, completionTokens, promptTokens } = res

    try {
      const jsonText = this.extractJson(text)
      if (jsonText) {
        logger.info("extractJson:", jsonText)
        jsonData = JSON.parse(jsonText)
      } else {
        jsonData = JSON.parse(text)
      }
    } catch (e) {
      logger.error("linkaiPostRequest error:", e)
    }
    // let sports;
    // if (jsonData.sports?.value) sports = jsonData.sports.value;
    // else sports = jsonData.sports || [];
    const date = extractDate(content)
    const weightLossData: IWeightLossData = {
      weight: this.searchValueByKey(jsonData, ["体重"]),
      water: this.searchValueByKey(jsonData, ["喝水", "饮水"]),
      sleep: this.searchValueByKey(jsonData, ["睡眠"]),
      aerobic: this.searchValueByKey(jsonData, ["有氧", "有氧运动"]),
      anaerobic: this.searchValueByKey(jsonData, ["无氧", "无氧运动", "阻抗", "阻抗运动"]),
      flexibility: this.searchValueByKey(jsonData, ["柔韧性", "柔韧性运行", "弹性", "弹性运动"]),
      model: "linkai",
    }
    return {
      model: "linkai",
      date: date ?? new Date(),
      aiResponse: weightLossData,
      aiJsonStr: text,
      aiJson: jsonData,
      aiTokens,
      completionTokens,
      promptTokens,
      targetWeight: this.searchValueByKey(jsonData, ["目标", "目标体重"]),
      bloodSugar: this.searchValueByKey(jsonData, ["血糖", "血糖值"]),
    }
  }

  async getUserSummary(userid: string, message: string) {
    try {
      const prompt = `对如下内容进行总结,直接返回结果,不要解释,其他内容不提供:"${message}"`
      const res = await this.linkaiPostRequest(userid, prompt)
      return res.showText
    } catch (error) {
      logger.error("Failed to summarize content:", error)
    }
    return null
  }


}

const gptLinkAI = new GPTServerApiLinkAI()
export default gptLinkAI
