import { Request, Response } from 'express';
import { Result, ValidationError, validationResult } from 'express-validator';
import { serviceGetGuildMemberships } from '../services/memberShipServices';

import friendServices from '../services/health/healthPatientsServices';
import consumeServices from '../services/users/consumeServices';
import followUpServices from '../services/users/followUpServices';
import groupServices from '../services/users/myWxGroupsServices';
import rechargeServices from '../services/users/rechargeServices';
import timerServices from '../services/timer/timerServices';
import watchingServices from '../services/users/watchingServices';

import services from '../services/users/userServices';
import wxGroupServices from '../services/wechat/cowGroupClass';


import { EthAddress, ValidateResult } from '../types/types';
import logger from '../utils/logger';
import { createErrorResponse } from '../utils/utils';
import { controlFunc } from './controller';
import { servicesGetWxGroupInfo, servicesGetWxGroupInfoBody } from '../services/wechat/cowGroupFunction';
import validators from '../api/validators';

const getUserGuildMemberships = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);

  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const userMemberships = await serviceGetGuildMemberships(req.params.address as EthAddress);

      res.status(200).json(userMemberships);
    } catch (error: any) {
      logger.warn(error.message);
      res.status(400).json(createErrorResponse(error.message));
    }
  }
};
const connect = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceUserConnect, req?.body);

const disconnect = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceUserDisConnect, req?.body);

const joinGuild = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const result = await services.serviceUserJoinGuild(req.body);

      res.status(result?.success ? 200 : 400).json(result);
    } catch (error: any) {
      logger.warn(error.message);
      res.status(400).json(createErrorResponse(error.message));
    }
  }
};
const leaveGuild = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const result = await services.serviceUserLeaveGuild(req.body);
      logger.info('==>leaveGuild: ', result);

      res.status(result?.success ? 200 : 400).json(result);
    } catch (error: any) {
      logger.warn(error.message);
      res.status(400).json(createErrorResponse(error.message));
    }
  }
};

const getUserUpvotyAuth = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const joinResponse: ValidateResult[] = [
        { roleId: 22, access: true },
        { roleId: 33, access: false },
      ];
      res.status(200).json(joinResponse);
    } catch (error: any) {
      logger.warn(error.message);
      res.status(400).json(createErrorResponse(error.message));
    }
  }
};

// TODO:
const getStatusUpdate = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      res.status(200).json({
        message: 'Status update',
        status: 'success',
        guildId: req.params.guildId,
        account: req.params.account,
      });
    } catch (error: any) {
      logger.warn(error.message);
      res.status(400).json(createErrorResponse(error.message));
    }
  }
};
const getUserBySSO = async (req: Request, res: Response): Promise<void> => {
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(400).json({ errors: errors.array() });
  } else {
    try {
      const user = await services.serviceGetUserBySSO(req?.params?.ssoID);
      res.status(200).json(user);
    } catch (error: any) {
      logger.warn(error.message);
      res.status(400).json(createErrorResponse(error.message));
    }
  }
};


// 管理员获取用户列表
const getUserListForAdmin = async (req: Request, res: Response) =>
  controlFunc(req, res, services.serviceGetUserList, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    sign: req.headers.sign, pageNum: req.params.pageNum || 1, pageSize: req.params.pageSize || 20
  });
const getUser = async (req: Request, res: Response) =>
  controlFunc(req, res, services.serviceGetUser, { sessionId: req.params?.sessionId, otherAccount: req?.params?.account });
const getDoctorList = async (req: Request, res: Response) =>
  controlFunc(req, res, services.servicesGetDoctorList, { sessionId: req.params?.sessionId, sign: req.params?.sign, });


const getUserDetails = async (req: Request, res: Response) =>
  controlFunc(req, res, services.serviceGetUserDetails, { ...req.body, account: req.params.account, });

const createUser = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceCreateUser, { address: req?.params?.account, body: req?.body || req.body?.payload });

const updateUser = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceUpdateUser, {
    sessionId: req.headers['session-id'],
    body: req.body?.payload || req.body
  })
//-------------------------------------
// friends timers
const getMyFriends = async (req: Request, res: Response) =>
  controlFunc(req, res, friendServices.servicesGetPaitents, { sessionId: req.params?.sessionId, sign: req.params?.sign, });

const addFriendTimer = async (req: Request, res: Response) =>
  controlFunc(req, res, friendServices.servicesAddFriendTimer, { account: req.params?.account, data: req.body?.payload, });

const getFriendTimers = async (req: Request, res: Response) =>
  controlFunc(req, res, friendServices.servicesGetFriendTimers, { account: req.params?.account, data: req.body?.payload, });

const delFriendTimer = async (req: Request, res: Response) =>
  controlFunc(req, res, friendServices.servicesDelFriendTimer, { account: req.params?.account, data: req.body?.payload, });
const getFriendKnowledge = async (req: Request, res: Response) =>
  controlFunc(req, res, friendServices.servicesGetFriendKnowledge, { account: req.params?.account, data: req.body?.payload, });
//----------------------------------------------
// my groups timers

// 获取我的所有群
const getMyWxGroups = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesGetMyGroups, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'] || req.params.sessionId,
    sign: req.params.sign,
    pageNum: req.params.pageNum,
    pageSize: req.params.pageSize
  });
// 获取我的所有群(同上)
const getMyWxGroupsOwn = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesGetMyGroupsOwn, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    sign: req.params.sign,
    pageNum: req.params.pageNum,
    pageSize: req.params.pageSize,
    search: req.query,
  });
// 获取我拥有的所有微信群的用户
const getMyWxUsersOwn = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesGetMyWxUsersOwn, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    sign: req.params.sign,
    pageNum: req.params.pageNum,
    pageSize: req.params.pageSize,
    search: req.query,
  });
// 获取我可以参与发图的微信群
const getMyWxGroupsOfJoined = async (req: Request, res: Response) =>
  controlFunc(req, res, wxGroupServices.serviceGetMyWxGroupsOfJoined, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    sign: req.params.sign,
    pageNum: Number(req.params.pageNum) || 1,
    pageSize: Number(req.params.pageSize) || 10,
    search: req.query,
  });
const inviteUserToWxGroups = async (req: Request, res: Response) =>
  controlFunc(req, res, wxGroupServices.servicesInviteUserToWxGroups, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    data: req.body?.payload,
  });
const getWxGroup = async (req: Request, res: Response) =>
  controlFunc(req, res, servicesGetWxGroupInfo, req.params);

const getWxGroupInfo = async (req: Request, res: Response) =>
  controlFunc(req, res, servicesGetWxGroupInfoBody, req.body?.payload);
const getWxGroupMembers = async (req: Request, res: Response) =>
  controlFunc(req, res, wxGroupServices.servicesGetWxGroupMembers, {
    sessionId: req.headers['session-id'],
    ...req.body?.payload
  });

// 获取我的定时器列表
const getMyTimers = async (req: Request, res: Response) =>
  controlFunc(req, res, timerServices.serviceGetMyTimerList, {
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
    pageSize: req.params.pageSize,
    pageNum: req.params.pageNum,
    search: req.query
  });
// 获取我们群定时器
const getMyGroupTimers = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesGetMyGroupTimers, {
    account: req.params?.account,
    data: req.body?.payload,
  });
// 为某个群添加定时器
const addGroupTimer = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesAddGroupTimer, {
    account: req.params?.account,
    data: req.body?.payload,
  });

// 为某个群添加公共定时器
const addGroupTimerPublic = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesAddGroupTimerPublic, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    account: req.body.params?.addr,
    data: req.body?.payload,
  });

const getGroupTimerPublic = async (req: Request, res: Response) =>
  controlFunc(req, res, timerServices.servicesGetGroupTimerPublic, {
    sessionId: req.headers?.authorization,
    groupObjectId: req.params?.groupObjectId,
  });

// 添加多个定时器,可一次发送给多个群
const addGroupsTimer = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesAddGroupsTimer, {
    sessionId: req.headers['session-id'],
    data: req.body?.payload,
  });
const delMyTimer = async (req: Request, res: Response) =>
  controlFunc(req, res, timerServices.servicesDelMyTimer, {
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
    timerObjectId: req.body?.payload.timerObjectId,
  });
const delGroupTimer = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesDelGroupTimer, {
    sessionId: req.params?.account,
    data: req.body?.payload
  });

const updateWxGroup = async (req: Request, res: Response) =>
  controlFunc(req, res, wxGroupServices.servicesUpdateWxGroupInfo, {
    account: req.body?.params?.addr,
    sessionId: req.headers['session-id'],
    agent: req.headers['iknow-agent'],
    data: req.body?.payload,
  });
const delMyWxGroup = async (req: Request, res: Response) =>
  controlFunc(req, res, groupServices.servicesDelMyWxGroup, {
    account: req.body?.params?.addr,
    sessionId: req.headers['session-id'],
    objectId: req.body?.payload?.objectId,
    groupObjectId: req.body?.payload?.groupObjectId,
  });
//--------------------------------------------------
// 随访信息管理
const getFollowUpList = async (req: Request, res: Response) =>
  controlFunc(req, res, followUpServices.servicesGetFollowUpList, {
    account: req.params?.account,
    pageNum: req.body?.payload?.pageNum,
    pageSize: req.body?.payload?.pageSize,
  });
const addFollowUpInfo = async (req: Request, res: Response) =>
  controlFunc(req, res, followUpServices.servicesAddFollowUp, {
    account: req.params?.account,
    content: req.body?.payload?.content,
    tag: req.body?.payload?.tag,
  });
const delFollowUpInfo = async (req: Request, res: Response) =>
  controlFunc(req, res, followUpServices.servicesDelFollowUp, {
    account: req.params?.account,
    objectId: req.body?.payload?.objectId,
  });
//--------------------------------------------------
// 关注用户信息管理
const getWatchingList = async (req: Request, res: Response) =>
  controlFunc(req, res,
    req.query.isWxGroup === 'true' ?
      watchingServices.servicesGetWatchingListGroups : watchingServices.servicesGetWatchingListUsers,
    {
      agent: req.headers['iknow-agent'],
      sessionId: req.headers['session-id'],
      pageNum: Number(req.params.pageNum) || 1,
      pageSize: Number(req.params.pageSize) || 10,
      isWxGroup: req.query.isWxGroup === 'true',
      search: req.query,
    });
const addWatching = async (req: Request, res: Response) =>
  controlFunc(req, res, watchingServices.servicesAddWatching, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    paylaod: req.body.payload
  });
const delWatching = async (req: Request, res: Response) =>
  controlFunc(req, res, watchingServices.servicesDelWatching, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    paylaod: req.body?.payload
  });
const isWatching = async (req: Request, res: Response) =>
  controlFunc(req, res, watchingServices.servicesIsWatching, {
    agent: req.headers['iknow-agent'],
    sessionId: req.headers['session-id'],
    objectId: req.params.objectId,
    isWxGroup: req.query.isWxGroup === 'true'
  });
//--------------------------------------------------
// AI Tokens
const consumeAiTokens = async (req: Request, res: Response) =>
  controlFunc(req, res, consumeServices.serviceConsumeAiTokens, {
    account: req.params?.account,
    data: req.body?.payload,
  });

const rechargeAiTokens = async (req: Request, res: Response) =>
  controlFunc(req, res, rechargeServices.serviceRechargeAiTokens, {
    account: req.params?.account,
    data: req.body?.payload,
  });
//--------------------------
export {
  addFollowUpInfo, addFriendTimer, addGroupTimer, addGroupTimerPublic, addGroupsTimer, addWatching, connect,
  //---------------------
  // AI Tokens 消费：
  consumeAiTokens, createUser, delFollowUpInfo, delFriendTimer, delGroupTimer, delMyTimer, delMyWxGroup, delWatching, disconnect,
  //--------------------------
  getDoctorList,
  //----------------------
  // 随访信息管理
  getFollowUpList, getFriendKnowledge, getFriendTimers, getGroupTimerPublic,
  //----------------------
  // friends
  getMyFriends, getMyGroupTimers,
  // 定时器
  getMyTimers,
  //----------------------
  // groups
  getMyWxGroups, getMyWxGroupsOfJoined, getMyWxGroupsOwn, getMyWxUsersOwn,
  getStatusUpdate, getUser,
  getUserBySSO,
  getUserDetails, getUserGuildMemberships,
  //--------------------------
  // user
  getUserListForAdmin, getUserUpvotyAuth,
  //---------------------
  // 关注用户
  getWatchingList, getWxGroup, getWxGroupInfo, getWxGroupMembers, isWatching, joinGuild,
  leaveGuild, rechargeAiTokens, updateUser, updateWxGroup, inviteUserToWxGroups
};

