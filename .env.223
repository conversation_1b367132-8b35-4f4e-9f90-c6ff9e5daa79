NODE_ENV=223
HOST_PORT=8988
LOGGER_LEVEL=info
# 223 测试库
#PARSE_SERVER_URL=http://**************:1338/parse
# 221 复制库
PARSE_SERVER_URL=http://**************:1339/parse

PARSE_APP_ID=nftweb2
PARSE_MASTER_KEY=123456aaa
PARSE_JAVASCRIPT_KEY=123456aaa

# HTTPS_PROXY=http://127.0.0.1:7890
# Oauth2.0
# <EMAIL>
# GOOGLE_CLIENT_ID=382855995820-opldgdcqn33q402aebpob6g4o3cj0iqn.apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=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
# akun.yunqi
GOOGLE_CLIENT_ID=1039972621727-dhcc16ve1n74g98hljj2qq99ftebp7aq.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REDIRECT_URL=http://localhost:5001/googleauth
# =====================
# y685296
GITHUB_CLIENT_MAP="[{"clientId":"********************","clientSecret":"214d1c75085c6d89cf77bcd3415fa726d5e852ba"},{"clientId":"42410bccfdb3bf739dec", "clientSecret":"8fdbc6ff7b3b2826a7c5f995ce538c922dcdcf79"}]"
GITHUB_CLIENT_ID=********************
GITHUB_CLIENT_SECRET=214d1c75085c6d89cf77bcd3415fa726d5e852ba
GITHUB_REDIRECT_URL=http://localhost:5001/oauth
# =====================
DISCORD_CLIENT_ID=1052964612221444228
DISCORD_CLIENT_SECRET=a8p5ATim5CaZ9AK4pSQ4QwiY0BJyxR4y
DISCORD_REDIRECT_URL=http://localhost:5001/oauth

DISCORD_BOT_USERNAME=GroupX
DISCORD_BOT_TOKEN=MTA1Mjk2NDYxMjIyMTQ0NDIyOA.GE_tGP.-AWQgcKRVy0hoVxdAxnwxf2hsdQdz7V3owG1sc
DISCORD_BOT_PERMISSIONS=268437556
# =====================
# TELEGRAM_APP_ID=16358088
# TELEGRAM_APP_HASH=357829ddb32507ed0f08c4215634f3f7
TELEGRAM_BOT_ID=5618179168
# TOKEN需要带上前面的id
TELEGRAM_BOT_TOKEN=**********************************************
# 5814738981:AAE1y_TS2TtLoNSipsLlulaag1mHHGmM14I
TELEGRAM_BOT_NAME=GroupNFTBot
# =====================
TWITTER_CLIENT_ID=dWNmVFBVQnU5RmtPRllsVnQ5N1U6MTpjaQ
TWITTER_CLIENT_SECRET=juTML8zgiLxCoNnlSDFKrRoVZ69CTCOGWqqNy2a4UVvlQA0KY7
TWITTER_REDIRECT_URL=https://470c-2409-8a28-863-ee70-00-7d.ngrok-free.app/oauth
TWITTER_BOT_ID=wew
TWITTER_BOT_TOKEN=es

# =====================
# bbs gate
# GATE_SSO_SERVER=https://door.bbs.mfull.cn
# GATE_SSO_ADMIN=user1
# GATE_SSO_ADMIN_PASSWORD=y1234567YHN#
# GATE_SSO_ADMIN_CLIENT_ID=0638d5b439b95c9b26d7
# GATE_SSO_ADMIN_CLIENT_SECRET=859f0076a637b109bca024fd0dc8a244b755a44c
# GATE_SSO_CLIENT_ID=daa4713de0111244e94a
# GATE_SSO_CLIENT_SECRET=8a7e6ec2199940c2b3852c9e4cd6e3c5f0619232

# GATE_SSO_APP_NAME=app_223
# GATE_SSO_ORG_NAME=org_iknowm
# GATE_SSO_CALLBACK=/callback
# GATE_SSO_PERMISSION_CHATLIST=org_groupX/permission_chatlist
# GATE_SSO_PERMISSION_CHATREPLY=org_groupX/permission_chat_reply
# GATE_SSO_PERMISSION_DOCTOR=org_groupX/permission_ai_doctor
# ==========================================
# 渐随
API_MPC_SERVER=https://mpc.js.mfull.cn/
GATE_SSO_SERVER=https://api.js.mfull.cn/

GATE_SSO_ADMIN=user_jiansui
GATE_SSO_ADMIN_PASSWORD=y1234567YHN#qw
GATE_SSO_ADMIN_CLIENT_ID=233107838fadb998117c
GATE_SSO_ADMIN_CLIENT_SECRET=79f8310ecc5de759e4ae7c6e15009cae43419613

GATE_SSO_CLIENT_ID=db0a442ccc01126bff00
GATE_SSO_CLIENT_SECRET=21d11496f908409208c6f0c4b3fc95d8c844ed9e

# 渐随小程序
GATE_SSO_CLIENT_SECRET_db0a442ccc01126bff00=21d11496f908409208c6f0c4b3fc95d8c844ed9e
#door.js.mfull.cn SSO 渐随web admin 登录 app_jiansui_admin
GATE_SSO_CLIENT_SECRET_9c77091410fb634e064b=b79e336fe9ca60d6b282719d568d75c271864fb2

GATE_SSO_APP_NAME=app_jiansui_miniapp
GATE_SSO_ORG_NAME=org_jiansui
GATE_SSO_CALLBACK=/callback
GATE_SSO_PERMISSION_CHATLIST=org_groupX/permission_chatlist
GATE_SSO_PERMISSION_CHATREPLY=org_groupX/permission_chat_reply
GATE_SSO_PERMISSION_DOCTOR=org_groupX/permission_ai_doctor
# ==========================================
# bbs gate **************
API_MPC_SERVER=http://**************:8006
GATE_SSO_SERVER=http://**************:8000
GATE_SSO_ADMIN=user1
GATE_SSO_ADMIN_PASSWORD=y1234567YHN#
GATE_SSO_ADMIN_CLIENT_ID=0c0f763352dbe246ace1
GATE_SSO_ADMIN_CLIENT_SECRET=0d6670fccdbdb5cfcfd01c5535ccb3696c6bd3be
GATE_SSO_CLIENT_ID=13e4e87be1854ea47bbe
GATE_SSO_CLIENT_SECRET=ebcfb8cf0358489fb2d798785b48b7a41b39dceb
# door.bbs.mfull.cn
GATE_SSO_CLIENT_SECRET_4cac6080b36662213794=5c7e8f6d0bfc45be44803f6fc476bfcd1aaa5d7d
# 满帆web app 及 iKnow服务号
GATE_SSO_CLIENT_SECRET_9aa34eaea44d3f80ce4c=513c7ae54f0847c7937e7fd1d0e98f6e8a58c878
#door.bbs.mfinfo.cn xuzijun
GATE_SSO_CLIENT_SECRET_c96df1729be83cf764e0=fc10ee84dec07e2ff099353eaa613a6ce6bf681f
# 9d8bf0e23c7d069c1e96
GATE_SSO_CLIENT_SECRET_9d8bf0e23c7d069c1e96=ced4b57309c72c3fe3f1d58006dc0350b2747497
#SSO 咸益小程序(正式)
GATE_SSO_CLIENT_SECRET_72b44e669572da44a963=7456b43bbc968425b27dd3d9ba259cd0
#SSO 渐随小程序(正式)
GATE_SSO_CLIENT_SECRET_daa4713de0111244e94a=8a7e6ec2199940c2b3852c9e4cd6e3c5f0619232
#SSO 渐随小程序(door-223)
GATE_SSO_CLIENT_SECRET_13e4e87be1854ea47bbe=ebcfb8cf0358489fb2d798785b48b7a41b39dceb
GATE_SSO_CLIENT_ID=daa4713de0111244e94a
GATE_SSO_CLIENT_SECRET=8a7e6ec2199940c2b3852c9e4cd6e3c5f0619232
GATE_SSO_APP_NAME=app_xuzijun
GATE_SSO_CALLBACK=/callback
GATE_SSO_PERMISSION_CHATLIST=org_groupX/permission_chatlist
GATE_SSO_PERMISSION_CHATREPLY=org_groupX/permission_chatlist
GATE_SSO_PERMISSION_DOCTOR=org_groupX/permission_ai_doctor
# ==========================================
# 共享免费的 Gate sso nft
GATE_SSO_NFT_COTAID=0xeab95c341a976ef943329c98887b8709600da339
# =====================
# nervod cota-sdk
NERVOS_CHAIN_TYPE=testnet
NERVOS_CKB_NODE_URL=https://testnet.ckbapp.dev/rpc
NERVOS_CKB_INDEXER_URL=https://testnet.ckbapp.dev/indexer
NERVOS_REGISTRY_URL=https://cota.nervina.dev/registry-aggregator
NERVOS_COTA_URL=https://cota.nervina.dev/aggregator
# 共享免费的 Nerfvos nft
NERVOS_NFT_COTAID=0xeab95c341a976ef943329c98887b8709600da339
# =====================
# weichat  正式
# WX_APPID=wxdda2bfed40889ed9
# WX_SECRET=a3981520dcf231e54419d5cb712d5665
# weichat  测试
WX_APPID=wx298297660860d110
WX_SECRET=64c133038eafbe01b7913886b45686c5
# 微信服务号(iKnowM)
# WX_APPID=wx454391c23c439801
# WX_SECRET=7754c938a5c2cf033cad3a23ca52fddb

#微信公众号


#小程序测试
# WX_MINI_APPID=wxe28489e10a06fa15
# WX_MINI_SECRET=5a62332cf13b14beb1553d56b642a5a2
#渐随小程序
WX_MINI_APPID=wx00ebae050141fbdc
WX_MINI_SECRET=1fa9bb149cd8ecfa7a08d4b9850ace8e
# =====================

IPFS_GATEWAY_SERVER=https://gateway.pinata.cloud/ipfs
# secret.akun.yunqi=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
# oauth2.0 client id = 1039972621727-dhcc16ve1n74g98hljj2qq99ftebp7aq.apps.googleusercontent.com
# =====================
ETHERSCAN_API_KEY=**********************************
BSCSCAN_API_KEY=**********************************
NODE_TLS_REJECT_UNAUTHORIZED=0

TENCENT_COS_SECRETID=AKID2qNefOq2xby4tte8MK0PaU7TYMuCDxQc
TENCENT_COS_SECRETKEY=wd3B2cx1nIWT9YgbGfnfGZdQwB9rHQKx
TENCENT_COS_BUCKET=iknowm-1257847067
TENCENT_COS_REGION=ap-nanjing
TENCENT_COS_EXT_WHITE_LIST="jpg,jpeg,png,gif,bmp,svg,mp4,doc,xls,xlsx,docx,pdf,txt,ppt,zip,csv"

API_ON_WECHAT_SERVER=http://**************:9092
API_ON_WECHAT_SERVER_0x7b9a3f5c2d8e1f4b9c6a0d3e8f7a1b2c4d5e6f=http://**************:9092
API_ON_WECHAT_SERVER_0xDb0a2D08A7217E7fb0eAD08FDF4d54d66e4365Ef=http://**************:9094
# 私钥去除头尾,且需要加双引号,因为内容中包含回车
API_ON_WECHAT_PRIVATE_KEY="MIICWwIBAAKBgQDcfFVNS6XTcim0XRqeJZ7nKRzssuoParVpujkpqbVIk1suMFVq\nB5oYfxhKo3LpPozWXQOMIxQUGKv0UchWLaGWYOvAk1g0f0wzyVqMjvqnCAQctASw\nZWXNtCU/335aIPnTzAwPS0+euSMK1xY2PaivwGXBZ8Vu/S5a1atOVrUlcwIDAQAB\nAoGAUgBT4Vl/JPLSm+f8nFC1lpdt0IKCFpXDPr0pwVsCtylGwhjry3FkWDP8ntXH\nSQQgcSFKznXFY+wBF+7KqXJzI7/Dar2+jrpTFrZjAWjTrQsDrM6j3DORtnoOJEWn\nNCDSkGoAEBFLAHJPOFIO+IGoe5BSfEtK/HlWWc2ajB0BxnECQQD0C3mu+h5cDII6\npmeMo0ImvG/McoWkWLPdP6IvTTAljkUTfkZMbjGDKDHhGU/T08hJ359Q8nwwzX4Z\neJBpViotAkEA50loFyAXxFMc/NHu5kQXRtC9ykK2qPVM+G/fu7V2Ro0iEm73+McR\nG1kIIH2OSWNVO1l9LGecOrFzQ7mlNTZyHwJAD43frpBQeQtvDW/nr6YEJFXkRkKS\nU/w3UoWov50K0Yn0yx5EOsDXNQXN0Av984FPBa5UCCO8WJvwSo1NnvkX7QJAK/HC\nmUI7wc2Y4GBy58VgNtBKfzeVxRx2d22qMNwVkOoX4zC6ZMZN9chAxwuUEVWSSCiE\no/87q9szb1bCkQ27OQJAY8KwILiP+Vkf+BfiNEY0dn4ec19FAfv8QJT4NS4edMsG\nOwSl6yBu40qTa6DrRCAOxohDQAl4C1yoXPrLMyxQDw=="
API_ON_WECHAT_NAME=groupx3

IKNOW_WEB_URL=http://**************:8080
# 大模型总开关
LINK_AI=true
LINKAI_API_KEY=Link_sdKK4dBiTBeadI933iv6c7JS7N6VC96a2gpdiq4epv
LINKAI_APP_CODE=NtObpL4d

LINKAI_CHAT_URL=https://api.link-ai.chat/v1/chat/completions
#带记忆能力,支持知识库,需要月付用户才支持
LINKAI_CHAT_URL2=https://api.link-ai.tech/v1/chat/memory/completions
LINKAI_HEALTH_PROMPT={content}\n从以上内容提取运动数据和身体基本信息,通过JSON格式回复,其他说明不要提供。\n- json每个元素是一个字典,包含"name"和"value","unit"三个键,"name","unit"为中文字符串，"value" 为数字,如果是时间,转换为分钟数;\n- 体重的"name"为"体重";\n- 饮水,喝水的"name"为"喝水";\n- 睡眠时间的"name"为"睡眠";\n- 所有运动相关的元素都归集到sports下,sports是数组\n- 其他元素都是字典,没有子元素
#"https://link-ai.tech/api/v1/chat/completions"; #无法访问
#"https://api.link-ai.chat/v1/chat/completions";

# 模型
# DIFY_API_URL=http://**************/v1
# DIFY_COMPLETION_URL=http://**************/completion-messages
# DIFY_CHAT_URL=http://**************/v1/chat-messages
# DIFY_API_CODE=11111111111111111111111111111111111111111111111111111111111111111
# DIFY_API_KEY=app-xeJ35xcDJAbKKBok0V0pBtzw

# https://cloud.dify.ai/app/43e817a4-d987-4a5b-8a72-0d24ed9cde79/develop
DIFY_API_URL=https://api.dify.ai/v1
DIFY_COMPLETION_URL=https://api.dify.ai/v1/completion-messages
DIFY_CHAT_URL=https://api.dify.ai/v1/chat-messages
DIFY_API_KEY=app-u8bOMw2MwjC8SIjIGS5wllu7


# 每日新闻
ALAPI_TOKEN=3ERHEO5rO0jbPsLL

SYSTEM_PROMPT="现在你是徐子军医疗团队,后续回答问题都请用'我们'代表你,你们是上海市第十人民医院,同济大大学附属第十人民医院,内分泌科生活方式医学管理中心,也是自然位法体态与代谢矫正技术发明团队,代谢性疾病早期逆转技术发起团队;专业特长:糖尿病前期、脂防肝逆转与管理脊柱侧弯、颈、腰椎痛复健、减重塑形、体态恢复与代谢提言;学会任职: 中国管理科学院职业技能培训师,中华医学会肿瘤影像技术青年委员会副主任委员,上海市医学会糖尿病学会糖尿病教育与管理学组委员';团队荣誉:上海市医学科技奖、上海市优秀发明奖获得者申请和授权专利23项，其中国家发明专利3项,国内外核心期刊及学术会议发表专业论文30余篇其中SCI收录4篇，主持参与上海市局级等课题共4项;"


QWEN_API_KEY=sk-a21db43518ae4cdba6eb854b19805558
QWEN_CHAT_URL=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation


# 每日新闻
ALAPI_TOKEN=3ERHEO5rO0jbPsLL

# 低于这个值，无法再使用AI功能
FLOOR_AI_TOKENS=-3001
# 提交公共月图片地址
IKONW_COMMON_IMG_PROMPT='点击链接分享图片：https://geth.mfull.cn/#/pages/act?a=nft&g={groupObjectId}'

WEIGHT_LOSS_RECORD_SUMMARY_MODEL=dify
HEALTH_GPT_MODEL=linkai
HEALTH_PROMPT="{content}\n从上述内容中提取基本信息,通过JSON格式回复,不提供其他说明。\n- json每个元素是一个字典,包含'name'和'value','unit'三个键,'name','unit'为中文字符串，'value' 为数字.\n- 饮水,喝水的'name'为'喝水';\n- 睡眠时间的'name'为'睡眠';\n- 日期的name为'date',日期的输格式如下: '年-月-日'\n- 读取文本内容中日期信息，包括:1. '年-月-日'\n2. 年.月.日\n- 如果日期未提供则date返回当前日期.\n- 所有元素都是字典,没有子元素和数组."
# 健康打卡
HEALTH_PROMPT_MEAL_CHECKIN_FILE='meal_checkin_prompt.txt'
#体检报告
HEALTH_PROMPT_CHECK_FILE='health_check_prompt.txt'
# 健康打卡群列表,该列表中的群，需要提取健康打卡数据
HEALTH_GROUP_LIST=['ALL_GROUP','**********', '**********','yhk-test6']
HEALTH_FOOD_CALORIE_CHECKIN_KEYWORDS="早餐,午餐,晚餐,早饭,午饭,晚饭,夜宵,下午加餐,晚上加餐"
HEALTH_CHECKIN_KEYWORDS=早起体重,早上体重,初始体重,代谢操,无氧运动,有氧运动,柔韧性运动,体重,喝水,睡眠,睡觉
#HEALTH_GROUP_NEED_REPLY_LIST=['3-11.2sdfas士大夫','**********', '**********','yhk-test6']
HEALTH_GROUP_NEED_REPLY_LIST=['ALL_GROUP','3-11.25杨医生减重管理','3-11.27李晓懿-减重管理','3-11.27减重管理','3-11.27李晓懿-减重管理','3-11.27张凌燕减肥群']
HEALTH_WORK_HOUR=8-23
HEALTH_CHECKIN_KEYWORDS_IGNORE=减重打卡关键字,减重打卡格式,减重,减重周报,减重月报,减重打卡
# 打卡卡路里催促开关
HEALTH_SPUR_CALORIES_PUNCH_SWITCH=false
# 打卡催促开关
HEALTH_SPUR_CHECKIN_SWITCH=false

DEEPSEEK_API_KEY2=bce-v3/ALTAK-w5fr0tHUr1kRvYskZW2CN/116f8c8497ef985d9d6cfcaf3e932c6899aac226
DEEPSEEK_API_KEY=bce-v3/ALTAK-uOF7ng9lHRlmjpzQm01Q4/b2925dd7a5e6d5162f776c9d366bc91bfb318f8c;ZjkyZmQ2YmQxZTQ3NDcyNjk0ZTg1ZjYyYjlkZjNjODB8AAAAABMCAAAjIHeRl5J6UVIS8m6DIjJPpSLR118m4iSWcu1+sOEtdwTpVPWWbt4GgYbK+pppbdUy9AGRgNKBLjFwHxV1MlGeg9+X8L8h5gnzKPNOeDPvVnpIDInHT3BK8vwPXBWu/dTmZUguZbzhZFz+EnT+Dh4knUUx0XD2sbqxkJTVdAVgT/mHhaIjbP2bKxpssOLuDXj4ZOfBJUlNLxZC+m8bri+4DCSELizR2nUf0SVDzmElKTBylC+DwdEI+2xne2J3Q7AyBGbOlugYC6/sVy5YuPN5PtLoeoZ7FsI6LQwjiCuSj7sQdjl6evIy/Ti2TJNCmGIbEY0klXZJhnx4mD+CBzY9W26GY//tuuYvpXmIlhRCvIH0Yn1K7Iqk4/NbYwS6mlWrfRRDB3rZI9JEFxZEnBideKIt36k3DPALBKvuQjLPO9UwQa9Hg4NUdjaYI4R26a0=
DEEPSEEK_MODEL=deepseek-v3

WEATHER_API_KEY=SPjKFwDL7FLe_epM5
WEATHER_API_URL=https://api.seniverse.com/v3/weather/daily.json

