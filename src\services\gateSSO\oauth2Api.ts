import axios from 'axios';
import { discord, github, telegram, twitter } from '../../config';
import { PlatformName } from '../../types/platformReward';
import { EthAddress, EthZero } from '../../types/types';

import { fetchDiscordUserinfo } from '../discord/discordServices';
import { fetchGithubOauthUserinfo } from '../github/githubServices';
import mpcServer<PERSON>pi from '../mpc/mpcServerApi';
import twitterServices from '../twitter/twitterServices';
import userServices from '../users/userServices';
import gateAdminApi from './gateSSOAdminApi';
import gateSSOApi from './gateSSOApi';
import { getLogger } from '../../utils/logger';

type OAuthUserInfo = {
  objectId: string; // Users中的objectId
  userObjectId: string; // 在Users中的wechat(itchatUser) user objectId
  userId: string;
  platformName: PlatformName;
  platformUserId: string;
  name: string;
  nickName: string;
  avatarUrl: string;
  email: string;

  wechat?: string; // wechat openid
  gateApp?: string; // sso gate 上的application名称

  account: EthAddress; // mpc api server的 ehtAddr
  sub: string; // gate sso 的 uuid

  permissions?: string[]; // 拥有的权限列表
  roles?: string[]; // 所属角色列表

  accessToken: string;
  refreshToken: string;
  expiresAt: any;
  // sessionId: string;
};
const logger = getLogger('oauth2Api');
const getClientSecret = (clientId: string) => {
  const clientSecret = process.env[`GATE_SSO_CLIENT_SECRET_${clientId}`];
  return clientSecret;
}
const fetchGateSSOUserByCode = async (code: string, agent: EthAddress, clientId: string, tag?: string): Promise<OAuthUserInfo> => {
  const clientSecret = getClientSecret(clientId);
  if (!clientSecret) {
    throw new Error(`不存在SSO ClientID对应的Secret(${clientId})`);
  }

  const token = await gateSSOApi.getAccessToken(code, clientId, clientSecret, tag);
  const gate = await gateSSOApi.getUser(token.access_token);

  const mpcUser = await mpcServerApi.oauthLoginGate(gate.name, token.access_token);
  if (!mpcUser || !mpcUser.user || !token.refresh_token || !gate.name) {
    logger.error('fetchGateSSOUserByCode error:', token, gate, mpcUser);
    throw new Error('mpcUser is null');
  }

  logger.info('mpcUser:', mpcUser);


  // 获取用户拥有的权限列表,判断用户角色
  const permissions: string[] = [];
  const perms = await gateAdminApi.getPermissions();
  if (perms) {
    perms.forEach((element: { name?: any; users?: any }) => {
      const { users } = element;
      if (users) {
        const isHave = users.includes(`${gate?.data?.owner}/${gate?.name}`);
        if (isHave) permissions.push(element.name);
      }
    });
  }

  // 获取用户拥有的角色列表
  const roles = []
  const rs = await gateAdminApi.getRoles();
  if (rs) {
    roles.push(
      ...rs
        .filter((element: { users: any }) => {
          const { users } = element;
          return users && users.includes(`${gate?.data?.owner}/${gate?.name}`);
        })
        .map((element: { name: any }) => element.name),
    );
  }

  // 如果用户不存在则创建，否则更新信息
  const user = await userServices.serviceCreateUser({
    address: mpcUser.user.ethAddr,
    body: {
      ssoID: gate.sub,
      openid: gate.data?.wechat,
      account: mpcUser.user.ethAddr,
      agent, // 默认agent，用于区分用户来自不同的系统和终端及渠道
      addresses: [mpcUser.user.ethAddr],
      avatar: gate.data.avatar,
      name: gate.name,
      nickName: gate.data.displayName,
      roles,
      publicKey: null,
      cotaAddresses: [],
      platformUsers: [
        {
          platformUserId: gate.sub,
          platformType: 6,
          platformName: 'GATESSO',
          platformUserData: gate.data,
          displayName: '',
          username: '',
          avatar: ''
        },
      ],
      // Add missing required properties from UserBase

      userObjectId: '',
      balanceAITokens: 30000,
      balanceScore: 0,
      balanceCNY: 0,
      usedAITokens: 0,
      totalAITokens: 0
    },
  });

  logger.info('fetchGateSSOUserByCode user:', user.account, user.name, gate?.data?.wechat);

  const userinfo: OAuthUserInfo = {
    ...user,
    objectId: user.objectId,
    userId: gate.sub,
    platformName: 'GATESSO',
    platformUserId: `${gate.data.owner}/${gate.name}`,
    name: gate.name,
    nickName: user.nickName,
    avatarUrl: gate.data.avatar,
    email: gate.data.email,

    wechat: gate.data?.wechat,
    gateApp: gate.data.signupApplication,

    sub: gate.sub,
    account: mpcUser.user.ethAddr, // mpc api server的 ehtAddr

    permissions, // 权限列表
    roles, // 角色列表

    accessToken: token?.access_token,
    refreshToken: token?.refresh_token,
    expiresAt: token?.expires_in,
    // sessionId: 'token?.session_id',
  };
  return userinfo;
};
const fetchDiscordOauthByCode = async (code: string) => {
  logger.info('fetchDiscordOauthByCode:', code, discord);
  const result = await axios.post(
    'https://discord.com/api/oauth2/token',
    {
      client_id: discord.clientId,
      client_secret: discord.clientSecret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: discord.redirectPath,
      scope: 'identify',
    },
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    },
  );
  if (result?.data?.error) {
    logger.error('token error:', result.data);
    throw new Error('discord token error');
  }
  const user = await fetchDiscordUserinfo(result?.data?.access_token);
  return {
    ...user,
    access_token: result?.data?.access_token,
    refresh_token: result?.data?.refresh_token,
    platformUserId: user?.data.username,
  };
};

const HttpsProxyAgent = require('https-proxy-agent');
const { google } = require('googleapis');

const fetchGetGoogleOauth2Userinfo = async (code: string): Promise<any> => {
  logger.info('https_proxy:', process.env.HTTPS_PROXY);
  if (process.env.HTTPS_PROXY) {
    google.options({
      agent: new HttpsProxyAgent(process.env.HTTPS_PROXY),
    });
  }

  // await getGoogleDoc("1KIKOaaA7-5y56DedhsrLXxztlOh1kw9KXm9RLh0fS1A/edit")
  // await getGmailList()

  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_REDIRECT_URL, // server redirect url handler
  );
  const googleToken = await oauth2Client.getToken(code);

  const accessToken = googleToken?.tokens?.access_token;
  // const refreshToken = tokens.refresh_token
  logger.info('==>localGetGoogleUserinfo accessToken: ', accessToken);
  await oauth2Client.setCredentials({
    access_token: accessToken,
  });
  const oauth2 = google.oauth2({ auth: oauth2Client, version: 'v2' });
  const userInfo = await oauth2.userinfo.get();
  logger.info('google userInfo:', userInfo?.data);
  return {
    ...userInfo?.data,
    tokens: googleToken?.tokens,
    platformUserId: userInfo?.email,
  };
};
const crypto = require('crypto');

function telegramCheckingAuthorization(authData: any, botToken: string) {
  const secret = crypto.createHash('sha256').update(botToken).digest();

  const dataKeys = Object.keys(authData);
  dataKeys.sort(); // key 按字母排序

  const len = dataKeys.length;
  let checkString = '';
  for (let i = 0; i < len; i += 1) {
    if (dataKeys[i] !== 'hash') {
      checkString += `${dataKeys[i]}=${authData[dataKeys[i]]}`;
      if (i !== len - 1) {
        checkString += '\n';
      }
    }
  }

  const checkHash = crypto.createHmac('sha256', secret).update(checkString).digest('hex');
  logger.info('telegramCheckingAuthorization', {
    check_string: checkString,
    check_hash: checkHash,
    authData,
  });
  return checkHash === authData.hash;
}
// 通过twitter oauth2 api 获取access_token
const fetchTwitterOauthByCode = async (code: string) => {
  logger.info('fetchTwitterOauthByCode input:', code, twitter);
  const result = await axios.post(
    'https://api.twitter.com/2/oauth2/token',
    {
      client_id: twitter.clientId,
      client_secret: twitter.clientSecret,
      code,
      grant_type: 'authorization_code',
      redirect_uri: twitter.redirectPath,
      scope: 'identify',
      code_verifier: 'challenge',
    },
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Basic ${Buffer.from(`${twitter.clientId}:${twitter.clientSecret}`).toString('base64')}`,
      },
    },
  );
  if (result?.data?.error) {
    logger.error('token error:', result.data);
    throw new Error('twitter token error');
  }
  logger.info('fetchTwitterOauthByCode result:', result?.data);
  const user = await twitterServices.fetchUserinfoByAccessToken(result?.data?.access_token);
  return {
    ...user,
    ...result?.data,
    access_token: result?.data?.access_token,
    refresh_token: result?.data?.refresh_token,
    platformUserId: result?.data?.username,
  };
};

// 通过telegram oauth2 api 获取access_token
const checkTelegramOauth = async (authData: any) => {
  logger.info('fetchTelegramOauthByCode', authData, telegram);
  const validate = telegramCheckingAuthorization(authData, telegram.botToken);
  if (validate) {
    return { ...authData, platformUserId: authData?.id };
  }
  logger.error('telegramCheckingAuthorization token error:', authData);
  throw new Error('telegram token error');
};
const fetchTelegramOauthByCode = async (authData: any) => {
  logger.info('fetchTelegramOauthByCode', authData, telegram);
  const validate = telegramCheckingAuthorization(authData, telegram.botToken);
  if (validate) {
    return { ...authData, platformUserId: authData?.id };
  }
  logger.error('telegramCheckingAuthorization token error:', authData);
  throw new Error('telegram token error');
};

type GithubVariable = {
  [key: string]: any;
};

const fetchGithubOauthByCode = async (code: string) => {
  logger.info('fetchGithubOauthByCode', code, github);

  const result = await axios.post(
    'https://github.com/login/oauth/access_token',
    {
      client_id: github.clientId,
      client_secret: github.clientSecret,
      code,
      // scope: "read:user,user:email,repo:invite,read:org",
      // scope: "repo,read:user,repo:invite,read:user", // = "repo:invite,read:user"
    },
    {
      headers: {
        Accept: 'application/json',
      },
    },
  );
  if (result?.data?.error) {
    logger.error('token error:', result.data);
    throw new Error('github token error');
  }
  const accessToken = result?.data?.access_token;
  const refreshToken = result?.data?.refresh_token;
  const user = await fetchGithubOauthUserinfo(accessToken);
  return {
    ...user?.data,
    access_token: accessToken,
    refresh_token: refreshToken,
    platformUserId: user?.data.username,
  };
};

const fetchOauthByCode = (code: string, platform: PlatformName, clientId?: string, tag?: string,agent): OAuthUserInfo => {
  const fetchFunc: { [key: string]: any } = {
    GATESSO: fetchGateSSOUserByCode,
    DISCORD: fetchDiscordOauthByCode,
    GITHUB: fetchGithubOauthByCode,
    TWITTER: fetchTwitterOauthByCode,
    TELEGRAM: fetchTelegramOauthByCode,
    NERVOS: fetchGateSSOUserByCode,
    GOOGLE: fetchGetGoogleOauth2Userinfo,
  };
  return fetchFunc[platform](code, agent, clientId, tag);
};
export default { fetchOauthByCode };
export { checkTelegramOauth, fetchOauthByCode, OAuthUserInfo };

