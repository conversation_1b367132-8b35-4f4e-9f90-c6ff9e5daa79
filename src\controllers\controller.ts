import { Request, Response } from 'express';
import { Result, ValidationError, validationResult } from 'express-validator';
import ApiError from '../utils/api_error';
import logger from '../utils/logger';

type IKnowReferer = {
  referer: string | string[];
  userAgent: string;
  realIp: string | string[];
};
const getClientReferer = (req: Request): IKnowReferer => {
  const { ip } = req;
  const forwardedHeaders = req.headers['x-forwarded-for'];

  const realIp = forwardedHeaders || ip || req.connection?.remoteAddress;

  const userAgent = req.headers['user-agent'];
  const referer = req.headers.referer || req.headers.referrer;

  // logger.info(`getClientReferer: ${realIp}, ${referer}, ${userAgent}`);
  return { referer, userAgent, realIp };
};

const controlFunc = async (
  req: Request,
  res: Response,
  callFunc: (params: any, referer: IKnowReferer) => any,
  params: any = undefined,
): Promise<void> => {
  const startTime = process.hrtime();
  const funName = callFunc.name || req.originalUrl;
  const errors: Result<ValidationError> = validationResult(req);
  if (!errors.isEmpty()) {
    const errorsString = errors
      .array()
      .map(err => `${err.location}:${err.msg} - ${err.param}`)
      .join(', ');
    logger.error('controlFunc err:', funName, errorsString);
    res.status(400).json({ errors: errorsString });
  } else {
    const referer = getClientReferer(req);
    try {
      //logger.info(`===>controlFunc:${req.method} ${req.originalUrl} ${JSON.stringify(referer)} `)
      const data = await callFunc(params, referer);
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const executionTime = seconds * 1000 + nanoseconds / 1000000; // 转换为毫秒

      // 检查执行时间是否超过3秒（3000毫秒）
      if (executionTime > 3000) {
        logger.warn(
          `⚠️ SLOW EXECUTION WARNING: took ${executionTime.toFixed(2)}ms to execute! URL: ${req.method} ${req.originalUrl
          }`,
        );
      }

      if (!data) {
        logger.error(`===>耗时 [${executionTime.toFixed(2)}]ms ${funName} 返回无Data`);
      } else if (Array.isArray(data)) {
        logger.info(`===>耗时 [${executionTime.toFixed(2)}]ms ${funName} 返回数组:${data.length} ${data?.[0]}`);
      } else if (typeof data === 'string') {
        logger.info(`===>耗时 [${executionTime.toFixed(2)}]ms ${funName} 返回字符串:${data}`);
      } else {
        logger.info(`===>耗时 [${executionTime.toFixed(2)}]ms ${funName} 返回Keys: ${Object.keys(data).join(', ')}`);
      }
      res.status(200).json(data);
    } catch (error: any) {
      const [seconds, nanoseconds] = process.hrtime(startTime);
      const executionTime = seconds * 1000 + nanoseconds / 1000000; // 转换为毫秒

      // 错误情况下也检查执行时间
      if (executionTime > 3000) {
        logger.warn(
          `⚠️ SLOW EXECUTION WARNING (with error): took ${executionTime.toFixed(2)}ms ${callFunc?.name} to fail! URL: ${req.method
          } ${req.originalUrl}`,
        );
      }

      logger.error(`===>controlFunc catch err:${req.method} ${req.originalUrl} `);
      logger.error('controlFunc catch err:', {
        func: callFunc?.name,
        params: req.params,
        body: req.body,
        query: req.query,
        message: error?.message,
      });
      logger.error('controlFunc catch err2:', error);

      if (error instanceof ApiError) {
        res.status(200).json({
          ...error,
          errors: error.message,
          message: error.message,
          msg: error.message,
          fail: error.fail,
        });
        return;
      }
      if (error?.message)
        res.status(error.code || 400).json({
          ...error,
          code: error.code || 400,
          message: error.message,
          msg: error.message,
          fail: error.fail,
        });
      else res.status(204); // 错误，但是没有返回内容
    }
  }
};

// eslint-disable-next-line import/prefer-default-export
export { controlFunc, IKnowReferer };
