/**
 * 体检报告生成与识别流程说明
 * 
 * 1. 用户上传体检报告图片，系统通过OCR识别提取文本内容（ocrResult）。
 * 2. 系统从OCR识别结果中提取关键信息（如姓名），并生成唯一哈希值用于去重。
 * 3. 体检报告原始数据（ocrResult等）保存到AIHealthReportRecord表。
 * 4. 当满足条件时，系统会将同一用户的多条体检报告记录合并，调用AI（如GPT）生成健康总结。
 * 5. AI生成的健康总结（aiResponse）保存到AIHealthReport表，并标记相关记录已生成报告。
 * 6. 用户可通过接口查询个人体检报告及AI总结。
 * 
 * 主要功能：
 * - addOrSaveHealthReport：保存原始体检报告记录，自动去重。
 * - processMakeHealthReportByGPT：合并多条记录，调用AI生成健康总结。
 * - getPersonName：从OCR文本中提取姓名。
 * - servicesGetReport/ReportList：获取体检报告及AI总结。
 */
import { ParseDB } from '../../database/database';
import { EthAddress, GResponse } from '../../types/types';
import { getLogger } from '../../utils/logger';
import { calculateHash } from '../../utils/utils';
import { BaseServices } from '../baseServices';
import gpt from '../gpt/gptServerApi';
import { IGPTResponse } from '../gpt/gptServerComm';

const logger = getLogger('healthRPT');
// 每份体检报告生成一个总结(多条记录合并生成一条)
const TABLE_NAME = 'AIHealthReport';
// 体检报告记录表
const TABLE_NAME_RECORD = 'AIHealthReportRecord';
type IHealthReportSummary = {
    summary: string;
    recommendations: string[];
    otherHealthIndicators: string[];
    // 其他健康指标
    [key: string]: any; // 其他可能的健康指标
};

type IHealthReport = {
    objectId?: string;
    personName: string; // 体检报告的姓名
    createdAt: Date;
    updatedAt: Date;
    agent: EthAddress;
    wxGroupOID: string;
    wxUserOID: string;
    reportType: string;
    aiSummary: IGPTResponse<IHealthReportSummary>;// ai分析结果
    pageTotal: number;// 完整页数
    pageCount: number;//已经获得的页数
};
type IHealthReportRecord = {
    objectId?: string;
    createdAt?: Date;
    updatedAt?: Date;
    hash?: string; // 数据的哈希值
    agent: EthAddress;
    wxGroupOID: string;
    wxUserOID: string;
    account: EthAddress;
    reportType: string;
    ocrResult?: string;
    aiResponse?: string;
};
const healthReportConst = {
    "嘉定区华亭镇社区卫生服务中体检报告": {
        pageTotal: 6
    }
}
// 获取数据的哈希值
const getHashFromData = (data: IHealthReportRecord): string => {
    const { wxGroupOID, wxUserOID, account, reportType, ocrResult, agent } = data;
    return calculateHash(`${wxGroupOID}-${wxUserOID}-${account}-${reportType}-${ocrResult}-${agent}`);
}
// 使用AI生成体检报告,可异步完成
const processMakeHealthReportByGPT = async ({ userObjectId, reportType, agent, personName }:
    { userObjectId: string, reportType: string, agent: EthAddress, personName: string }): Promise<IHealthReport> => {
    if (!userObjectId || !reportType || !agent) {
        throw new Error('Missing required fields: userObjectId, reportType, or agent');
    }
    const query = new ParseDB.Query(TABLE_NAME_RECORD);
    query.equalTo('wxUserOID', userObjectId);
    query.equalTo('reportType', reportType);
    query.equalTo('agent', agent);
    query.notEqualTo('isGenReport', true);
    query.descending('createdAt');
    query.limit(20); // 限制查询数量，避免过多数据

    const results = await query.find();
    if (results.length === 0) {
        console.warn('No health reports found for this user and report type.');
        throw new Error('No health reports found for this user and report type.');
    }
    let content = '';
    let pageCount = 0;
    const pageTotal = healthReportConst[reportType]?.pageTotal;
    // 合并多条记录的OCR结果
    for (const result of results) {
        const ocrResult = result.get('ocrResult') || '';
        if (ocrResult) {
            content += `OCR Result,index:${pageCount} content: ${ocrResult}\n`;
            pageCount++;
        }
    }
    // 调用AI生成报告
    const aiResponse = await gpt.makeHealthReport(userObjectId, content, false);
    if (!aiResponse) {
        console.warn('AI response is empty, cannot generate health report.');
        throw new Error('AI response is empty, cannot generate health report.');
    }
    // 更新记录为已生成报告
    for (const result of results) {
        result.set('isGenReport', true);
        await result.save();
    }

    const aiSummary: IHealthReportSummary = {
        model: aiResponse.model,
        showText: aiResponse.showText,
        aiTokens: aiResponse.aiTokens,
        completionTokens: aiResponse.completionTokens,
        promptTokens: aiResponse.promptTokens,
        summary: aiResponse.json?.summary || '',
        recommendations: aiResponse.json?.recommendations || [],
        otherHealthIndicators: aiResponse.json?.otherHealthIndicators || []
    };
    // 保存生成的报告
    const HealthReportObject = ParseDB.Object.extend(TABLE_NAME);
    const healthReport = new HealthReportObject();
    healthReport.set('personName', personName)
    healthReport.set('wxUserOID', userObjectId);
    healthReport.set('reportType', reportType);
    healthReport.set('agent', agent);
    healthReport.set('aiSummary', aiSummary);
    healthReport.set('wxGroupOID', results[0].get('wxGroupOID')); // 使用第一条记录的群组ID
    healthReport.set('pageCount', pageCount);
    healthReport.set('pageTotal', pageTotal)
    const savedReport = await healthReport.save();

    if (!savedReport) {
        console.warn('Failed to save health report.');
        throw new Error('Failed to save health report.');
    }
    return {
        objectId: savedReport.id,
        personName: savedReport.get('personName') || personName,
        createdAt: savedReport.createdAt,
        updatedAt: savedReport.updatedAt,
        wxGroupOID: savedReport.get('wxGroupOID'),
        wxUserOID: savedReport.get('wxUserOID'),
        reportType: savedReport.get('reportType'),
        agent: savedReport.get('agent'),
        aiSummary: savedReport.get('aiSummary'),
        pageCount: savedReport.get('pageCount'),
        pageTotal: savedReport.get('pageTotal')
    }
}
// 从OCR识别结果中获取姓名,一般格式如下: 姓名：翟秀芳 或者 姓名:翟秀芳
const getPersonName = (ocrResult: string) => {
    if (!ocrResult) return '';
    // 匹配“姓名：xxx”或“姓名:xxx”，支持中英文冒号和可选空格
    const match = ocrResult.match(/姓名[:：]?\s*([\u4e00-\u9fa5A-Za-z]+)/);
    return match ? match[1] : '';
}
const addOrSaveHealthReport = async (data: IHealthReportRecord) => {
    const { wxGroupOID, wxUserOID, account, reportType, ocrResult, aiResponse, agent } = data;
    if (!wxGroupOID || !wxUserOID || !account || !agent) {
        throw new Error('Missing required fields: wxGroupOID, wxUserOID, account, or agent');
    }
    const hash = getHashFromData(data);
    // 是否重复提交
    const query = new ParseDB.Query(TABLE_NAME_RECORD);
    query.equalTo('hash', hash);
    const result = await query.first();
    if (result) {
        const data = await getHealthReport({ agent, userObjectId: wxUserOID, reportType });
        let text = `当前页体检报告已存在,请勿重复提交. `
        if (data) {
            text += `\n已经提交${data.pageCount}页(共${data.pageTotal}).`
        }
        console.warn(text);
        throw new Error(text);
    }
    const personName = getPersonName(ocrResult)
    const HealthReportObject = ParseDB.Object.extend(TABLE_NAME_RECORD);
    const healthReport = new HealthReportObject();
    healthReport.set('wxGroupOID', wxGroupOID);
    healthReport.set('wxUserOID', wxUserOID);
    healthReport.set('account', account);
    healthReport.set('reportType', reportType || 'default');
    healthReport.set('ocrResult', ocrResult || '');
    healthReport.set('aiResponse', aiResponse || '');
    healthReport.set('agent', agent);
    healthReport.set('hash', hash);
    healthReport.set('personName', personName);

    // 保存体检报告
    const result2 = await healthReport.save();
    // 创建报告
    if (result2) processMakeHealthReportByGPT({ userObjectId: wxUserOID, reportType, agent, personName })
    return result2
}
const getHealthReportJson = (record: Parse.Object): IHealthReport => record ? {
    objectId: record.id,
    personName: record.get('personName') || '',
    createdAt: record.createdAt,
    updatedAt: record.updatedAt,
    wxGroupOID: record.get('wxGroupOID'),
    wxUserOID: record.get('wxUserOID'),
    reportType: record.get('reportType') || 'default',
    agent: record.get('agent'),
    aiSummary: record.get('aiSummary') || {},
    pageTotal: record.get('pageTotal') || 0,
    pageCount: record.get('pageCount') || 0
} : undefined;

const getHealthReport = async (data: { agent: EthAddress, userObjectId: string, reportType: string }): Promise<IHealthReport | null> => {
    if (!data.userObjectId) {
        throw new Error('Missing required parameter: userObjectId');
    }
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('wxUserOID', data.userObjectId);
    query.equalTo('agent', data.agent);
    query.equalTo('reportType', data.reportType);
    query.descending('createdAt');

    const srcCount = await query.count();

    const result = await query.first();
    return getHealthReportJson(result);
}
// 体检报告
class HealthReport extends BaseServices {
    private report: string;
    // 构造函数
    constructor() {
        super();
        this.report = '';
    }

    // 获取体检报告
    servicesGetReport = async ({ sessionId, userObjectId, reportType }:
        { sessionId: string, userObjectId: EthAddress, reportType: string }): Promise<GResponse<IHealthReport>> => {
        if (!sessionId || !userObjectId || !reportType) {
            throw new Error('Missing required parameters: sessionId, userObjectId, reportType, or healthReportId');
        }
        const sess = await this.isAdminOrMySelfOID(sessionId, userObjectId);
        const data = await getHealthReport({ agent: sess.agent, userObjectId, reportType })
        if (!data) return this.makeGResponse(undefined, 404, `未找到体检报告.`);

        return this.makeGResponse(data);
    }

    // 获取体检报告列表
    servicesGetReportList = async ({ sessionId, reportType, account, pageNum: page, pageSize }:
        { sessionId: string, reportType: string, account: EthAddress, pageNum: number, pageSize: number }) => {
        const sess = await this.isAdminOrMySelf(sessionId, account);
        const query = new ParseDB.Query(TABLE_NAME_RECORD);
        query.equalTo('account', account);
        query.equalTo('agent', sess.agent);
        query.equalTo('reportType', reportType);
        const srcCount = await query.count();
        if (srcCount === 0) {
            return this.makeGResponse({
                code: 404,
                message: `未找到体检报告.${reportType}`
            });
        }
        query.descending('createdAt');
        query.equalTo('isGenReport', true);
        const total = await query.count();
        // 分页处理
        query.skip(this.getSkipNumber(total, page, pageSize));
        query.limit(pageSize);
        const results = await query.find();
        return this.makeGResponseList(results, page, pageSize, total, 200, `已经上传${srcCount}条记录`);
    }

    // 清空体检报告
    clearReport(): void {
        this.report = '';
    }

    // 输出体检报告
    printReport(): void {
        if (this.report) {
            console.log('Health Report:');
            console.log(this.report);
        } else {
            console.log('No health report available.');
        }
    }
}

export const healthReportServices = new HealthReport();
export type { IHealthReport, IHealthReportSummary };
export { addOrSaveHealthReport, processMakeHealthReportByGPT }