import { fetcherGate } from '../../utils/fetcher';
import logger from '../../utils/logger';
import GateSS<PERSON><PERSON><PERSON><PERSON><PERSON> from './gateSSOBaseApi';

class GateSSOAdminApi extends GateSSOBase<PERSON>pi {
  // 获取管理员的登录码
  getAdminCode = async () =>
    this.getLoginCode(
      {
        application: 'app-built-in',
        username: process.env.GATE_SSO_ADMIN,
        password: process.env.GATE_SSO_ADMIN_PASSWORD,
        autoSignin: true,
        type: 'code',
      },
      process.env.GATE_SSO_ADMIN_CLIENT_ID,
    );

  // 获取管理员令牌
  getAdminAccessToken = async (code: string) =>
    this.getAccessToken(code, process.env.GATE_SSO_ADMIN_CLIENT_ID, process.env.GATE_SSO_ADMIN_CLIENT_SECRET);

  /**
   * 刷新管理员令牌
   * 1. 如果令牌过期,则重新获取令牌
   * 2. 如果令牌未过期,则不刷新
   * 3. 如果令牌不存在,则获取令牌
   * 4. 令牌过期时间为token.expires_in * 60 * 1000
   */
  async refreshAdminToken() {
    try {
      const now = new Date();
      if (!this.token?.access_token || now > this.tokenExpiresAt) {
        const code = await this.getAdminCode();
        this.token = await this.getAdminAccessToken(code);
        // 分钟*60*1000 = 毫秒
        this.tokenExpiresAt = new Date(Date.now() + this.token.expires_in * 60 * 1000);
        logger.info('admin login tokenExpiresAt--------------:', this.tokenExpiresAt);
      }
    } catch (e) {
      logger.error('Gate SSO Admin Token Refresh Failed:', e);
    }
  }

  // 获取指定的权限,返回权限拥有的users
  getPermission = async (permission: string) => {
    try {
      await this.refreshAdminToken();

      const result = await fetcherGate(`/api/get-permission?id=${permission}&accessToken=${this.token.access_token}`);
      if (result && result.users) {
        // logger.info('getPermission success!', permission, result.users);
        return result.users;
      }
      logger.error('Gate SSO Get Permission Failed:', result);
      return null;
    } catch (e) {
      logger.error('Gate SSO Get Permission Failed2:', e);
      return null;
    }
  };

  // 获取所有的权限,返回权限列表
  getPermissions = async () => {
    await this.refreshAdminToken();

    const results = await fetcherGate(`/api/get-permissions?accessToken=${this.token.access_token}`);
    if (results) {
      logger.info('getPermission admin login success!', results);
      return results.data || results;
    }
    logger.error('Gate SSO Get Permissions Failed', results);
    return null;
  };

  // 获取所用户信息
  // userid: owner/name
  getUserInfo = async (userid: string) => {
    await this.refreshAdminToken();

    const result = await fetcherGate(`/api/get-user?id=${userid}`);
    if (result) {
      // logger.info(`getUserInfo success! ${result.name}, ${result.wechat}, ${result.id}`);
      return result.data || result;
    }
    logger.error('getUserInfo Failed', result);
    return null;
  };

  /** *
   * 获取所有的角色(roles),返回角色列表
   * 1. 如果角色不存在,则创建角色
   * 2. 如果角色存在,则返回角色
   */
  getRoles = async () => {
    await this.refreshAdminToken();

    const results = await fetcherGate(`/api/get-roles?accessToken=${this.token.access_token}`);
    if (results) {
      logger.info('getRoles admin login success!', results);
      return results.data || results;
    }
    throw new Error('Gate SSO Get Roles Failed');
  };

  checkPermission = async (userid: string, permission: string): Promise<boolean> => {
    try {
      const users = await this.getPermission(permission);
      return !!users.includes(userid);
    } catch (e) {
      logger.error('checkPermission:', permission, userid, e);
      return false;
    }
  };
}
export default new GateSSOAdminApi();
