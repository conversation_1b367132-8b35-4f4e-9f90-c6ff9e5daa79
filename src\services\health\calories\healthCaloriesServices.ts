/**
 * 卡路里摄入管理服务
 * 
 * 主要功能：
 * 1. 卡路里摄入记录
 *    - 识别并提取消息中的卡路里数值
 *    - 记录用户的卡路里摄入情况
 *    - 计算每日卡路里摄入总量
 * 
 * 2. 卡路里目标管理
 *    - 设置每日卡路里摄入目标
 *    - 跟踪目标完成情况
 * 
 * 3. 数据统计与报告
 *    - 支持日/周/月维度的卡路里摄入报告
 *    - 计算平均摄入量
 *    - 分析与目标的偏差
 * 
 * @class HealthCaloriesServices
 * @extends {HealthBaseServices}
 */
import moment from 'moment';
import { ParseDB } from "../../../database/database";
import { IWeightLossRecords } from "../weightLoss/weightLossTypes";

import { EthAddress } from "../../../types/types";
import logger from "../../../utils/logger";
import { isValidString } from '../../../utils/utils';
import wxGroupServices from '../../wechat/cowGroupClass';

import { IFood, IMealType } from '../../../types/health';
import { extractDate } from '../../../utils/date_tools';
import { INotAtMsg } from '../../chatlist/groupNotAtMsgTypes';
import { getItchatUserInfo } from '../../wechat/cowUserFunctions';
import { HealthBaseServices } from "../base/healthBaseServices";
import { AIRespNutrition } from './types';
import { refreshUserCaloriesCard } from './healthCaloriesPlan';
import { AIRespCaloriesJson, HealthCaloriesRecord, HealthCaloriesRequest } from './types';
import { IGPTResponse } from '../../gpt/gptServerComm';
import { makeWxGroupRef } from '../../wechat/cowGroupFunction';

const TABLE_NAME = 'AIHealthCaloriesRecord'

class HealthCaloriesServices extends HealthBaseServices {
    constructor() {
        super()
        // 设置表名
        this.TABLE_NAME = TABLE_NAME
    }

    getRecordJson = (record: ParseDB.Object): HealthCaloriesRecord => record ? {
        objectId: record.id,
        agent: record.get('agent'),
        account: record.get('account'),
        activityDate: record.get('activityDate'),
        userName: record.get('userName'),
        groupName: record.get('groupName'),
        mealType: record.get('mealType'),
        foodItems: record.get('foodItems'),
        totalCaloriesToday: record.get('totalCaloriesToday'),
        targetCalories: record.get('targetCalories'),
        totalScore: record.get('totalScore'),
        scoreDetails: record.get('scoreDetails'),
        imageFoods: record.get('imageFoods'),
        refWxUser: record.get('refWxUser'),
        refWxGroup: record.get('refWxGroup'),

        aiRespCalories: record.get('aiRespCalories'),
        aiRespNutrition: record.get('aiRespNutrition'),
        aiRespSummary: record.get('aiRespSummary'),
        aiRespSuggestion: record.get('aiRespSuggestion'),
        aiRespOther: record.get('aiRespOther'),
        aiRespTotalCalories: record.get('aiRespTotalCalories'),

    } : null;

    /**
     * 获取N天前到今天的records
     * @param agent 
     * @param userObjectId 
     * @param groupObjectId 
     * @param n 
     * @returns 
     */
    getLastNDaysRecords = async (agent: string, userObjectId: string | undefined, groupObjectId: string, n: number): Promise<IWeightLossRecords> => {
        const startDate = moment().subtract(n, 'days').startOf('day').toDate(); // 获取起始时间（00:00:00.000）
        const endDate = moment().endOf('day').toDate(); // 获取终止时间（23:59:59.999）
        const results = await this.getDateRangeRecords({
            agent,
            userObjectId,
            groupObjectId,
            start: startDate,
            end: endDate
        });
        return { startDate, endDate, results };
    }

    /**
     * 获取指定日期范围的记录集
     * @param agent 
     * @param userObjectId  为空时,表示微信群所有记录
     * @param groupObjectId 
     * @param start 
     * @param end 
     * @returns 
     */
    getDateRangeRecords = async ({ agent, userObjectId, groupObjectId, start, end }: {
        agent: string,
        userObjectId?: string,
        groupObjectId: string,
        start: Date,
        end: Date
    }): Promise<Array<any>> => {
        if (!start || !end || start > end) return null;

        const query = new ParseDB.Query(TABLE_NAME);
        query.equalTo('agent', agent);
        query.ascending('activityDate');
        query.equalTo('refWxGroup', makeWxGroupRef(groupObjectId));

        // 如果userObjectId不为空,则查询指定用户的记录
        if (isValidString(userObjectId)) {
            query.equalTo('refWxUser', { __type: "Pointer", className: "AIItchatUsers", objectId: userObjectId });
        }

        query.greaterThanOrEqualTo('activityDate', start);
        query.lessThanOrEqualTo('activityDate', end);

        try {
            const records = await query.find();
            return records.map(this.getRecordJson);
        } catch (error) {
            logger.error('Error fetching data:', error);
        }
        return null;
    };
    isRecordExist = ({ agent, mealType, userObjectId, groupObjectId, activityDate }:
        { agent: string, mealType: IMealType, userObjectId: string, groupObjectId: string, activityDate: Date }):
        Promise<ParseDB.Object> => {
        const query = new ParseDB.Query(TABLE_NAME);
        query.equalTo('agent', agent);
        query.equalTo('mealType', mealType);
        query.equalTo('refWxUser', { __type: "Pointer", className: "AIItchatUsers", objectId: userObjectId });
        query.equalTo('refWxGroup', { __type: "Pointer", className: "AIItchatGroups", objectId: groupObjectId });
        // 判断是否同一天
        query.greaterThanOrEqualTo('activityDate', moment(activityDate).startOf('day').toDate());
        query.lessThanOrEqualTo('activityDate', moment(activityDate).endOf('day').toDate());
        return query.first();
    };



    /**
     * 分析 nutrition 数据,求当日总的nutrition
     * 使用 reduce 方法对相同 name 的项进行累加,返回name:value形式
    */
    sumNutritionItems = (results: any[]): { [key: string]: number } => {
        // 提取所有记录中的 aiRespNutrition 数据
        const allNutritionItems: AIRespNutrition[] = results.flatMap(record => record.get('aiRespNutrition') || []);

        // 使用 reduce 方法对相同 name 的项进行累加,返回name:value形式
        return allNutritionItems.reduce((acc: { [key: string]: number }, item: AIRespNutrition) => {
            if (!item?.name) return acc;
            acc[item.name] = (acc[item.name] || 0) + (Number(item.value) || 0);
            return acc;
        }, {});
    };
    /**
     * 计算nutrition数据中,各项占总数的百分比
     */
    getNutritionPercentage = (nutrition: { [key: string]: number }): { [key: string]: number } => {
        const total = Object.values(nutrition).reduce((acc, val) => acc + val, 0);
        return Object.entries(nutrition).reduce((acc, [key, value]) => {
            acc[key] = (value / total) * 100;
            return acc;
        }, {} as { [key: string]: number });
    }

    // 获取用户的卡路里,营养成分每日统计信息
    getTotalCaloriesToday = async (agent: string, activityDate: Date, userObjectId: string, groupObjectId: string):
        Promise<{
            totalCaloriesToday: number, totalNutrition: {
                [key: string]: number;
            }
        }> => {
        try {
            const query = new ParseDB.Query(TABLE_NAME);
            query.equalTo('agent', agent);
            query.equalTo('refWxUser', { __type: "Pointer", className: "AIItchatUsers", objectId: userObjectId });
            query.equalTo('refWxGroup', { __type: "Pointer", className: "AIItchatGroups", objectId: groupObjectId });
            // 判断是否同一天
            query.greaterThanOrEqualTo('activityDate', moment(activityDate).startOf('day').toDate());
            query.lessThanOrEqualTo('activityDate', moment(activityDate).endOf('day').toDate());

            query.select('aiRespTotalCalories', 'aiRespNutrition');
            const results = await query.find();
            if (results && results.length > 0) {
                const totalCaloriesToday = results.reduce((acc, record) => acc + record.get('aiRespTotalCalories'), 0);
                const totalNutrition = this.sumNutritionItems(results);

                return { totalCaloriesToday, totalNutrition };
            }
        }
        catch (e) {
            logger.error(`[CaloriesServices] getTotalCaloriesToday error: ${e}`)
        }
        return { totalCaloriesToday: 0, totalNutrition: {} };
    }

    // 设置用户的卡路里打卡每日统计信息
    setDayStaisCalories = async (agent: string, activityDate: Date, userObjectId: string, groupObjectId: string,
        total: number,
        totalNutrition: { [key: string]: number; }): Promise<{
            totalCaloriesToday: number,
            activityDate: Date
        }> => {
        const tabNameDay = "AIHealthCaloriesDayStatis"
        const query = new ParseDB.Query(tabNameDay);
        query.equalTo('agent', agent);
        query.equalTo('refWxUser', { __type: "Pointer", className: "AIItchatUsers", objectId: userObjectId });
        query.equalTo('refWxGroup', { __type: "Pointer", className: "AIItchatGroups", objectId: groupObjectId });
        // 判断是否同一天
        query.greaterThanOrEqualTo('activityDate', moment(activityDate).startOf('day').toDate());
        query.lessThanOrEqualTo('activityDate', moment(activityDate).endOf('day').toDate());

        let record = await query.first();
        if (!record) {
            const TBData = ParseDB.Object.extend(tabNameDay)
            record = new TBData()
            record.set('agent', agent);
            record.set('refWxUser', { __type: "Pointer", className: "AIItchatUsers", objectId: userObjectId });
            record.set('refWxGroup', { __type: "Pointer", className: "AIItchatGroups", objectId: groupObjectId });
            record.set('activityDate', activityDate);
        }

        // 分数只能增加,不能减少,否则找你麻烦
        const oldTotal = record.get('totalCalories') || 0
        if (total && total > oldTotal) {
            record.set('totalCalories', total)
            record.set('totalNutrition', totalNutrition)
            logger.info(`[CaloriesServices] 设置用户当日总卡路里: ${total} oldTotal: ${oldTotal} user: ${userObjectId} group: ${groupObjectId} activityDate: ${activityDate}`)
            const result = await record.save()
        }


        return { totalCaloriesToday: total > oldTotal ? total : 0, activityDate }
    };
    mergeRecord = async (activityDate: Date, req: HealthCaloriesRecord):
        Promise<{
            total: number, totalNutrition: { [key: string]: number; },
            result: HealthCaloriesRecord
        }> => {
        const { agent, refWxUser, refWxGroup, mealType } = req;
        try {
            let isExist = false
            // 检查记录是否存在
            let recordObj = await this.isRecordExist({ agent, mealType, userObjectId: refWxUser.objectId, groupObjectId: refWxGroup.objectId, activityDate });
            if (recordObj) {
                isExist = true
                // 合并foodItems
                const existFoodItems = recordObj.get('foodItems')
                const newFoodItems = req.foodItems?.filter(food => !existFoodItems.includes(food))
                recordObj.set('foodItems', [...existFoodItems, ...newFoodItems])
                // 合并imageFoods
                const existImageFoods = recordObj.get('imageFoods')
                const newImageFoods = req.imageFoods?.filter(food => !existImageFoods.includes(food))
                // 合并scoreDetails
                const existScoreDetails = recordObj.get('scoreDetails')
                const newScoreDetails = req.scoreDetails

                recordObj.set({
                    ...req,
                    activityDate,
                    imageFoods: [...existImageFoods, ...newImageFoods],
                    scoreDetails: newScoreDetails,
                })
            } else {
                const TBData = ParseDB.Object.extend(TABLE_NAME)
                recordObj = new TBData()
                recordObj.set(req)
                recordObj.set({
                    activityDate,
                    refWxUser,
                    refWxGroup,
                })
            }
            // 需要首先保存,确保后续计算总卡路里正确
            const result = await recordObj.save()
            // 如果total为 0 ,那么就是没有历史记录,就用当前目录
            let { totalCaloriesToday: total, totalNutrition } = await this.getTotalCaloriesToday(agent, activityDate, refWxUser.objectId, refWxGroup.objectId)
            if (total <= 0) total = req.aiRespTotalCalories || 0

            // 设置用户的卡路里打卡每日统计信息
            await this.setDayStaisCalories(agent, activityDate, refWxUser.objectId, refWxGroup.objectId, total, totalNutrition)

            return { total, totalNutrition, result: this.getRecordJson(result) }
        } catch (e) {
            logger.error(`[CaloriesServices] mergeRecord error: ${e}`)

        }
        return { total: 0, totalNutrition: {}, result: undefined }
    };
    // 处理卡路里打卡结果
    procCaloriesResults = async ({ agent, account, mealType, foods, msg, aiResponse }: {
        agent: EthAddress, account, mealType: IMealType, foods: Array<IFood>,
        msg: INotAtMsg, aiResponse: IGPTResponse<AIRespCaloriesJson>
    })
        : Promise<{ total: number, totalNutrition: { [key: string]: number; }, result: HealthCaloriesRecord }> => {
        try {
            const activityDate = extractDate(msg.content) ?? new Date()
            // const userInfo = await getItchatUserInfo(account, wxUser)
            const record: HealthCaloriesRecord = {
                agent,
                activityDate,
                account,
                userName: msg.userName,
                groupName: msg.groupName,
                mealType,
                foodItems: foods,
                imageFoods: [],
                scoreDetails: { format: 0, content: 0, image: 0, nutrition: 0 },
                refWxUser: { __type: 'Pointer', className: 'AIItchatUsers', objectId: msg.refItchatUser?.objectId },
                refWxGroup: { __type: 'Pointer', className: 'AIItchatGroups', objectId: msg.refItchatGroup?.objectId },

                aiRespOther: aiResponse.json?.other,
                aiRespCalories: aiResponse.json?.calories,
                aiRespNutrition: aiResponse.json?.nutrition,
                aiRespSummary: aiResponse.json?.summary,
                aiRespSuggestion: aiResponse.json?.suggestion,
                aiRespTotalCalories: aiResponse.json?.totalCalories,
            }
            return this.mergeRecord(activityDate, record)
        } catch (e) {
            logger.error(`[CaloriesServices] procCaloriesResults error: ${e}`)

        }
        return undefined;
    };
    mergeRecordDay = (activityDate: Date, record: ParseDB.Object, newData: HealthCaloriesRecord) => {
        throw new Error("Method not implemented.");
    };
    /**
     * 插入卡路里记录(来自cow)
     * @param agent 
     * @param account 
     * @param body 
     * @returns 
     */
    async servicesInsertRecord({ agent, sessionId, account, body }: { agent: EthAddress, sessionId: string, account: EthAddress, body: HealthCaloriesRequest }): Promise<any> {
        const { meal_date: mealDate, meal_type: mealType, food_items: foodItems,
            image_foods: imageFoods, total_score: totalScore, score_details: scoreDetails,
            total_calories_today: totalCaloriesToday, target_calories: targetCalories,
            wx_user: wxUser, wx_group: wxGroup } = body

        const group = await wxGroupServices.getItchatGroupInfo(agent, wxGroup)
        const user = await getItchatUserInfo(agent, wxUser)

        if (!group || !user) {
            logger.error(`[CaloriesServices] 微信群或用户不存在: ${wxGroup} ${wxUser}`)
            throw new Error('Group or user not found')
        }

        const activityDate = mealDate ? extractDate(mealDate) : new Date()
        const record: HealthCaloriesRecord = {
            agent,
            account,
            mealType,
            activityDate,
            foodItems,
            totalScore,
            scoreDetails,
            totalCaloriesToday,
            targetCalories,
            refWxUser: {
                __type: "Pointer",
                className: "AIItchatUsers",
                objectId: user.objectId
            },
            refWxGroup: {
                __type: "Pointer",
                className: "AIItchatGroups",
                objectId: group.objectId
            },
            userName: user.NickName,
            groupName: group.groupNickName,
            imageFoods: []
        }

        const result = await this.mergeRecord(activityDate, record)

        // 更新卡路里档案卡
        refreshUserCaloriesCard(result?.result)
        return result
    }
}

// 创建单例实例
const healthCaloriesServices = new HealthCaloriesServices();
export default healthCaloriesServices;


