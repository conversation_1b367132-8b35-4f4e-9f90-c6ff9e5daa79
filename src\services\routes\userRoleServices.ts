import { ParseDB } from "../../database/database";
import { BaseServices } from "../baseServices";
import { GResponse, GResponseList } from "../../types/types";
import CommonError from "../../utils/comm_error";
import ApiError from "../../utils/api_error";

const TABLE_NAME = 'UsersRole';

type UserRole = {
  objectId?: string;
  roleName: string;
  roleCode: string;
  roleDesc?: string;
  status: "启用" | "禁用";
};

class UserRoleServices extends BaseServices {
  constructor() {
    super();
  }
  getRecordJson(record: ParseDB.Object): UserRole {
    return {
      objectId: record.id,
      roleName: record.get('roleName'),
      roleCode: record.get('roleCode'),
      roleDesc: record.get('roleDesc'),
      status: record.get('status'),
    };
  }

  serviceGetRoles = async ({ sessionId, pageNum = 1, pageSize = 20 }: { sessionId: string, pageNum?: number, pageSize?: number }): Promise<GResponseList<UserRole>> => {
    await this.checkSession(sessionId);
    const query = new ParseDB.Query(TABLE_NAME);

    // 获取总数
    const total = await query.count();

    // 设置分页
    const skip = this.getSkipNumber(total, pageNum, pageSize);
    query.skip(skip);
    query.limit(pageSize);

    const results = await query.find();
    const roles = results.map(result => this.getRecordJson(result));

    return this.makeGResponseList(roles, pageNum, pageSize, total);
  };

  serviceGetRole = async ({ sessionId, objectId }: { sessionId: string, objectId: string }): Promise<GResponse<UserRole | null>> => {
    await this.checkSession(sessionId);
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('objectId', objectId);
    const result = await query.first();
    if (!result) return this.makeGResponse(null);

    return this.makeGResponse(this.getRecordJson(result));
  };

  serviceCreateRole = async ({ sessionId, status = "启用", ...roleData }: { sessionId: string, roleName: string, roleCode: string, roleDesc?: string, status?: "启用" | "禁用" }): Promise<GResponse<UserRole>> => {
    await this.isSuperMan(sessionId);

    // 检查是否已存在相同roleCode的角色
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('roleCode', roleData.roleCode);
    const existingRole = await query.first();

    if (existingRole) {
      throw new ApiError(`已存在相同编码(${roleData.roleCode})的角色`, 400);
    }

    // 创建新角色
    const Role = ParseDB.Object.extend(TABLE_NAME);
    const role = new Role();
    role.set('roleName', roleData.roleName);
    role.set('roleCode', roleData.roleCode);
    role.set('roleDesc', roleData.roleDesc)
    role.set("status", status);

    const result = await role.save();
    return this.makeGResponse(this.getRecordJson(result));
  };

  serviceUpdateRole = async ({ sessionId, objectId, ...updateData }: { sessionId: string, objectId: string, roleName?: string, roleCode?: string, roleDesc?: string, status?: "启用" | "禁用" }): Promise<GResponse<UserRole>> => {
    await this.isSuperMan(sessionId);
    const query = new ParseDB.Query(TABLE_NAME);
    const role = await query.get(objectId);
    if (!role) throw new Error('Role not found');

    // 如果更新了roleCode，检查是否与其他角色冲突
    if (updateData.roleCode && updateData.roleCode !== role.get('roleCode')) {
      const checkQuery = new ParseDB.Query(TABLE_NAME);
      checkQuery.equalTo('roleCode', updateData.roleCode);
      const existingRole = await checkQuery.first();

      if (existingRole) {
        throw new Error(`已存在相同编码(${updateData.roleCode})的角色`);
      }
    }

    Object.entries(updateData).forEach(([key, value]) => {
      role.set(key, value);
    });

    const result = await role.save();
    return this.makeGResponse(this.getRecordJson(result));
  };

  serviceDeleteRole = async ({ sessionId, objectId }: { sessionId: string, objectId: string }): Promise<GResponse<boolean>> => {
    await this.isSuperMan(sessionId);
    const query = new ParseDB.Query(TABLE_NAME);
    const role = await query.get(objectId);
    if (!role) throw new Error('Role not found');
    await role.destroy({ useMasterKey: true });
    return this.makeGResponse(true);
  };

}

export default new UserRoleServices();
