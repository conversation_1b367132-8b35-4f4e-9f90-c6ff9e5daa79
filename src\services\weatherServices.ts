/**
 * 天气服务
 * 该服务用于获取指定城市的天气信息
 * API来源: Seniverse API (心知天气)
 * API文档: https://api.seniverse.com/
 */

import { GResponse } from "../types/types";

import { BaseServices } from "./baseServices";
import CacheService from "../utils/cacheService";
import { getLogger } from '../utils/logger';

const logger = getLogger('weatherSrv');
// 缓存配置
const WEATHER_CACHE_PREFIX = 'weather:';
const CACHE_DURATION = 2 * 60 * 60; // 2小时（秒）

// 创建天气服务专用的缓存实例
const weatherCache = new CacheService(WEATHER_CACHE_PREFIX, CACHE_DURATION);

interface WeatherLocation {
    id: string;
    name: string;
    country: string;
    path: string;
    timezone: string;
    timezone_offset: string;
}

interface DailyWeather {
    date: string;
    text_day: string;
    code_day: string;
    text_night: string;
    code_night: string;
    high: string;
    low: string;
    rainfall: string;
    precip: string;
    wind_direction: string;
    wind_direction_degree: string;
    wind_speed: string;
    wind_scale: string;
    humidity: string;
}

interface WeatherResult {
    location: WeatherLocation;
    daily: DailyWeather[];
    last_update: string;
}

interface WeatherResponse {
    results: WeatherResult[];
}

const getWeather = async (city: string): Promise<WeatherResponse> => {
    try {
        const apiKey = process.env.WEATHER_API_KEY;
        if (!apiKey) {
            throw new Error('Weather API key not found in environment variables');
        }

        const baseUrl = process.env.WEATHER_API_URL || 'https://api.seniverse.com/v3/weather/daily.json';
        const url = `${baseUrl}?key=${encodeURIComponent(apiKey)}&location=${encodeURIComponent(city)}&language=zh-Hans&unit=c`;

        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Weather API request failed with status ${response.status}`);
        }

        const data: WeatherResponse = await response.json();
        logger.warn(`从API获取天气数据: ${city}`, data);
        return data;
    } catch (error) {
        logger.error('Error fetching weather data:', error);
        throw error;
    }
}

class WeatherServices extends BaseServices {
    /**
     * 获取指定城市的天气信息
     * @param city 城市名称
     * @returns 天气信息
     */
    servicesGetWeather = async ({ sessionId, params }: { sessionId: string, params: any }): Promise<GResponse<WeatherResponse>> => {
        const session = await this.checkSession(sessionId);
        if (!session) {
            throw new Error('Session not found');
        }

        const { city } = params;

        try {
            // 尝试从缓存获取数据
            const cachedData = await weatherCache.get<WeatherResponse>(city);
            if (cachedData) {
                logger.info(`Weather data retrieved from cache for city: ${city}`);
                return this.makeGResponse(cachedData);
            }

            // 如果缓存中没有数据，则从API获取
            const weather = await getWeather(city);

            // 将新数据存入缓存
            await weatherCache.set(city, weather);

            logger.info(`Weather data cached for city: ${city}`);

            return this.makeGResponse(weather);
        } catch (error) {
            const errMsg = `Error in servicesGetWeather for city ${city}: ${error}`;
            logger.error(errMsg);
            return this.makeGResponseError(404, errMsg);
        }
    }
}

export default new WeatherServices;
