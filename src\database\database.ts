import ParseDB from "parse/node";
import logger from "../utils/logger";

const Parse = require("parse/node");

type ParseObject = ParseDB.Object;

function initParseServer(configParam?: {
  appId: string;
  serverURL: string;
  javascriptKey: string;
  masterKey: string;
}) {
  const config = configParam || {
    appId: process.env.PARSE_APP_ID,
    serverURL: process.env.PARSE_SERVER_URL,
    javascriptKey: process.env.PARSE_JAVASCRIPT_KEY,
    masterKey: process.env.PARSE_MASTER_KEY,
  };
  logger.info(`\n===>initParseServer=== \n\t${config.appId} \n\t${config.serverURL}\n===>initParseServer===`);

  ParseDB.serverURL = config.serverURL;
  ParseDB.initialize(config.appId, config.javascriptKey, config.masterKey);
  // 设置allowClientClassCreation
  ParseDB.CoreManager.set("allowClientClassCreation", true);

  const initApiKeyInfo = async () => {
    const query = new ParseDB.Query(ParseDB.Object.extend("ApiKeyInfo"));
    query.equalTo("name", "pinata-key");
    const row = await query.first();
    if (!row || !row?.id) {
      const info = new ParseDB.Object("ApiKeyInfo");
      info.set("name", "pinata-key");
      info.set("key", "35754f52936dfaca8acb");
      info.set(
        "jwt",
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.mgnKaPZpBazUT-GVdS2als2_p5yWKzw5b3ECiO81ZsM",
      );

      await info.save();
    }
  };

  const createIDS = async () => {
    const classes = ["Guilds", "Groups", "Roles", "GroupPlatforms", "GuildPlatforms", "Requirements", "Users"];

    classes.forEach(async (className, index) => {
      const query = new ParseDB.Query(ParseDB.Object.extend("IncrementIDS"));
      query.equalTo("class", className);
      const row = await query.first();
      if (!row || !row?.id) {
        logger.warn(`===>createIDS=== :${(index + 1) * 10000} - ${className}`);
        const incrementIDS = new (ParseDB.Object.extend("IncrementIDS"))();

        incrementIDS.set("class", className);
        incrementIDS.set("uid", (index + 1) * 10000);
        await incrementIDS.save();
      }
    });
  };
  // parse server cloud script 会调用这些内容,不能删除
  createIDS();
  initApiKeyInfo();
  // nftInfoTB.initNftInfoList();
}
const createNewId = async (className: string, firstId = 10000): Promise<number> => {
  const query = new ParseDB.Query(ParseDB.Object.extend("IncrementIDS"));
  query.equalTo("class", className);
  const row = await query.first();
  if (row && row?.id) {
    const uid = row.get("uid");
    row.increment("uid");
    await row.save();
    return uid + 1;
  }

  const incrementIDS = new (ParseDB.Object.extend("IncrementIDS"))();

  incrementIDS.set("class", className);
  incrementIDS.set("uid", firstId);
  await incrementIDS.save();
  return firstId;

};
type ParseRef = {
  __type: "Pointer";
  className: string;
  objectId: string;
  id?: string;
};

export default initParseServer;
export { Parse, ParseDB, ParseObject, ParseRef, initParseServer, createNewId };

