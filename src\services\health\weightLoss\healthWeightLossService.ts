import moment from 'moment';
import { Parse, ParseDB } from '../../../database/database';
import { EthAddress, WECHAT_DIVIDING_LINE } from '../../../types/types';
import {
    calculateWeeksBetweenDates,
    extractDate,
    formatDateTimeWithWeek,
    getBeijingTime,
    getFriendlyDuration,
} from '../../../utils/date_tools';
import logger from '../../../utils/logger';
import { isValidString } from '../../../utils/utils';
import gpt from '../../gpt/gptServerApi';
import userCardServices from '../healthUserCardServices';
import {
    IAIResItem,
    IWeightLossData,
    IWeightLossRecords,
    IWeightLossReq,
    IWeightLossResponse,
    IWeightLossResponseBase,
    IWeightLossSummaryResponse,
    IWeightLossTable,
    TABLE_NAME
} from './weightLossTypes';

import { ICaloriePlan, IWeightLossCard } from '../../../types/health';
import { IWeightLossGPTResponse } from '../../gpt/gptServerComm';
import wechatGroup from '../../wechat/cowGroupClass';

import { getUserInfo, isDoctorByWxUserOId } from '../../users/userFunction';
import { addOrUpdateWxUser, getItchatUserInfo, makeWxUserRef } from '../../wechat/cowUserFunctions';
import { HealthBaseServices } from '../base/healthBaseServices';
import { getCalorieCard, getCaloriesPlan, getCaloriesPlanByUID, setUserCaloriesPlanByUID } from '../calories/healthCaloriesPlan';
import { getWxGroupInfoByObjectId, makeWxGroupRef, updateWxGroupSimpleInfo } from '../../wechat/cowGroupFunction';
import { WxGroupInfo, WxUserInfo } from '../../wechat/types';
import { assignWeightLossPlanToHealthWxGroup } from '../healthPatientsWxGroup';
import { addOrSaveHealthReport } from '../healthReportSevices';
import gptServerApi from '../../gpt/gptServerApi';

class HealthWeightLossService extends HealthBaseServices {
    constructor() {
        super();
        this.TABLE_NAME = TABLE_NAME;
    }

    keyWord = '!run,!weight,!减重,!打卡,!运动,!减重图表,!减重月报,!减重周报,#减重，#减重周报，#减重月报';

    checkInPrompt = (agentName: string): string => {
        let text = '可参考录入方法,如:\n\n';
        text += `@${agentName} #打卡 昨天\n`;
        text += `体重 66kg\n饮水量 2000ml\n睡觉时间 7时\n有氧运动 32分钟\n\n`;
        text += `日期格式:'昨天,前天,3天前,1月2日'等\n`;
        text += "设目标体重:'目标重量: 56kg'";
        return text;
    };
    getWeightLossData = (record: ParseDB.Object): IWeightLossData => {
        if (!record) return null;
        return {
            model: record.get('model'),
            weight: record.get('weight') || 0,
            water: record.get('waterIntake') || 0,
            sleep: record.get('sleepTime') || 0,
            aerobic: record.get('exerciseAerobic') || 0,
            anaerobic: record.get('exerciseResistance') || 0,
            flexibility: record.get('exerciseFlexibility') || 0
        };
    };
    getRecordJson = (record: ParseDB.Object): IWeightLossTable => {
        if (!record) return null;
        return {
            objectId: record.id,
            aiTokens: record.get('aiTokens') || 0,
            createdAt: record.get('createdAt'),
            updatedAt: record.get('updatedAt'),
            agent: record.get('agent'),
            account: record.get('account'),
            hash: record.get('hash'),
            weight: record.get('weight') || 0,
            bloodSugar: record.get('bloodSugar') || 0,
            waterIntake: record.get('waterIntake') || 0,
            sleepTime: record.get('sleepTime') || 0,
            sportsTime: record.get('sportsTime') || 0,
            sports: record.get('sports') || [],
            model: record.get('model'),
            isGroup: record.get('isGroup'),
            action: record.get('action'),
            activityDate: record.get('activityDate'),
            exerciseAerobic: record.get('exerciseAerobic'),
            exerciseResistance: record.get('exerciseResistance'),
            exerciseFlexibility: record.get('exerciseFlexibility'),
            aiResponse: record.get('aiResponse'),
            completionTokens: record.get('completionTokens'),
            promptTokens: record.get('promptTokens'),
            wxUserObjectId: record.get('wxUserObjectId'),
            wxGroupObjectId: record.get('wxGroupObjectId'),
            conversationId: record.get('conversationId'),
            message: record.get('message'),
            userName: record.get('userName'),
            groupName: record.get('groupName'),
            score: record.get('score')
        };
    };

    makeShowText = async (agent: EthAddress, info: IWeightLossTable, userCard: IWeightLossCard, targetWeight: number = 0): Promise<string> => {
        const lastWeekWeight = await this.getLastWeekAvgWeight(info.wxUserObjectId);
        const lastWeekSleep = getFriendlyDuration(await this.getLastWeekAvgSleep(info.wxUserObjectId));
        const lastWeekWater = await this.getLastWeekAvgWaterIntake(info.wxUserObjectId);

        const thisWeek = getFriendlyDuration(await this.getThisWeekSportTime(info.wxUserObjectId));
        const lastWeek = getFriendlyDuration(await this.getLastWeekSportTime(info.wxUserObjectId));

        const dateStr = formatDateTimeWithWeek(new Date(info.activityDate));

        const data = await this.getThisWeekRecords(agent, info.wxUserObjectId, info.wxGroupObjectId);
        const thisWeekRecords = data?.results?.length || 0;
        const thisMonthSummary = await this.getThisMonthSummary(agent, info.wxUserObjectId, info.wxGroupObjectId);
        const thisMonthRecords = thisMonthSummary?.count || 0;
        const tail = `${WECHAT_DIVIDING_LINE}`;

        let returnText = `✅ 已成功打卡!\n📅 日期: ${dateStr}\n`;

        if (targetWeight) returnText += `🎯 目标体重: ${targetWeight}公斤\n`;
        if (info.weight) returnText += `⚖️ 体重: ${info.weight}公斤\n`;
        if (info.waterIntake) returnText += `💧 饮水: ${info.waterIntake} 毫升\n`;
        if (info.sleepTime) returnText += `😴 睡眠: ${getFriendlyDuration(info.sleepTime)}\n`;
        if (info.bloodSugar) returnText += `🩺 血糖: ${info.bloodSugar} 毫摩尔/升\n`;
        if (info.exerciseAerobic) returnText += `🏃 有氧运动: ${info.exerciseAerobic}分钟\n`;
        if (info.exerciseResistance) returnText += `💪 无氧运动: ${info.exerciseResistance}分钟\n`;
        if (info.exerciseFlexibility) returnText += `🧘 柔韧性训练: ${info.exerciseFlexibility}分钟\n`;

        returnText += `\n打卡记录,本周(${thisWeekRecords}次) 本月(${thisMonthRecords}次)\n`;

        const score = userCardServices.calculateScore(info)
        returnText += `💯 本次打卡积分: ${score},总积分: ${userCard?.totalScore + score}\n`;
        returnText += `${tail}`;
        return returnText;
    };

    // Record retrieval methods
    getDateRangeRecords = async ({ agent, userObjectId, groupObjectId, start, end }: {
        agent: string,
        userObjectId?: string,
        groupObjectId: string,
        start: Date,
        end: Date
    }): Promise<any> => {
        if (!isValidString(userObjectId) || !start || !end || start > end) return null;

        const query = new ParseDB.Query(TABLE_NAME);
        query.equalTo('agent', agent);
        query.ascending('activityDate');
        query.equalTo('wxUserObjectId', userObjectId);
        query.equalTo('wxGroupObjectId', groupObjectId);
        query.greaterThanOrEqualTo('activityDate', start);
        query.lessThanOrEqualTo('activityDate', end);

        try {
            return query.find();
        } catch (error) {
            logger.error('Error fetching data:', error);
        }
        return null;
    };

    getLastNDaysRecords = async (agent: string, userObjectId: string, groupObjectId: string, n: number = 14): Promise<IWeightLossRecords> => {
        if (!isValidString(userObjectId) || n <= 0 || !agent) return null;

        const startDate = moment().subtract(n, 'days').startOf('day').toDate();
        const endDate = moment().endOf('day').toDate();

        return { startDate, endDate, results: await this.getDateRangeRecords({ agent, userObjectId, groupObjectId, start: startDate, end: endDate }) };
    };

    getLastMonthRecords = async (agent: string, userObjectId: string, groupObjectId: string): Promise<IWeightLossRecords> => {
        if (!isValidString(userObjectId)) return undefined;
        const startDate = moment().subtract(1, 'month').startOf('month').toDate();
        const endDate = moment().subtract(1, 'month').endOf('month').toDate();

        return { startDate, endDate, results: await this.getDateRangeRecords({ agent, userObjectId, groupObjectId, start: startDate, end: endDate }) };
    };

    getThisMonthRecords = async (agent: string, userObjectId: string, groupObjectId: string): Promise<IWeightLossRecords> => {
        if (!isValidString(userObjectId)) return undefined;
        const startDate = moment().startOf('month').toDate();
        const endDate = moment().endOf('month').toDate();
        return { startDate, endDate, results: await this.getDateRangeRecords({ agent, userObjectId, groupObjectId, start: startDate, end: endDate }) };
    };

    getLastWeekRecords = async (agent: string, userObjectId: string, groupObjectId: string): Promise<any> => {
        if (!isValidString(userObjectId)) return undefined;
        const startDate = moment().subtract(1, 'week').startOf('week').toDate();
        const endDate = moment().subtract(1, 'week').endOf('week').toDate();
        return { startDate, endDate, results: await this.getDateRangeRecords({ agent, userObjectId, groupObjectId, start: startDate, end: endDate }) };
    };
    getLastTwoWeekRecords = async (agent: string, userObjectId: string, groupObjectId: string): Promise<any> => {
        if (!isValidString(userObjectId)) return undefined;
        const startDate = moment().subtract(2, 'weeks').startOf('week').toDate();
        const endDate = moment().subtract(1, 'weeks').endOf('week').toDate();
        return { startDate, endDate, results: await this.getDateRangeRecords({ agent, userObjectId, groupObjectId, start: startDate, end: endDate }) };
    };
    getThisWeekRecords = async (agent: string, userObjectId: string, groupObjectId: string): Promise<any> => {
        if (!isValidString(userObjectId)) return undefined;
        const startDate = moment().startOf('week').toDate();
        const endDate = moment().endOf('week').toDate();
        return { startDate, endDate, results: await this.getDateRangeRecords({ agent, userObjectId, groupObjectId, start: startDate, end: endDate }) };
    };

    // Average calculation methods
    getLastWeekAverage = async (userId: string, keyName: string): Promise<number> => {
        if (!isValidString(userId) || !isValidString(keyName)) return 0;

        const lastWeekStartDate = moment().subtract(1, 'weeks').startOf('week').toDate();
        const lastWeekEndDate = moment().subtract(1, 'weeks').endOf('week').toDate();

        const query = new Parse.Query(TABLE_NAME);
        query.equalTo('wxUserObjectId', userId);
        query.greaterThanOrEqualTo('activityDate', lastWeekStartDate);
        query.lessThanOrEqualTo('activityDate', lastWeekEndDate);

        try {
            const records = await query.find();
            if (records.length === 0) return 0;
            let results = records.map((item: any) => item.get(keyName));
            results = results.filter((item: any) => item);

            const avg = Math.floor(results.reduce((sum: number, cur: number) => sum + cur, 0) / results.length);
            return avg;
        } catch (error) {
            logger.error(error);
            return 0;
        }
    };

    getLastWeekAvgWaterIntake = async (userId: string) => this.getLastWeekAverage(userId, 'waterIntake');
    getLastWeekAvgSleep = async (userId: string): Promise<number> => this.getLastWeekAverage(userId, 'sleepTime');
    getLastWeekAvgWeight = async (userObjectId: string): Promise<number> => this.getLastWeekAverage(userObjectId, 'weight');


    // Sports time calculation methods
    _calcSportsTime = (sports: Array<IAIResItem>): number =>
        sports?.reduce((acc: any, cur: any) => {
            if (cur.value === undefined || cur.value === null) {
                return acc;
            }
            let sum = acc;
            if (cur.unit === '分钟') sum += Number(cur.value);
            else if (cur.unit === '小时') {
                const aaa = Number(cur.value) * 60;
                sum += aaa;
            } else sum += Number(cur.value);
            return sum;
        }, 0);

    getSportsTime = (data: IWeightLossTable): number => {
        const sports = data.sports;
        const time1 = this._calcSportsTime(sports);
        const time2 = data.exerciseAerobic + data.exerciseResistance + data.exerciseFlexibility;
        return time1 + time2;
    }

    getThisWeekSportTime = async (userObjectId: string): Promise<number> => {
        if (!isValidString(userObjectId)) return 0;

        const thisWeekStartDate = moment().startOf('week').toDate();
        const thisWeekEndDate = moment().endOf('week').toDate();

        const query = new ParseDB.Query(TABLE_NAME);
        query.equalTo('wxUserObjectId', userObjectId);
        query.greaterThanOrEqualTo('activityDate', thisWeekStartDate);
        query.lessThanOrEqualTo('activityDate', thisWeekEndDate);

        try {
            const results = await query.find({ useMasterKey: true });
            const sports = results.map((r: any) => r.get('sports'));
            if (!sports.length) return 0;
            return sports.reduce((acc: any, item: any) => {
                let sum = acc;
                sum += item.reduce((acc2: any, cur: any) => {
                    if (cur.value === undefined || cur.value === null) {
                        return acc2;
                    }
                    let sum2 = acc2;
                    if (cur.unit === '分钟') sum2 += Number(cur.value);
                    else if (cur.unit === '小时') {
                        const aaa = Number(cur.value) * 60;
                        sum2 += aaa;
                    } else sum2 += Number(cur.value);
                    return sum2;
                }, 0);
                return sum;
            }, 0);
        } catch (error) {
            logger.error('Error fetching data:', error);
            return 0;
        }
    };

    getLastWeekSportTime = async (userObjectId: string): Promise<number> => {
        if (!isValidString(userObjectId)) return 0;

        const lastWeekStartDate = moment().subtract(1, 'weeks').startOf('week').toDate();
        const lastWeekEndDate = moment().subtract(1, 'weeks').endOf('week').toDate();

        const query = new ParseDB.Query(TABLE_NAME);
        query.equalTo('wxUserObjectId', userObjectId);
        query.greaterThanOrEqualTo('activityDate', lastWeekStartDate);
        query.lessThanOrEqualTo('activityDate', lastWeekEndDate);

        try {
            const results = await query.find({ useMasterKey: true });
            const sports = results.map((r: any) => r.get('sports'));
            if (!sports.length) return 0;
            return sports.reduce((acc: any, item: any) => {
                let sum = acc;
                sum += item.reduce((acc2: any, cur: any) => {
                    if (cur.value === undefined || cur.value === null) {
                        return acc2;
                    }
                    let sum2 = acc2;
                    if (cur.unit === '分钟') sum2 += Number(cur.value);
                    else if (cur.unit === '小时') {
                        const aaa = Number(cur.value) * 60;
                        sum2 += aaa;
                    } else sum2 += Number(cur.value);
                    return sum2;
                }, 0);
                return sum;
            }, 0);
        } catch (error) {
            logger.error('Error fetching data:', error);
            return null;
        }
    };

    // Summary methods
    makeSummaryData = async (records: Array<any>, userCard: any): Promise<IWeightLossSummaryResponse> => {
        if (!records || !records.length) return null;
        const count = records.length;

        let minSleepTime = 60 * 20;
        let avgSleepTime = 0;
        let avgAerobic = 0;
        let avgResistance = 0;
        let avgFlexibility = 0;
        let weightLoss = 0;
        let firstWeight = 0;
        let lastWeight = 0;
        let aiTokens = 0;
        let avgWaterIntake = 0;
        const data: Array<IWeightLossResponse> = [];

        let minDate = records[0].get('activityDate');
        let maxDate = records[0].get('activityDate');
        let days = 0;

        for (let i = 0; i < records.length; i += 1) {
            const item = this.getRecordJson(records[i]);
            if (lastWeight === 0 && item.weight > 0) {
                lastWeight = item.weight;
            }
            if (item.weight > 0) {
                firstWeight = item.weight;
            }

            if (item.sleepTime > 0) {
                minSleepTime = Math.min(minSleepTime, item.sleepTime);
                avgSleepTime = (avgSleepTime * i + item.sleepTime) / (i + 1);
            }

            if (item.waterIntake > 0) avgWaterIntake = (avgWaterIntake * i + item.waterIntake) / (i + 1);

            aiTokens += item.aiTokens;

            if (item.activityDate < minDate) minDate = item.activityDate;
            if (item.activityDate > maxDate) maxDate = item.activityDate;
            days += 1;

            const dataItem: IWeightLossResponse = {
                model: item.model,
                aiTokens: item.aiTokens,
                completionTokens: item.completionTokens,
                promptTokens: item.promptTokens,
                bloodSugar: item.bloodSugar,
                sportsTime: item.sportsTime,
                sports: item.sports,
                updatedAt: item.updatedAt,
                createdAt: item.createdAt,
                isGroup: item.isGroup,
                action: item.action,
                agent: item.agent,
                objectId: item.objectId,
                account: item.account,
                nickName: item.userName,
                balanceAITokens: 0,
                hash: item.hash,
                showText: '',
                activityDate: item.activityDate,
                weightLossData: {
                    model: item.model,
                    weight: item.weight,
                    water: item.waterIntake,
                    sleep: item.sleepTime,
                    aerobic: item.exerciseAerobic,
                    anaerobic: item.exerciseResistance,
                    flexibility: item.exerciseFlexibility
                },
            };
            data.push(dataItem);
        }

        avgAerobic = this.getAvgNumber(data, 'exerciseAerobic');
        avgResistance = this.getAvgNumber(data, 'exerciseResistance');
        avgFlexibility = this.getAvgNumber(data, 'exerciseFlexibility');
        weightLoss = Number((firstWeight - lastWeight).toFixed(2));

        const formattedStartDate = moment(minDate).format('M月D日');
        const formattedEndDate = moment(maxDate).format('M月D日');
        const dateRangeText = `${formattedStartDate}-${formattedEndDate}`;

        const result: IWeightLossSummaryResponse = {
            count,
            days,
            dateRangeText,
            minSleepTime,
            avgSleepTime: Number(avgSleepTime.toFixed(2)),
            sportsCount: 0,
            avgWaterIntake: Number(avgWaterIntake.toFixed(2)),
            aiTokens,
            data,
            userCard,
            avgAerobic: Number(avgAerobic.toFixed(2)),
            avgResistance: Number(avgResistance.toFixed(2)),
            avgFlexibility: Number(avgFlexibility.toFixed(2)),
            weightLoss: Number(weightLoss.toFixed(2))
        };

        return result;
    };

    getAvgNumber = (arr: any, name: string): number => {
        const filteredData = arr.filter((item: any) => Number(item[name]) > 0);
        if (filteredData.length === 0) return 0;
        const sum = filteredData.reduce((acc: any, item: any) => acc + Number(item[name]), 0);
        return sum / filteredData.length;
    };

    // Record management methods
    saveWeightLossLog(data: IWeightLossTable) {
        if (!data) return;
        logger.info(
            `保存减重日志:${data.wxUserObjectId} - ${data.aiTokens} - ${data.wxGroupObjectId} - ${data.activityDate}`,
        );
        const TBWeightLoss = ParseDB.Object.extend(`${TABLE_NAME}_log`);
        const chat = new TBWeightLoss();
        chat.set(data);
        chat.save();
    }

    async checkRecordExistDay({ agent, wxUserObjectId, activityDate }: { agent: EthAddress; wxUserObjectId: string; activityDate: Date; }): Promise<ParseDB.Object | undefined> {
        const query = new ParseDB.Query(TABLE_NAME);
        query.equalTo('agent', agent);
        query.equalTo('wxUserObjectId', wxUserObjectId);
        query.greaterThanOrEqualTo('activityDate', moment(activityDate).startOf('day').toDate());
        query.lessThanOrEqualTo('activityDate', moment(activityDate).endOf('day').toDate());
        return query.first();
    }

    async checkRecordExist({ hash, userOId, groupOID, agent, activityDate }: { hash: string, userOId: string, groupOID: string, agent: EthAddress, activityDate: Date }): Promise<any> {
        try {
            const query = new ParseDB.Query(TABLE_NAME);
            query.equalTo('agent', agent);
            query.equalTo('hash', hash);
            query.equalTo('wxUserObjectId', userOId);
            if (groupOID) query.equalTo('wxGroupObjectId', groupOID);

            const startOfDay = moment(activityDate).startOf('day').toDate();
            const endOfDay = moment(activityDate).endOf('day').toDate();
            query.greaterThanOrEqualTo('activityDate', startOfDay);
            query.lessThan('activityDate', endOfDay);

            return query.first();
        } catch (error) {
            logger.error('Error checking record exist:', error);
            return null;
        }
    }

    mergeAIResItem = (oldArray: IAIResItem[], newArray: IAIResItem[]): IAIResItem[] => {
        const mergedMap: { [name: string]: IAIResItem } = {};
        oldArray?.forEach(item => {
            mergedMap[item.name] = item;
        });
        newArray?.forEach(item => {
            mergedMap[item.name] = item;
        });
        return Object.values(mergedMap);
    };

    mergeRecordDay = (activityDate: Date, record: ParseDB.Object, newData: IWeightLossTable): IWeightLossTable => {
        if (!activityDate || !record) return newData;

        const oldData = this.getRecordJson(record);
        const mergeData: IWeightLossTable = newData;
        if (oldData) {
            mergeData.weight = newData.weight > 0 ? newData.weight : oldData.weight;
            mergeData.sleepTime = newData.sleepTime > 0 ? newData.sleepTime : oldData.sleepTime;
            mergeData.waterIntake = newData.waterIntake > 0 ? newData.waterIntake : oldData.waterIntake;

            mergeData.sports = this.mergeAIResItem(oldData.sports, newData.sports);
            mergeData.sportsTime = this.getSportsTime(mergeData);

            mergeData.aiTokens = oldData.aiTokens + newData.aiTokens;
        }

        return mergeData;
    };

    // 格式化餐点描述文本，去除·符号和多余空格，添加适当缩进，但保留☑符号
    formatMealDescription = (description: string): string => {
        if (!description) return '';

        let formattedText = '';
        // 分割描述文本为行
        const lines = description.split(/\r?\n/);

        // 处理每一行
        lines.forEach(line => {
            let trimmedLine = line.trim();
            let prefix = '';

            // 保留☑符号，但去除其他前缀符号
            if (trimmedLine.startsWith('☑')) {
                prefix = '☑';
                trimmedLine = trimmedLine.substring(1).trim();
            } else if (trimmedLine.startsWith('·') || trimmedLine.startsWith('•') || trimmedLine.startsWith('-')) {
                trimmedLine = trimmedLine.substring(1).trim();
            }

            // 去除多余空格（将连续多个空格替换为单个空格）
            trimmedLine = trimmedLine.replace(/\s+/g, ' ');

            if (trimmedLine) {
                // 添加2个字符的缩进，并在前面添加保留的前缀
                formattedText += `${prefix}${trimmedLine}\n`;
            }
        });

        return formattedText;
    }

    makeCaloriesPlanTextByData = (plan: ICaloriePlan): string => {
        if (!plan) return "⚠️ 未设置减重方案\n\n📝 设置方法:\n   设置减重方案 100\n";

        let text = `📊 您当前的减重方案:\n`;

        text += `  方案名称:${plan.name}\n`;
        text += `🔥推荐摄入热量: ${plan.calories}千卡/天\n\n`;

        // 对餐食进行排序，只根据时间的“时”和“分”进行排序，忽略日期部分
        const sortedMeals = [...plan.meals].sort((a, b) => {
            const dateA = new Date(a.time);
            const dateB = new Date(b.time);
            // 提取小时和分钟，计算总分钟数作为排序依据
            const timeA = dateA.getHours() * 60 + dateA.getMinutes();
            const timeB = dateB.getHours() * 60 + dateB.getMinutes();
            return timeA - timeB;
        });

        // 定义数字编号图标数组
        const numberIcons = [
            '1️⃣', '2️⃣', '3️⃣', '4️⃣',
            '5️⃣', '6️⃣', '7️⃣', '8️⃣',
            '9️⃣', '🔟'
        ];

        // 处理每个餐点
        sortedMeals.forEach((meal, index) => {
            // 使用索引作为编号，如果超过数组长度则使用最后一个图标
            const icon = index < numberIcons.length ? numberIcons[index] : '🔟';
            const timeStr = getBeijingTime(meal.time.toISOString(), 'HH:mm');

            // 添加餐点标题行
            text += `  ${icon}【${meal.type}】${timeStr} - ${meal.calories}千卡\n`;
            // 使用独立的格式化函数处理描述文本
            if (meal.description) {
                text += this.formatMealDescription(meal.description);
            }
            text += '\n';
        });
        return text;
    }
    makeCaloriesPlanText = async (agent, groupObjectId: string): Promise<string> => {
        const group = await getWxGroupInfoByObjectId(groupObjectId);
        if (!group.weightLossPlan?.objectId) {
            return "⚠️ 未设置减重方案\n📝 设置方法:\n\n   设置减重方案 100";
        }
        const plan = await getCaloriesPlan(agent, group.weightLossPlan?.objectId);
        return this.makeCaloriesPlanTextByData(plan)
    };
    /**
     * [设置减重计划]减重计划信息存储在群中
     * @param agent 
     * @param userObjectId 
     * @param groupObjectId 
     * @param planUID 
     * @returns 
     */
    cmdSetWeghtLossPlan = async (agent: EthAddress, userObjectId, groupObjectId, planUID: number): Promise<string> => {
        const isDoctor = await isDoctorByWxUserOId(userObjectId);
        if (!isDoctor) return "⚠️ 只有医生才能设置减重方案";

        logger.warn('[设置减重方案]:', planUID);
        const plan = await getCaloriesPlanByUID(agent, planUID)
        if (!plan) return "⚠️ 未找到指定的减重方案"

        // 普通微信群中设置
        const ret = await updateWxGroupSimpleInfo({
            agent, groupObjectId,
            group: { agent, weightLossPlan: { objectId: plan.objectId, name: plan.name }, groupUserName: undefined, groupNickName: undefined, groupHeadImgUrl: undefined }
        });
        if (!ret) return "⚠️ 设置群减重方案失败"
        // 徐医生微信群中设置
        assignWeightLossPlanToHealthWxGroup(groupObjectId, { objectId: plan.objectId, name: plan.name });

        let text = "";
        if (plan) {
            text = "✅ 设置减重方案成功!\n\n";
            text += this.makeCaloriesPlanTextByData(plan);
        }
        return text;
    };
    // Main service methods
    async procCommand(agent: EthAddress, cmd: string, userObjectId: string, groupObjectId: string): Promise<any> {
        if (!cmd || cmd.length > 10) return undefined;
        // 首先检查命令是否匹配任何有效命令，如果不匹配则立即返回
        const validCommands = ['!减重', '#减重', '!减重周报', '#减重周报', '!减重月报', '#减重月报', '减重方案'];
        if (!validCommands.includes(cmd) && !cmd.startsWith('设置减重方案')) {
            logger.info("[减重服务]:非定义命令:", cmd);
            return undefined;
        }

        // 只有在确认是有效命令后才获取用户卡片信息
        const userCard = await userCardServices.getUserWeightLossCard(agent, userObjectId, groupObjectId);
        let showText: string = '';

        // 根据命令类型执行相应操作
        if (cmd === '!减重' || cmd === '#减重') {
            showText = this.makeSummaryText(await this.getThisWeekSummary(agent, userObjectId, groupObjectId), userCard);
        } else if (cmd === '!减重周报' || cmd === '#减重周报') {
            showText = this.makeSummaryText(await this.getLastWeekSummary(agent, userObjectId, groupObjectId), userCard);
        } else if (cmd === '!减重月报' || cmd === '#减重月报') {
            showText = this.makeSummaryText(await this.getLastMonthSummary(agent, userObjectId, groupObjectId), userCard);
        } else if (cmd === '减重方案') {
            showText = await this.makeCaloriesPlanText(agent, groupObjectId);
        } else if (cmd.startsWith('设置减重方案')) {
            // 从命令中提取数字作为planUID
            const planUIDMatch = cmd.match(/[-]?\d+/);
            if (planUIDMatch) {
                const planUID = parseInt(planUIDMatch[0], 10);
                showText = await this.cmdSetWeghtLossPlan(agent, userObjectId, groupObjectId, planUID);
            }
            if (!showText)
                showText = "⚠️ 请指定有效的方案编号\n\n📝 正确格式例如：\n   设置减重方案 100";
        }

        // 返回结果
        return showText ? {
            objectId: userObjectId,
            account: '' as EthAddress,
            balanceAITokens: 0,
            hash: '',
            data: { showText },
        } : undefined;
    }

    // 判断是否重复提交
    isRepeatCheckIn = async ({
        agent,
        wxUserObjectId,
        wxGroupObjectId,
        message,
        hash,
        createdAt,
    }: {
        agent: EthAddress;
        wxUserObjectId: string;
        wxGroupObjectId: string;
        message: string;
        hash: string;
        createdAt: string;
    }): Promise<IWeightLossResponseBase> => {
        try {
            const activityDate: Date = extractDate(message) || createdAt ? new Date(createdAt) : new Date();
            const record = await this.checkRecordExist({
                hash,
                userOId: wxUserObjectId,
                groupOID: wxGroupObjectId,
                agent,
                activityDate
            });

            if (record) {
                logger.warn('重复提交,忽略。', record.id, record.get('hash'));
                const data = this.getRecordJson(record);
                const userCard = await userCardServices.getUserWeightLossCard(agent, wxUserObjectId, wxGroupObjectId);
                if (!userCard) {
                    logger.error(`健康打卡,用户信息卡获取失败: ${wxUserObjectId} - ${wxGroupObjectId} - ${agent}`)
                    return undefined;
                }
                let showText = await this.makeShowText(agent, data, userCard);
                showText += '\n\n重复提交,忽略。';

                return {
                    error: '重复提交,忽略。',
                    agent: record.get('agent'),
                    hash: undefined,
                    balanceAITokens: 0,
                    objectId: record.id,
                    account: record.get('account'),
                    nickName: record.get('userName'),
                    showText: showText,
                    weightLossData: this.getWeightLossData(record)
                };
            }
        } catch (error) {
            logger.error('Error in isRepeatCheckIn:', error);
            throw error;
        }
    }

    saveOrMergeWeightLossRecord = async ({
        agent,
        account,
        nickName, groupName,
        wxUserObjectId,
        wxGroupObjectId,
        conversationId,
        action,
        message,
        hash,
        createdAt,
        gptResponse,
    }: {
        agent: EthAddress;
        account: EthAddress;
        nickName: string;
        groupName: string;
        wxUserObjectId: string;
        wxGroupObjectId: string;
        conversationId: string;
        action: string;
        message: string;
        createdAt: string;
        hash: string;
        gptResponse: IWeightLossGPTResponse;
    }): Promise<IWeightLossResponseBase> => {
        try {
            if (!gptResponse) {
                logger.error("保存减重记录失败,gptResponse为空")
                return
            }

            const isGroup = isValidString(wxGroupObjectId);
            const activityDate = extractDate(message) || new Date(createdAt);

            let tbData: IWeightLossTable = {
                hash,
                account,
                userName: nickName, groupName,
                agent,
                action,
                activityDate,
                model: gptResponse.model,
                aiResponse: gptResponse.aiJsonStr,
                aiTokens: gptResponse.aiTokens,
                completionTokens: gptResponse.completionTokens,
                promptTokens: gptResponse.promptTokens,
                isGroup,
                wxUserObjectId,
                refItchatUser: isValidString(wxUserObjectId) ? {
                    __type: 'Pointer',
                    className: 'AIItchatUsers',
                    objectId: wxUserObjectId,
                } : undefined,
                wxGroupObjectId,
                refItchatGroup: isValidString(wxGroupObjectId) ? {
                    __type: 'Pointer',
                    className: 'AIItchatGroups',
                    objectId: wxGroupObjectId,
                } : undefined,
                message,
                conversationId,
                weight: gptResponse.aiResponse.weight,
                bloodSugar: gptResponse.bloodSugar,
                waterIntake: gptResponse.aiResponse.water,
                sleepTime: gptResponse.aiResponse.sleep,
                sports: gptResponse.sports || [],
                sportsTime: 0,
                exerciseAerobic: gptResponse.aiResponse.aerobic,
                exerciseResistance: gptResponse.aiResponse.anaerobic,
                exerciseFlexibility: gptResponse.aiResponse.flexibility,

                score: 0
            };

            tbData.score = userCardServices.calculateScore(tbData);
            tbData.sportsTime = this.getSportsTime(tbData);

            this.saveWeightLossLog(tbData);

            let haveRecord = false;
            let tableWeightLoss = await this.checkRecordExistDay({
                agent,
                wxUserObjectId,
                activityDate
            });

            if (tableWeightLoss) {
                tbData = this.mergeRecordDay(activityDate, tableWeightLoss, tbData);
                haveRecord = true;
            } else {
                const TBWeightLoss = ParseDB.Object.extend(TABLE_NAME);
                tableWeightLoss = new TBWeightLoss();
            }

            tableWeightLoss.set(tbData);
            const result = await tableWeightLoss.save();

            if (!result?.id) {
                throw new Error('Failed to save weight loss record');
            }

            const userCard = await userCardServices.saveUserWeightLossCard(
                agent,
                wxUserObjectId,
                tbData,
                gptResponse.targetWeight,
                haveRecord
            );

            const data = this.getRecordJson(result);
            let showText = await this.makeShowText(agent, this.getRecordJson(result), userCard, gptResponse.targetWeight);
            if (haveRecord) {
                showText += '\n\n当日已有记录,自动合并。';
            }
            // 早于今天日期超过30天
            if (moment(activityDate).isBefore(moment().subtract(30, 'days'))) {
                showText += '\n⚠️ 指定日期距今已超过30天。🚨🚨🚨';
            }

            return {
                error: undefined,
                agent,
                balanceAITokens: 0,
                hash: data.hash,
                objectId: result.id,
                account: data.account,
                nickName,
                showText,
                weightLossData: gptResponse.aiResponse
            };

        } catch (error) {
            logger.error('Error in saveOrMergeWeightLossRecord:', error);
            throw error;
        }
    };

    servicesWeightLoss = async ({
        account,
        body,
    }: {
        account: EthAddress;
        body: IWeightLossReq;
    }): Promise<IWeightLossResponseBase> => {
        const { itchatUser } = body;
        // 通过gpt获取健康打卡数据(或regex)
        const gptResponse = await gpt.analysisWeightLoss(body.wxGroupObjectId, body.message);
        // logger.info('GPT Response:', gptResponse);

        return this.saveOrMergeWeightLossRecord({
            ...body,
            gptResponse,
            account,
            nickName: itchatUser?.NickName,
            groupName: "",
            createdAt: (new Date()).toISOString(),
        });
    };

    getThisMonthSummary = async (agent: EthAddress, userObjectId: string, groupObjectId: string): Promise<IWeightLossSummaryResponse> => {
        const u = await userCardServices.getUserWeightLossCard(agent, userObjectId, groupObjectId);
        const result = await this.getThisMonthRecords(agent, userObjectId, groupObjectId);
        return this.makeSummaryData(result?.results, u);
    };

    getLastMonthSummary = async (agent: EthAddress, userObjectId: string, groupObjectId: string): Promise<IWeightLossSummaryResponse> => {
        const u = await userCardServices.getUserWeightLossCard(agent, userObjectId, groupObjectId);
        const result = await this.getLastMonthRecords(agent, userObjectId, groupObjectId);
        return this.makeSummaryData(result?.results, u);
    };

    getLastWeekSummary = async (agent: EthAddress, userObjectId: string, groupObjectId: string): Promise<IWeightLossSummaryResponse> => {
        const u = await userCardServices.getUserWeightLossCard(agent, userObjectId, groupObjectId);
        const data = await this.getLastWeekRecords(agent, userObjectId, groupObjectId);
        return this.makeSummaryData(data?.results, u);
    };

    makeSummaryText(summary: IWeightLossSummaryResponse, userCard: IWeightLossCard): string {
        if (!summary) {
            return '您的打卡数据太少,无法进行汇总.';
        }

        const summaryData = summary
        if (!summaryData.data || summaryData.data.length < 3) {
            return '您的打卡数据太少,无法进行汇总.';
        }

        const {
            minSleepTime,
            avgSleepTime,
            avgWaterIntake,
            dateRangeText,
            days,
            avgAerobic,
            avgResistance,
            avgFlexibility,
            data,
            weightLoss
        } = summaryData;

        const activityDateAry = data.map((item: any) => item.activityDate);
        activityDateAry.sort((a: any, b: any) => a - b);

        const firstW = userCard.firstWeight;
        const lastW = this.getLastWeight(data) || userCard.lastWeight;
        // 减重百分比
        const weightLossPercent = ((firstW - lastW) / firstW) * 100;
        // 本周减重目标
        const weeks = calculateWeeksBetweenDates(userCard.firstWeightDate, new Date());
        const thisWeekTarget = firstW * (1 - (weeks * userCard.targetWeightHalfMonth) / 2);

        let text = `记录：${days}条(${dateRangeText})\n`;
        text += `${WECHAT_DIVIDING_LINE}\n`;
        text += `初始体重：${firstW.toFixed(2)}kg\n`;
        text += `现体重：${lastW.toFixed(2)}kg\n`;
        text += `共减重：${weightLoss.toFixed(2)}kg\n`;
        text += `已经减重：${weightLossPercent.toFixed(2)}%\n`;

        const targetW = userCard.targetWeight;
        const targetWMonth = 5; // ((firstW - targetW) / firstW / 3) * 100
        const targetWHMonth = 2.5; // ((firstW - targetW) / firstW / 6) * 100

        if (targetW) text += `您的目标体重：${targetW.toFixed(2)}kg\n`;

        text += `半个月减重目标：${targetWHMonth.toFixed(2)}%\n`;
        text += `一个月减重目标：${targetWMonth.toFixed(2)}%\n`;
        text += `本周目标体重：${Math.round(thisWeekTarget)}kg\n`;
        text += `\n`;
        if (minSleepTime)
            text += `最少睡眠时间：${getFriendlyDuration(Math.round(minSleepTime))}\n`;
        if (avgSleepTime)
            text += `平均睡眠时间：${getFriendlyDuration(Math.round(avgSleepTime))}\n`;
        if (avgWaterIntake)
            text += `平均水量摄入：${Math.round(avgWaterIntake)}ml\n`;
        text += `\n`;
        if (avgAerobic)
            text += `有氧运动时间：${getFriendlyDuration(Math.round(avgAerobic || 0))}\n`;
        if (avgFlexibility)
            text += `阻抗运动时间：${getFriendlyDuration(Math.round(avgFlexibility || 0))}\n`;
        if (avgResistance)
            text += `柔韧性运动时间：${getFriendlyDuration(Math.round(avgResistance || 0))}\n`;
        text += `${WECHAT_DIVIDING_LINE}\n`;
        text += `查看所有功能：发送"减重"\n`;

        return text;
    }

    getLastWeight = (ary: Array<IWeightLossResponse>): number => {
        for (let i = ary.length - 1; i >= 0; i -= 1) {
            if (Number(ary[i].weightLossData.weight) > 0) return Number(ary[i].weightLossData.weight);
        }
        return 0;
    }

    // 获取用户减重打卡最多记录的群
    getUserWeightLossCheckInGroup = async (agent: EthAddress, userObjectId: string): Promise<WxGroupInfo> => {
        if (!userObjectId) throw new Error(`getUserFirstWeightLossGroup user not found`);
        const query = new Parse.Query(TABLE_NAME);
        query.equalTo('agent', agent);
        query.equalTo('wxUserObjectId', userObjectId);
        query.descending('createdAt');
        const result = await query.first()
        return result?.get('wxGroupObjectId')
    }
    // 获取最近N天的汇总数据
    servicesWeightLossLastData = async ({ account, body, sessionId }: { sessionId: string; account: EthAddress; body: any; })
        : Promise<{
            error: number;
            userCard: IWeightLossCard;
            startDate: Date;
            endDate: Date;
            chartType: string;
            showText: string;
            showText2?: string;
            data: Array<IWeightLossResponse>;
        }> => {
        const { userObjectId: wxObjectId, lastDays, chart_type: chartType, itchatUser, itchatGroup } = body;
        const session = await this.checkSession(sessionId);
        const agent = session.agent;
        let groupObjectId = body?.groupObjectId || itchatGroup?.objectId; // 传入的groupObjectId居然发现不靠谱

        let userObjectId: string = wxObjectId; // 传入的wxObjectId居然发现不靠谱
        const u = await getItchatUserInfo(agent, itchatUser);
        if (u) userObjectId = u.objectId;

        const user = await getUserInfo(account);
        if (!user && !isValidString(userObjectId) && itchatUser) {
            logger.warn('获取最近N天的汇总数据 时，itchat用户不存在:', itchatUser);
            const { objectId } = await addOrUpdateWxUser(agent, itchatUser);
            if (!isValidString(objectId)) throw new Error(`servicesWeightLoss user not found ${account}`);
            logger.warn(` servicesWeightLoss user not found ${account}, 新创建 itchatUser:${itchatUser}`);
            userObjectId = objectId;
        }
        const group = await wechatGroup.getWxGroupInfo(agent, itchatGroup);
        if (group) groupObjectId = group.objectId;// 传入的groupObjectId居然发现不靠谱
        else groupObjectId = await this.getUserWeightLossCheckInGroup(agent, u.objectId) //实在找不到,那就到打卡记录里去找
        if (!isValidString(groupObjectId)) {
            throw new Error(`servicesWeightLossLastData groupObjectId is required userOID:${userObjectId}`);
        }

        const userCard = await userCardServices.getUserWeightLossCard(agent, userObjectId, group?.objectId || groupObjectId);
        if (!userCard) {
            logger.error(`servicesWeightLossLastData 用户资料卡未建立`, agent, userObjectId, account);
            return {
                error: -1,
                userCard,
                startDate: new Date(),
                endDate: new Date(),
                chartType,
                showText: `资料卡未建立\n建卡方法,如:\n\n打卡 早起体重:60kg,目标体重:50kg `,
                data: [],
            };
        }

        if (!userObjectId) userObjectId = userCard.userObjectId;

        let result;
        if (chartType === '月报') result = await this.getLastMonthRecords(agent, userObjectId, groupObjectId);
        else if (chartType === '周报') result = await this.getLastWeekRecords(agent, userObjectId, groupObjectId);
        else if (chartType === '两周报') result = await this.getLastTwoWeekRecords(agent, userObjectId, groupObjectId);
        else result = await this.getLastNDaysRecords(agent, userObjectId, groupObjectId, lastDays);

        const { startDate, endDate, results } = result;
        const dataAry = results?.map(
            this.getRecordJson,
        );

        const data = await this.makeSummaryData(results, userCard);
        const showText = this.makeSummaryText(data, userCard);

        const res = {
            error: 0,
            startDate,
            endDate,
            userCard,
            chartType,
            showText,
            data: dataAry,
        };
        const aiResp = await gptServerApi.makeWeightLossRecordSummary(userObjectId, res);
        return { ...res, showText2: aiResp?.showText };
    };

    servicesImageOcrRecord = async ({ sessionId, account, body }: { sessionId: string, account: EthAddress, body: any }) => {
        const session = await this.checkSession(sessionId);
        if (!session) throw new Error('session not found');
        const { ocrResult, reportType, aiResponse, wxuser, wxgroup, isGroup } = body;

        const user = await getItchatUserInfo(session.agent, wxuser);
        const group = await wechatGroup.getWxGroupInfo(session.agent, wxgroup);

        const TBImageOcrRecord = ParseDB.Object.extend('AIHealthImageOcrRecord');
        const imageOcrRecord = new TBImageOcrRecord();

        imageOcrRecord.set('account', account);
        imageOcrRecord.set('ocrResult', ocrResult);
        imageOcrRecord.set('reportType', reportType);
        imageOcrRecord.set('aiResponse', aiResponse);
        imageOcrRecord.set('isGroup', isGroup);

        if (user) {
            const refWxUser = makeWxUserRef(user.objectId); // 使用辅助函数创建指针
            imageOcrRecord.set('refWxUser', refWxUser);
        }
        if (group) {
            const refWxGroup = makeWxGroupRef(group.objectId); // 使用辅助函数创建指针
            imageOcrRecord.set('refWxGroup', refWxGroup);
        }
        const result = await imageOcrRecord.save();
        if (!result || !result.id) {
            throw new Error('Failed to save image OCR record');
        }
        let reportId = 0
        if (reportType.includes('体检报告')) {    // 添加或保存体检报告
            try {
                const healthReport = await addOrSaveHealthReport({
                    account, agent: session.agent,
                    wxGroupOID: group?.objectId || '', wxUserOID: user?.objectId || '',
                    reportType, ocrResult, aiResponse
                });

                reportId = healthReport?.id;
            } catch (error: any) {
                logger.error('Failed to add or save health report:', error);
                return this.makeGResponseError(404, error.message || "保存体检报告发送错误");
            }
        }

        logger.info('Image OCR record saved successfully:', result.id);
        return this.makeGResponse({
            objectId: result.id,
            account: result.get('account') as EthAddress,
            refWxUser: result.get('refWxUser'),
            refWxGroup: result.get('refWxGroup'),
            updatedAt: result.get('updatedAt'),
            createdAt: result.createdAt,
            reportType,
            reportId,
        });
    }
}

const weightLossService = new HealthWeightLossService();
export default weightLossService;
