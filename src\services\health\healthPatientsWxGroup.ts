/**
 * 健康打卡微信群管理服务
 * 功能包括:
 * 1. 健康打卡群的创建与管理(3个月/6个月)
 * 2. 群打卡提醒与统计
 * 3. 阶段性通知的发送
 * 4. 复诊提醒管理
 */

import moment from 'moment';
import { EthAddress, EthZero } from '../../types/types';
import { isSameDay } from '../../utils/date_tools';
import { getLogger } from '../../utils/logger';

import { IStandardizedHealthGroup } from '../../types/health';
import sysNoticeServices from '../notifications/sysNotificationServices';

import wxGroupServices from '../wechat/cowGroupClass';

import { getGroupsBySearch, setWxGroupHealthExpired, setWxGroupKeywords } from '../wechat/cowGroupFunction';
import { getWxUserByObjectId } from '../wechat/cowUserFunctions';
import onWechatApi, { WxMsgContent } from '../wechat/onWechatApi';
import { WxGroupInfo } from '../wechat/types';
import healthNoticesServices, { getWxNoticesRecordsOfGroupsNDays, HealthMsgType } from './base/healthNoticesServices';

import { cronJobSpurCaloriesPunch } from './calories/healthCaloriesFunctions';
import * as healthSetSrv from './healthSettingServices';
import healthWeightServices from './weightLoss/healthWeightLossService';
import { ParseDB } from '../../database/database';

const TABLE_NAME = 'AIHealthPatientsWxGroup';
const logger = getLogger('healthPW');

const getRecordJson = (record: any): IStandardizedHealthGroup =>
  record
    ? {
      objectId: record.id,
      agent: record.get('agent'),

      ownerAccount: record.get('ownerAccount'),
      ownerOID: record.get('ownerOID'),
      ownerName: record.get('ownerName'),
      groupOID: record.get('groupOID'),
      groupName: record.get('groupName'),
      type: record.get('type'),
      groupStatus: record.get('groupStatus'),
      patientIds: record.get('patientIds') || [],

      stageDates: record.get('stageDates') || [],
      sentStageDates: record.get('sentStageDates') || [],

      reVisitDates: record.get('reVisitDates') || [],
      sentReVisitDates: record.get('sentReVisitDates') || [],

      weightLossPlan: record.get('weightLossPlan'),
      startDate: record.get('startDate'),

      sentWeekDates: record.get('sentWeekDates') || [],
      sentMonthDates: record.get('sentMonthDates') || [],
      createAt: record.get('createdAt'),
      updateAt: record.get('updatedAt'),
    }
    : (undefined as IStandardizedHealthGroup);

// 函数来转换字符串为日期格式
function convertToDate(monthStr: string, dayStr: string, createdAt: Date): Date {
  const month = parseInt(monthStr, 10);
  const day = parseInt(dayStr, 10);
  const year = createdAt ? createdAt.getFullYear() : new Date().getFullYear(); // 获取当前年份

  return new Date(year, month - 1, day);
}

const preGroupNameFunc = (groupName: string) =>
  groupName?.replace('(预6)', '').replace('(预3)', '').replace('(预2)', '').replace('(预)', '');

// 通用函数，用于从群名中提取日期
const getDateFromGroupNameMonth136 = (agent: EthAddress, groupName: string, createdAt: Date, prefix: string = '136'): Date | null => {
  if (!groupName) return null;
  const preGroupName = preGroupNameFunc(groupName);

  // 特殊处理 "3-5.10陈叶-82.5减重管理" 和 "3-陈叶5.10-82.5减重管理" 这种情况
  // 先尝试匹配 "数字.数字" 的模式，并且这个模式在群名的前半部分
  const specialRegex = new RegExp(`^${prefix === '3' ? '3' : (prefix === '6' ? '6' : (prefix === '1' ? '1' : '[136]'))}.*?(\\d{1,2})\\.(\\d{1,2})`, 'i');
  const specialMatches = preGroupName.match(specialRegex);

  if (specialMatches) {
    const month = parseInt(specialMatches[1], 10);
    const day = parseInt(specialMatches[2], 10);

    // 验证月份和日期的有效性
    if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
      // 进一步验证特定月份的日期有效性
      if (!((month === 4 || month === 6 || month === 9 || month === 11) && day > 30)) {
        // 验证2月份的日期
        if (!(month === 2 && day > 29)) {
          return convertToDate(specialMatches[1], specialMatches[2], createdAt);
        }
      }
    }
  }

  // 使用传入的prefix参数来决定使用哪个正则表达式
  let regexps: RegExp[];

  // 构建更精确的正则表达式，确保只匹配群名开头的日期部分
  if (prefix === '3') {
    regexps = [
      /^3(.*?)-(\d{1,2})\.(\d{1,2})(?!\.\d)/i, // 匹配 3-5.10 但不匹配后面的 82.5
      /^3(.*?)-([^0-9]*?)(\d{1,2})\.(\d{1,2})(?!\.\d)/i, // 匹配 3-陈叶5.10 但不匹配后面的 82.5
    ];
  } else if (prefix === '6') {
    regexps = [
      /^6(.*?)-(\d{1,2})\.(\d{1,2})(?!\.\d)/i,
      /^6(.*?)-([^0-9]*?)(\d{1,2})\.(\d{1,2})(?!\.\d)/i,
    ];
  } else if (prefix === '1') {
    regexps = [
      /^1(.*?)-(\d{1,2})\.(\d{1,2})(?!\.\d)/i,
      /^1(.*?)-([^0-9]*?)(\d{1,2})\.(\d{1,2})(?!\.\d)/i,
    ];
  } else {
    // 默认匹配1、3、6开头的群名
    regexps = [
      /^[136](.*?)-(\d{1,2})\.(\d{1,2})(?!\.\d)/i,
      /^[136](.*?)-([^0-9]*?)(\d{1,2})\.(\d{1,2})(?!\.\d)/i,
    ];
  }

  let matches: RegExpMatchArray | null = null;
  let startDate: Date | null = null;

  // eslint-disable-next-line no-restricted-syntax
  for (const regexp of regexps) {
    matches = preGroupName.match(regexp);
    if (matches) {
      // 根据正则表达式的匹配组数来决定如何提取月份和日期
      const monthIndex = matches.length === 5 ? 3 : 2;
      const dayIndex = matches.length === 5 ? 4 : 3;

      // 提取月份和日期
      const month = parseInt(matches[monthIndex], 10);
      const day = parseInt(matches[dayIndex], 10);

      // 验证月份和日期的有效性
      if (month < 1 || month > 12) {
        const errorMsg = `${groupName} 包含无效的月份: ${month}`;
        logger.error(errorMsg);
        sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
        return null;
      }

      if (day < 1 || day > 31) {
        const errorMsg = `${groupName} 包含无效的日期: ${day}`;
        logger.error(errorMsg);
        sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
        return null;
      }

      // 进一步验证特定月份的日期有效性
      if ((month === 4 || month === 6 || month === 9 || month === 11) && day > 30) {
        const errorMsg = `${groupName} 包含无效的日期: ${month}月只有30天`;
        logger.error(errorMsg);
        sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
        return null;
      }

      // 验证2月份的日期
      if (month === 2) {
        const year = createdAt.getFullYear();
        const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
        const maxDaysInFeb = isLeapYear ? 29 : 28;

        if (day > maxDaysInFeb) {
          const errorMsg = `${groupName} 包含无效的日期: ${year}年2月只有${maxDaysInFeb}天`;
          logger.error(errorMsg);
          sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
          return null;
        }
      }

      startDate = convertToDate(matches[monthIndex], matches[dayIndex], createdAt);
      return startDate;
    }
  }

  // 如果上面的正则表达式都没有匹配成功，尝试使用更宽松的正则表达式
  // 这是为了处理特殊情况，如 "3-5.10陈叶-82.5减重管理"
  const looseRegexps = [
    new RegExp(`^${prefix === '3' ? '3' : (prefix === '6' ? '6' : (prefix === '1' ? '1' : '[136]'))}(.*?)-([^-]*)(\\d{1,2})\\.(\\d{1,2})`, 'i'),
  ];

  for (const regexp of looseRegexps) {
    matches = preGroupName.match(regexp);
    if (matches) {
      // 提取月份和日期
      const month = parseInt(matches[3], 10);
      const day = parseInt(matches[4], 10);

      // 验证月份和日期的有效性
      if (month < 1 || month > 12) {
        const errorMsg = `${groupName} 包含无效的月份: ${month}`;
        logger.error(errorMsg);
        sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
        return null;
      }

      if (day < 1 || day > 31) {
        const errorMsg = `${groupName} 包含无效的日期: ${day}`;
        logger.error(errorMsg);
        sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
        return null;
      }

      // 进一步验证特定月份的日期有效性
      if ((month === 4 || month === 6 || month === 9 || month === 11) && day > 30) {
        const errorMsg = `${groupName} 包含无效的日期: ${month}月只有30天`;
        logger.error(errorMsg);
        sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
        return null;
      }

      // 验证2月份的日期
      if (month === 2) {
        const year = createdAt.getFullYear();
        const isLeapYear = (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
        const maxDaysInFeb = isLeapYear ? 29 : 28;

        if (day > maxDaysInFeb) {
          const errorMsg = `${groupName} 包含无效的日期: ${year}年2月只有${maxDaysInFeb}天`;
          logger.error(errorMsg);
          sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
          return null;
        }
      }

      startDate = convertToDate(matches[3], matches[4], createdAt);
      return startDate;
    }
  }

  const errorMsg = `${groupName} 无法从群名获得日期.`;
  logger.error(errorMsg);
  sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
  return null;
};






// 获取群名称中的日期:必须以"3"开头
// 如: 3(预6)-9.10郁泽康-体重管理
// 如:3-4.23王明才-减重脂肪肝逆转
const getDateFromGroupName3Month = (agent: EthAddress, groupName: string, createdAt: Date): Date | null => {
  // 调用通用函数 getDateFromGroupNameMonth136，并指定前缀为 '3'
  return getDateFromGroupNameMonth136(agent, groupName, createdAt, '3');
};

// 获取群名称中的日期:必须以"6"开头
// 如: 6(预6)-9.10郁泽康-体重管理
// 如:6-4.23王明才-减重脂肪肝逆转
const getDateFromGroupName6Month = (agent: EthAddress, groupName: string, createdAt: Date): Date | null => {
  // 调用通用函数 getDateFromGroupNameMonth136，并指定前缀为 '6'
  return getDateFromGroupNameMonth136(agent, groupName, createdAt, '6');
};

// 通用函数，用于判断是否是健康打卡群
const isHealthGroupGeneric = (agent: EthAddress, groupName: string, createdAt: Date, prefix: string = '136', maxDays: number = 365): boolean => {
  if (!groupName) return false;

  // 根据传入的prefix参数构建正则表达式
  let regexPattern: string;
  if (prefix === '3') {
    regexPattern = '^3(?:\\(预[236]?\\))?-';
  } else if (prefix === '6') {
    regexPattern = '^6(?:\\(预[236]?\\))?-';
  } else if (prefix === '1') {
    regexPattern = '^1(?:\\(预[236]?\\))?-';
  } else {
    regexPattern = '^(3|6|1)(?:\\(预[236]?\\))?-';
  }

  const regex = new RegExp(regexPattern, 'i');
  if (!regex.test(groupName)) return false;

  // 根据前缀选择适当的日期提取函数
  let date: Date | null;
  if (prefix === '3') {
    date = getDateFromGroupName3Month(agent, groupName, createdAt);
  } else if (prefix === '6') {
    date = getDateFromGroupName6Month(agent, groupName, createdAt);
  } else {
    date = getDateFromGroupNameMonth136(agent, groupName, createdAt, prefix);
  }

  if (!date) return false;

  const now = new Date();
  const diffTime = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= maxDays;
};


const isHealthGroupMonth136 = (agent: EthAddress, groupName: string, createdAt: Date) => {
  // 调用通用函数，指定前缀为 '136'，最大天数为 365 * 2
  return isHealthGroupGeneric(agent, groupName, createdAt, '136', 365 * 2);
};
// 只有3- 前缀群才算健康打卡群
const isHealthGroup3Month = (agent: EthAddress, groupName: string, createdAt: Date) => {
  // 调用通用函数，指定前缀为 '3'，最大天数为 365
  return isHealthGroupGeneric(agent, groupName, createdAt, '3', 365);
};

const isHealthGroup6Month = (agent: EthAddress, groupName: string, createdAt: Date) => {
  // 调用通用函数，指定前缀为 '6'，最大天数为 365
  return isHealthGroupGeneric(agent, groupName, createdAt, '6', 365);
};

/*只有3-,6-,前缀群才算健康打卡群
  1. 获取群信息
  2. 判断是否是健康打卡群
  3. 获取健康打卡群信息
*/
const getStandardizedHealthGroup = async (groupOID: string): Promise<IStandardizedHealthGroup | null> => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('groupOID', groupOID);
  query.notEqualTo('groupStatus', 'INACTIVE');
  const record = await query.first();
  return getRecordJson(record);
};

// 添加已发送的阶段日期
export const addSentStageDate = async (groupOID: string, date: Date) => {
  try {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupOID', groupOID);
    const record = await query.first();

    if (!record) {
      logger.error(`addSentStageDate: 未找到群组记录 ${groupOID}`);
      return false;
    }

    // 设置日期时间为0点
    date.setHours(0, 0, 0, 0);

    // 使用原子操作添加日期
    record.addUnique('sentStageDates', date);
    await record.save();

    logger.info(`addSentStageDate: 成功添加阶段通知记录 ${groupOID}`);
    return true;
  } catch (error) {
    logger.error(`addSentStageDate: 保存阶段通知记录失败 ${groupOID}`, error);
    return false;
  }
};

// 添加已发送的复诊日期
export const addSentReVisitDate = async (groupOID: string, date: Date) => {
  try {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupOID', groupOID);
    const record = await query.first();

    if (!record) {
      logger.error(`addSentReVisitDate: 未找到群组记录 ${groupOID}`);
      return false;
    }

    // 设置日期时间为0点
    date.setHours(0, 0, 0, 0);

    // 使用原子操作添加日期
    record.addUnique('sentReVisitDates', date);
    await record.save();

    logger.info(`addSentReVisitDate: 成功添加复诊通知记录 ${groupOID}`);
    return true;
  } catch (error) {
    logger.error(`addSentReVisitDate: 保存复诊通知记录失败 ${groupOID}`, error);
    return false;
  }
};

// 添加已经发送的周报日期
export const addSentWeekDate = async (groupOID: string, date: Date) => {
  try {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupOID', groupOID);
    const record = await query.first();

    if (!record) {
      logger.error(`addSentWeekDate: 未找到群组记录 ${groupOID}`);
      return false;
    }

    // 设置日期时间为0点
    date.setHours(0, 0, 0, 0);

    // 使用原子操作添加日期
    record.addUnique('sentWeekDates', date);
    await record.save();

    logger.info(`addSentWeekDate: 成功添加周报通知记录 ${groupOID}`);
    return true;
  } catch (error) {
    logger.error(`addSentWeekDate: 保存周报通知记录失败 ${groupOID}`, error);
    return false;
  }
};

// 添加已经发送的月报日期
export const addSentMonthDate = async (groupOID: string, date: Date) => {
  try {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupOID', groupOID);
    const record = await query.first();

    if (!record) {
      logger.error(`addSentMonthDate: 未找到群组记录 ${groupOID}`);
      return false;
    }

    // 设置日期时间为0点
    date.setHours(0, 0, 0, 0);

    // 使用原子操作添加日期
    record.addUnique('sentMonthDates', date);
    await record.save();

    logger.info(`addSentMonthDate: 成功添加月报通知记录 ${groupOID}`);
    return true;
  } catch (error) {
    logger.error(`addSentMonthDate: 保存月报通知记录失败 ${groupOID}`, error);
    return false;
  }
};

// 删除已发送的复诊日期
export const removeSentReVisitDate = async (groupOID: string, date: Date) => {
  try {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupOID', groupOID);
    const record = await query.first();

    if (!record) {
      logger.error(`removeSentReVisitDate: 未找到群组记录 ${groupOID}`);
      return false;
    }

    // 设置日期时间为0点
    date.setHours(0, 0, 0, 0);

    // 获取当前的 sentReVisitDates 数组
    const sentReVisitDates = record.get('sentReVisitDates') || [];

    // 移除匹配的日期
    const updatedDates = sentReVisitDates.filter(
      (d: Date) => !isSameDay(d, date)
    );

    // 更新记录
    record.set('sentReVisitDates', updatedDates);
    await record.save();

    logger.info(`removeSentReVisitDate: 成功删除复诊通知记录 ${groupOID}`);
    return true;
  } catch (error) {
    logger.error(`removeSentReVisitDate: 删除复诊通知记录失败 ${groupOID}`, error);
    return false;
  }
};

// 删除已发送的阶段日期
export const removeSentStageDate = async (groupOID: string, date: Date) => {
  try {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupOID', groupOID);
    const record = await query.first();

    if (!record) {
      logger.error(`removeSentStageDate: 未找到群组记录 ${groupOID}`);
      return false;
    }

    // 设置日期时间为0点
    date.setHours(0, 0, 0, 0);

    // 获取当前的 sentStageDates 数组
    const sentStageDates = record.get('sentStageDates') || [];

    // 移除匹配的日期
    const updatedDates = sentStageDates.filter(
      (d: Date) => !isSameDay(d, date)
    );

    // 更新记录
    record.set('sentStageDates', updatedDates);
    await record.save();

    logger.info(`removeSentStageDate: 成功删除阶段通知记录 ${groupOID}`);
    return true;
  } catch (error) {
    logger.error(`removeSentStageDate: 删除阶段通知记录失败 ${groupOID}`, error);
    return false;
  }
};

// 删除已发送的周报日期
export const removeSentWeekDate = async (groupOID: string, date: Date) => {
  try {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupOID', groupOID);
    const record = await query.first();

    if (!record) {
      logger.error(`removeSentWeekDate: 未找到群组记录 ${groupOID}`);
      return false;
    }

    // 设置日期时间为0点
    date.setHours(0, 0, 0, 0);

    // 获取当前的 sentWeekDates 数组
    const sentWeekDates = record.get('sentWeekDates') || [];

    // 移除匹配的日期
    const updatedDates = sentWeekDates.filter(
      (d: Date) => !isSameDay(d, date)
    );

    // 更新记录
    record.set('sentWeekDates', updatedDates);
    await record.save();

    logger.info(`removeSentWeekDate: 成功删除周报通知记录 ${groupOID}`);
    return true;
  } catch (error) {
    logger.error(`removeSentWeekDate: 删除周报通知记录失败 ${groupOID}`, error);
    return false;
  }
};

// 删除已发送的月报日期
export const removeSentMonthDate = async (groupOID: string, date: Date) => {
  try {
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('groupOID', groupOID);
    const record = await query.first();

    if (!record) {
      logger.error(`removeSentMonthDate: 未找到群组记录 ${groupOID}`);
      return false;
    }

    // 设置日期时间为0点
    date.setHours(0, 0, 0, 0);

    // 获取当前的 sentMonthDates 数组
    const sentMonthDates = record.get('sentMonthDates') || [];

    // 移除匹配的日期
    const updatedDates = sentMonthDates.filter(
      (d: Date) => !isSameDay(d, date)
    );

    // 更新记录
    record.set('sentMonthDates', updatedDates);
    await record.save();

    logger.info(`removeSentMonthDate: 成功删除月报通知记录 ${groupOID}`);
    return true;
  } catch (error) {
    logger.error(`removeSentMonthDate: 删除月报通知记录失败 ${groupOID}`, error);
    return false;
  }
};

/**
 * 添加或更新健康打卡微信群
 * @param group 健康打卡微信群信息
 * @returns 更新后的健康打卡微信群信息
 */
const addOrUpdateHealthWxGroup = async (group: IStandardizedHealthGroup) => {
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('ownerOID', group.ownerOID);
  query.equalTo('groupOID', group.groupOID);
  let record = await query.first();

  if (!record) record = new ParseDB.Object(TABLE_NAME);

  if (!record.get('ownerOID')) {
    record.set('ownerOID', group.ownerOID);
    record.set('ownerName', group.ownerName);
    record.set('ownerAccount', group.ownerAccount);
  }
  if (!record.get('groupOID')) {
    record.set('groupOID', group.groupOID);
    record.set('groupName', group.groupName);
  }
  record.set('agent', group.agent);
  record.set('type', group.type);
  record.set('groupStatus', group.groupStatus);
  record.set('stageDates', group.stageDates);
  record.set('patientIds', group.patientIds);
  record.set('sentStageDates', group.sentStageDates);
  record.set('reVisitDates', group.reVisitDates);
  record.set('sentReVisitDates', group.sentReVisitDates);
  record.set('startDate', group.startDate);
  if (group.weightLossPlan) {
    record.set('weightLossPlan', group.weightLossPlan);
  }
  const result = await record.save();
  return getRecordJson(result);
}; // Adjust the import path as needed
const assignWeightLossPlanToHealthWxGroup = async (groupOID: string, plan: { objectId: string, name: string }) => {
  if (!groupOID || !plan || !plan.objectId) return;
  logger.info(`assignWeightLossPlanToHealthWxGroup: ${groupOID} ${plan.name}`);
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('groupOID', groupOID);
  let record = await query.first();
  if (!record) { logger.error(`assignWeightLossPlanToHealthWxGroup: 未找到群组记录 ${groupOID}`); return; }

  record.set('weightLossPlan', plan);

  const result = await record.save();
  return getRecordJson(result);
}; // Adjust the import path as needed
// const sleep = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));
const sleep = (ms: number): Promise<void> =>
  new Promise(resolve => {
    setTimeout(() => {
      resolve(); // 如果需要，可以在这里传递一个特定的值
    }, ms);
  });
// 发送消息列表到群(只有使用for时才能有效延时)
const sendMsgListToGroup = async (
  agent: EthAddress,
  groupOID: string,
  groupName: string,
  groupWxid: string,
  msgList: WxMsgContent[],
  msgType: HealthMsgType = 'msgList',
): Promise<{ ret: boolean; results: boolean[] }> => {
  if (!msgList || msgList.length === 0 || !groupWxid) {
    logger.error(`发送消息列表通知: 参数为空: ${JSON.stringify(msgList)} groupWxid: ${groupWxid}`);
    return { ret: false, results: [] };
  }

  for (let i = 0; i < msgList.length; i += 1) {
    const msg = msgList[i];

    // eslint-disable-next-line no-await-in-loop
    const result = await onWechatApi.sendMsgToGroups(msg, [groupWxid], false, agent);
    if (!result) {
      return { ret: false, results: Array(msgList.length).fill(false) };
    }
    // 延时1-3秒
    const delay = 1000 + Math.floor(Math.random() * 2000);
    // eslint-disable-next-line no-await-in-loop
    await sleep(delay);
  }
  const results = Array(msgList.length).fill(true);
  // 保存发送记录
  healthNoticesServices.saveNoticesLog({
    agent,
    groupOID,
    nickName: groupName,
    wxid: groupWxid,
    msgList,
    results,
    msgType,
    dateType: 'day',
    sentDay: new Date().getDate(),
  });

  return { ret: true, results };
};

/**
 * 获取打人objectId,群名称对应的所有聊天记录中打卡数量最多的用户
 * @param agent
 * @param groupObjectId
 * @returns
 */
const getCheckInUserObjectId = async (agent: EthAddress, groupObjectId: string): Promise<string | undefined> => {
  const group = await wxGroupServices.getWxGroupInfoByObjectId(groupObjectId);
  if (!group) {
    logger.error(`${groupObjectId} getCheckInUserObjectId 群信息不存在! `);
    return undefined;
  }
  if (!group.aiKeywords?.length) {
    logger.warn(`2.${group.groupNickName} getCheckInUserObjectId 无关键词! `);
    return undefined;
  }

  const query3 = new ParseDB.Query(`AIGroupNotAtMsg_${agent}`);
  query3.equalTo('agent', agent);
  query3.equalTo('groupName', group.groupNickName);
  query3.descending('createdAt');
  const regex = new RegExp(group.aiKeywords.join('|'), 'i'); // 'i' 代表忽略大小写
  query3.matches('content', regex);

  const pipeline: any[] = [
    { $group: { _id: '$refItchatUser', count: { $sum: 1 } } }, // 按照 userName 字段分组，并统计每个 userName 出现的次数
    { $sort: { count: -1 } }, // 按 count 字段降序排列
    { $limit: 1 }, // 只返回前一个
  ];
  const results = await query3.aggregate(pipeline);
  if (!results || results.length < 1) {
    logger.warn(`${group.groupNickName} getCheckInUserObjectId 未找到打卡人信息! `);
    return undefined;
  }
  return results[0].objectId;
};

// 发送减重图表
const sendMsgStatisticsLastDays = async (hGroup: IStandardizedHealthGroup, days: number = 14, wxMsgChannel: string, test = false) => {
  const { agent, groupName, groupOID } = hGroup;

  const group = await wxGroupServices.getWxGroupInfoByObjectId(groupOID);
  if (!group.aiKeywords?.length) {
    logger.warn(`2.${groupName} 减重图表 无关键词,不发送! `);
    return;
  }

  const topUserObjectId = await getCheckInUserObjectId(agent, groupOID);
  const user = await getWxUserByObjectId(topUserObjectId);
  if (!user) {
    const msg = `${groupName} 减重图表 未找到打卡人信息,不发送! `;
    logger.warn(msg);
    // sysNoticeServices.sendMsgToGroupFor3month(agent, msg);
    return;
  }
  logger.warn(`4.${groupName} 减重图表 找到打卡人信息:${user.NickName} `);
  //---------------------------------
  // 获取打卡人周报信息数目
  const weekly = await healthWeightServices.getLastWeekRecords(agent, topUserObjectId, groupOID);
  if (!weekly || weekly.results.length < 3) {
    logger.warn(`5.减重图表 打卡人图表信息不足3条,不发送! ${groupName} ${user.NickName}F`);
    return;
  }
  //---------------------------------
  const msgData = {
    from_user_id: group?.groupUserName || group?.wxid,
    from_user_nickname: group?.groupNickName,
    actual_user_id: user.wxid || user.UserName,
    actual_user_nickname: user.NickName,
  };

  if (test) {
    logger.info(`=====>图表测试通过,已经可以发送图表;${groupName} ${user.NickName}}`);
    return;
  }
  const result = await onWechatApi.sendPluginsProc(
    `减重图表 ${days}`,
    group?.groupUserName || group?.wxid,
    group?.groupNickName, wxMsgChannel,
    msgData,
  );
  if (result) {
    logger.warn(`6.${groupName} 健康打卡群发送图表成功!`, result, group?.groupNickName, group?.groupUserName);
  } else logger.error(`7.${groupName} 健康打卡群发送图表失败!`, group?.groupNickName, group?.groupUserName);
};
// 发送复诊通知
const sendReVisitNotice = async (hGroup: IStandardizedHealthGroup, wxMsgChannel: string, test = false) => {
  // 复诊日期,已经发送的复诊日期
  const { reVisitDates, sentReVisitDates, agent, groupName, groupOID } = hGroup;
  // 判断复诊设置
  const setting = await healthSetSrv.getTwoWeekReminderSetting(agent);
  if (!setting || !setting.msgSwitch) {
    logger.debug(`复诊通知置为关闭状态 ${groupName}`);
    return false;
  }
  // 检查是否在复诊日期内,只比较年,月,日
  const now = new Date();
  const hour = now.getHours();
  const workHour = (process.env.HEALTH_WORK_HOUR || '8-20').split('-').map(Number);
  if (!test && (hour < workHour[0] || hour > workHour[1])) {
    logger.warn(`${groupName} 复诊通知 不在工作时间,不发送!`);
    return false;
  }

  // 检查是否在复诊日期内
  now.setDate(now.getDate() + 1); // 提前一天发送, +1 后,准时明天需要发送的消息就提前到今天了
  const inReVisit = reVisitDates.some(stage => isSameDay(now, stage));
  if (!inReVisit) return false;
  // 检查是否已经发送过复诊通知
  if (sentReVisitDates.some(sentStage => isSameDay(sentStage, now))) return false;

  logger.info(`健康打卡群发送复诊通知: ${hGroup.groupName}   reVisitDates: ${JSON.stringify(reVisitDates)}`);

  const group = await wxGroupServices.getWxGroupInfoByObjectId(groupOID);
  // 发送复诊通知
  const msgList: WxMsgContent[] = await healthSetSrv.getTwoWeekReminderMsgList(group.agent);

  // 先尝试记录发送状态
  const saveResult = await addSentReVisitDate(groupOID, now);
  if (!saveResult) {
    logger.error(`健康打卡群准备发送复诊通知时记录状态失败: ${groupName}`);
    sysNoticeServices.sendMsgToGroupForSystem(agent, `${groupName} 发送复诊通知记录失败，为避免重复发送，本次不发送通知`);
    return false;
  }

  // 记录状态成功后，再发送所有消息
  // 1. 首先发送减重图表(14天)
  await sendMsgStatisticsLastDays(hGroup, 14, wxMsgChannel, test);

  // 2. 两条消息之间延时1-3秒
  await sleep(1000 + Math.floor(Math.random() * 2000));

  // 3. 发送复诊通知消息
  const { ret, results } = await sendMsgListToGroup(agent, groupOID, groupName, group?.groupUserName || group?.wxid, msgList, 'reVisit');
  if (ret) {
    sentReVisitDates?.push(now);
    logger.warn(`健康打卡群发送复诊通知成功: ${groupName} `);
    return true;
  }
  const successCount = results?.filter(r => r).length;

  if (successCount > 0) {
    // 消息发送部分失败，但状态已记录，避免重复发送
    logger.error(
      `健康打卡群发送复诊通知部分失败(cow): ${JSON.stringify(results)} ${JSON.stringify(hGroup)} ${JSON.stringify(group)}`,
    );
    return false;
  }

  // 消息发送失败,删除已记录的发送状态
  await removeSentReVisitDate(groupOID, now);

  logger.error(
    `健康打卡群发送复诊通知全部失败: ${JSON.stringify(results)} ${JSON.stringify(hGroup)} ${JSON.stringify(group)}`,
  );
  return false;
};
// 发送阶段通知
const sendStagesNotice = async (hGroup: IStandardizedHealthGroup,  wxMsgChannel:string,test = false) => {
  const { stageDates, sentStageDates, agent, groupName, groupOID } = hGroup;
  // 判断阶段设置
  const setting = await healthSetSrv.getSecondStageSetting(agent);
  if (!setting || !setting.msgSwitch) {
    logger.debug(`阶段通知设置为关闭状态 ${groupName}`);
    return false;
  }
  // 检查是否在阶段日期内
  const now = new Date();

  // 北京时间8点以后,20点之前才会发送
  const hour = now.getHours();
  const workHour = (process.env.HEALTH_WORK_HOUR || '8-20').split('-').map(Number);
  if (!test && (hour < workHour[0] || hour > workHour[1])) {
    logger.warn(`${groupName} 阶段通知 不在工作时间,不发送!`);
    return false;
  }

  now.setDate(now.getDate() + 1); // 提前一天发送, +1 后,准时明天需要发送的消息就提前到今天了

  // 检查是否在阶段日期内,只比较年,月,日;提前一天发送
  const inStage = stageDates.some(stage => isSameDay(now, stage));
  if (inStage) {
    logger.info(`健康打卡群发送阶段通知: ${hGroup.groupName}   stageDates: ${JSON.stringify(stageDates)}`);
    // 检查是否已经发送过阶段通知
    if (!sentStageDates.some(sentStage => isSameDay(sentStage, now))) {
      const group = await wxGroupServices.getWxGroupInfoByObjectId(hGroup.groupOID);
      // 发送阶段通知
      const msgList: WxMsgContent[] = await healthSetSrv.getSecondStageMsgList(agent);
      if (test) {
        logger.info(`=====>阶段测试通过,已经可以发送阶段;${groupName} `);
        return true;
      }

      // 先尝试记录发送状态
      const saveResult = await addSentStageDate(groupOID, now);
      if (!saveResult) {
        logger.error(`健康打卡群准备发送阶段通知时记录状态失败: ${groupName}`);
        sysNoticeServices.sendMsgToGroupForSystem(agent, `${groupName} 发送阶段通知记录失败，为避免重复发送，本次不发送通知`);
        return false;
      }

      // 记录状态成功后，再发送消息
      const { ret, results } = await sendMsgListToGroup(
        agent,
        groupOID,
        groupName,
        group?.groupUserName || group?.wxid,
        msgList,
        'stagesNotice',
      );
      if (ret) {
        sentStageDates?.push(now);
        logger.warn(`健康打卡群发送阶段通知成功: ${groupName} `);
        return true;
      }

      if (results.length > 0) {
        // 消息发送部分失败，但状态已记录，避免重复发送
        logger.error(`健康打卡群发送阶段通知部分失败: ${JSON.stringify(results)} hGroup: ${JSON.stringify(hGroup)}`);
        return false;
      }
      logger.error(`健康打卡群发送阶段通知全部失败: ${JSON.stringify(results)} hGroup: ${JSON.stringify(hGroup)}`);
      return false;
    }
  }
  return false;
};

// 发送用户进群欢迎信息(只有未设置了群欢迎信息的微信群才发送)
const sendJoinGroupMsgList = async (group: WxGroupInfo, test = false) => {
  try {
    const isHealthGroup =
      (await getStandardizedHealthGroup(group?.objectId)) || isHealthGroup3Month(group.agent, group.groupNickName, new Date(group.createdAt));
    if (isHealthGroup && group && !group.joinWelcomeInfo) {
      const msgList: WxMsgContent[] = await healthSetSrv.getJoinGroupMsgList(group.agent);
      const { ret, results } = await sendMsgListToGroup(
        group.agent,
        group?.objectId,
        group?.groupNickName,
        group?.groupUserName || group?.wxid,
        msgList,
        'joinGroup',
      );
      if (ret) {
        logger.warn(`健康打卡群发送用户进群欢迎信息成功: ${group.groupNickName} ${msgList.length}`);
        return true;
      }
      if (results.length > 0) {
        // 部分失败,下次再发就重复了.
        logger.error(`健康打卡群发送用户进群欢迎信息部分失败:: ${JSON.stringify(results)} ${JSON.stringify(group)}`);
        return false;
      }
      logger.error(`健康打卡群发送用户进群欢迎信息全部失败:: ${JSON.stringify(results)} ${JSON.stringify(group)}`);
      return false;
    }
    return false;
  } catch (e) {
    logger.error('sendJoinGroupMsgList: 发送用户进群欢迎信息失败: ', group);
    return false;
  }
};

// 按时发送周报,如果本周打卡数据小于3条,则提醒打卡
const sendWeeklyReport = async (hGroup: IStandardizedHealthGroup, wxMsgChannel: string, test = false) => {
  const now = new Date();
  // 北京时间8点以后,20点之前才会发送(工作时间发送)
  if (!healthNoticesServices.isUserFriendlyTime(test)) {
    logger.debug(`非用户友好时间, 群组 ${hGroup.groupName} 暂不发送周报!`);
    return;
  }

  const { stageDates, agent, groupName, groupOID } = hGroup;
  const endDate = stageDates[1];

  const isDay = now.getDay() === 1;
  const isDateInRange = now <= endDate;
  const isSent = hGroup.sentWeekDates.some(sentDate => isSameDay(sentDate, now));
  const isNotTime = !isDay || !isDateInRange || isSent;

  if (!test && isNotTime) {
    if (isDay) {
      if (!isDateInRange) logger.warn(`${groupName} 减重周报未发出:减重周报超过结束日期 stageDates: ${JSON.stringify(stageDates)}`);
      else if (isSent) logger.warn(`${groupName} 减重周报未发出:减重周报已发送过了!`);
      else logger.warn(`${groupName} 减重周报未发出:原因未知`);
    }
    return;
  }

  const group = await wxGroupServices.getWxGroupInfoByObjectId(groupOID);
  if (!group.aiKeywords?.length) {
    logger.info(`2.减重周报 无关键词,不发送! ${groupName}`);
    // 通知管理员
    sysNoticeServices.sendMsgToGroupForSystem(agent, `${groupName} 减重周报 未设置关键词!`);
    return;
  }

  const topUserObjectId = await getCheckInUserObjectId(agent, groupOID);
  const user = await getWxUserByObjectId(topUserObjectId);
  if (!user) {
    const msg = `${groupName} 减重周报 未找到打卡人信息,未发出!  `;
    logger.info(msg);
    sysNoticeServices.sendMsgToGroupForSystem(agent, msg, test);
    return;
  }

  //---------------------------------
  // 获取打卡人周报信息数目
  const weekly = await healthWeightServices.getLastWeekRecords(agent, topUserObjectId, groupOID);
  if (!weekly || weekly.results.length < 3) {
    // 通知到患者所在群
    healthNoticesServices.spurCheckInAlert({
      agent,
      groupOID,
      groupWxid: group.groupUserName,
      groupName,
      // spur: '⏰ 记得及时打卡喔，本周打卡记录不足3条,所以没法给你发送周报了喔 💪',
      spur: '⏰ 记得及时打卡喔，本周打卡记录不足3条,所以没法给你发送周报了喔 💪',
      test,
    });
    const errMsg = `${groupName} ${user.NickName} 打卡,周报信息不足3条!`;
    logger.info(errMsg);
    sysNoticeServices.sendMsgToGroupForDaily(agent, errMsg, test);
    return;
  }
  //---------------------------------
  const msgData = {
    from_user_id: group?.groupUserName || group?.wxid,
    from_user_nickname: group?.groupNickName,
    actual_user_id: user.wxid || user.UserName,
    actual_user_nickname: user.NickName,
  };

  if (test) {
    logger.info(`=====>${groupName} ${user.NickName} 周报测试通过,已经可以发送周报;`);
    return;
  }

  // 先尝试记录发送状态
  const saveResult = await addSentWeekDate(groupOID, now);
  if (!saveResult) {
    logger.error(`健康打卡群准备发送周报时记录状态失败: ${groupName}`);
    sysNoticeServices.sendMsgToGroupForSystem(agent, `${groupName} 发送周报记录失败，为避免重复发送，本次不发送通知`);
    return;
  }

  // 记录状态成功后，再发送消息
  const result = await onWechatApi.sendPluginsProc(
    '/减重周报',
    group?.groupUserName || group?.wxid,
    group?.groupNickName, wxMsgChannel,
    msgData,
  );
  if (result) {
    hGroup.sentWeekDates.push(now);
    logger.warn(`6.${groupName} 健康打卡群发送周报成功!`, result, group?.groupNickName, group?.groupUserName);
  } else {
    // 消息发送失败,删除已记录的发送状态
    await removeSentWeekDate(groupOID, now);

    logger.error(`7.${groupName} 健康打卡群发送周报失败!`, group?.groupNickName, group?.groupUserName);
    // 通知管理员
    sysNoticeServices.sendMsgToGroupForDaily(agent, `${groupName} 减重周报 发送失败:cow无应答!`);
  }
};
/**
 * 按时发送月报
 */
const sendMonthlyReport = async (hGroup: IStandardizedHealthGroup, wxMsgChannel: string, test = false) => {
  const now = new Date();
  if (!healthNoticesServices.isUserFriendlyTime(test)) {
    logger.debug(`非用户友好时间...,${hGroup.groupName} 不发送月报!`);
    return;
  }

  const { stageDates, agent, groupName, groupOID } = hGroup;
  const endDate = stageDates[1];

  const isDay = now.getDate() === 1;
  const isDateInRange = now <= endDate;
  const isSent = hGroup.sentMonthDates.some(sentDate => isSameDay(sentDate, now));
  const isNotTime = !isDay || !isDateInRange || !isSent;
  if (!test && isNotTime) {
    if (isDay) {
      logger.warn(`1.==>${groupName} 减重月报不发送!`);
      if (!isDateInRange) logger.warn(`减重月报超过结束日期`);
      if (isSent) logger.warn(`减重月报已发送!`);
    }
    return;
  }

  const group = await wxGroupServices.getWxGroupInfoByObjectId(groupOID);
  if (!group.aiKeywords?.length) {
    const errorMsg = `${groupName} 减重月报 无关键词,未发出! `;
    logger.info(errorMsg);
    // 通知管理员
    sysNoticeServices.sendMsgToGroupForDaily(agent, errorMsg);
    return;
  }

  const topUserObjectId = await getCheckInUserObjectId(agent, groupOID);
  const user = await getWxUserByObjectId(topUserObjectId);
  if (!user) {
    logger.info(`4.${groupName} 减重月报 未找到打卡人信息,未发出!  `);
    return;
  }
  //---------------------------------
  // 获取打卡人周报信息数
  const monthly = await healthWeightServices.getLastMonthRecords(agent, topUserObjectId, groupOID);
  if (!monthly || monthly.results.length < 3) {
    logger.info(`5.${groupName} ${user.NickName} 减重月报 打卡人月报信息不足3条,不发送!`);
    return;
  }
  //---------------------------------
  const msgData = {
    from_user_id: group?.groupUserName || group?.wxid,
    from_user_nickname: group?.groupNickName,
    actual_user_id: user.wxid || user.UserName,
    actual_user_nickname: user.NickName,
  };

  if (test) {
    logger.info(`=====>${groupName} ${user.NickName} 月报测试通过,已经可以发送月报;`);
    return;
  }

  // 先尝试记录发送状态
  const saveResult = await addSentMonthDate(groupOID, now);
  if (!saveResult) {
    logger.error(`健康打卡群准备发送月报时记录状态失败: ${groupName}`);
    sysNoticeServices.sendMsgToGroupForSystem(agent, `${groupName} 发送月报记录失败，为避免重复发送，本次不发送通知`);
    return;
  }

  // 记录状态成功后，再发送消息
  const result = await onWechatApi.sendPluginsProc(
    '/减重月报',
    group?.groupUserName || group?.wxid,
    group?.groupNickName, wxMsgChannel,
    msgData,
  );
  if (result) {
    hGroup.sentMonthDates.push(now);
    logger.warn(`6.健康打卡群发送月报成功!===>`, result, group?.groupNickName, group?.groupUserName);
  } else {
    // 消息发送失败,删除已记录的发送状态
    await removeSentMonthDate(groupOID, now);

    logger.error(`7.健康打卡群发送月报失败!===>`, group?.groupNickName, group?.groupUserName);
  }
};
/**
 * 催促打卡信息(每小时检测一次)
 * @param hGroup
 * @param test
 * @returns
 */
const cronSpurDailyCheckIn = async ({ group, startDate, test = false }:
  { group: WxGroupInfo, startDate: Date, test: boolean }) => {
  try {
    const { agent, objectId: groupOID, groupUserName: groupWxid, groupNickName: groupName, switchs } = group;
    if (!healthNoticesServices.isUserFriendlyTime(test)) {
      logger.debug(`非用户友好时间, 群组 ${groupName} 暂不发送打卡提醒!`);
      return;
    }

    // 条件1.配置文件未打卡催促开关
    const isSpurCheckIn = process.env.HEALTH_SPUR_CHECKIN_SWITCH === 'true';
    if (!isSpurCheckIn) {
      logger.warn(`${groupName} 打卡催促总开关未开启,不催促!`);
      return;
    }
    // 条件2.群组开关关闭
    if (switchs?.includes('SPUR_CHECKIN_CLOSE')) {
      logger.warn(`${groupName} 打卡催促群组开关关闭,不催促!`);
      return;
    }
    // 条件2.四天内不提示,直接返回
    const days = moment().diff(startDate, 'days');
    if (days < 4) {
      logger.warn(`${groupName} 催促打卡信息 建群${days}天内不提醒,直接返回!`);
      return;
    }
    // 条件3,检查3天内是否有催促过
    const count = await getWxNoticesRecordsOfGroupsNDays(agent, groupOID, 'spurCheckIn', 3);
    if (count > 0) {
      logger.warn(`${groupName} 催促打卡信息 3天内已催促过,不催促!`);
      return;
    }
    // 条件3.检查打卡消息是否足够
    const userObjectId = await getCheckInUserObjectId(agent, groupOID);
    if (days >= 7 && !userObjectId) {
      const sent = await healthNoticesServices.spurCheckInAlert({
        agent,
        groupWxid,
        groupName,
        groupOID,
        msgType: 'spurCheckIn',
        spur: '⏰记得及时打卡喔.我们一起记录您的减重时刻',
        test,
      }
      );
      if (sent) {
        const errorMsg = `${groupName} 催促打卡信息: 7天内未检测到打卡人`;
        logger.warn(errorMsg);
        sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
      }
      return;
    }

    const results = await healthWeightServices.getLastNDaysRecords(agent, userObjectId, groupOID, 7);
    // 条件4.7天内打卡记录
    if (!results || results.results.length < 1) {
      const sent = await healthNoticesServices.spurCheckInAlert({
        agent,
        groupWxid,
        groupName,
        groupOID,
        msgType: 'spurCheckIn',
        spur: '⏰记得及时打卡喔.我们一起记录您的减重时刻',
        test,
      }
      );
      if (sent) {
        const errorMsg = `${groupName} 催促打卡信息: 7天内未收到打卡信息(无打卡人)`;
        logger.warn(errorMsg);
        sysNoticeServices.sendMsgToGroupForSystem(agent, errorMsg);
      }
      return;
    }

    // 条件5.检查昨天,今天是否打卡
    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    // eslint-disable-next-line no-restricted-syntax
    for (const result of results.results) {
      const createdAt = new Date(result.createdAt);
      if (isSameDay(createdAt, yesterday) || isSameDay(createdAt, today)) {
        logger.warn(`${groupName} 昨天,今天刚刚打卡过,不催了!`);
        return;
      }
    }

    // 条件6.7天内打卡记录不足3条
    if (results.results.length < 1) {
      healthNoticesServices.spurCheckInAlert({
        agent,
        groupOID,
        groupWxid,
        groupName,
        msgType: 'spurCheckIn',
        spur: '⏰ 记得及时打卡喔，本周打卡记录不足3条,我们一起记录您的减重时刻. 💪\n\n输入"减重打卡"查看打卡格式',
        test,
      });

    }

  } catch (e) {
    logger.error(`催促打卡信息发生意外: ${group?.groupNickName}`, e);
    sysNoticeServices.sendMsgToGroupForSystem(group?.agent, `${group?.groupNickName} 催促打卡信息 发生意外`);
  }
}
/**
 * 设置健康打卡群的默认关键词(如果aiKeywords为空)
 * @param objectId
 */
const setWxGroupKeywordsOfHealth = async (objectId: string) => {
  await setWxGroupKeywords(objectId, process.env.HEALTH_CHECKIN_KEYWORDS?.split(',')
    || ['减脂操', '早起体重', '早上体重', '代谢操', '共减重']);
}

/**
 * 是否在一个月内
 * @param startDate
 * @returns
 */
const isWithinOneMonth = (startDate: Date) => {
  const now = moment();
  const start = moment(startDate).startOf('day');
  const end = moment(startDate).add(1, 'months').endOf('day');
  return now.isSameOrAfter(start) && now.isSameOrBefore(end);
}
const isWithInThreeMonth = (startDate: Date) => {
  const now = moment();
  const start = moment(startDate).startOf('day');
  const end = moment(startDate).add(3, 'months').endOf('day');

  // 添加日志以便调试
  logger.debug(`isWithInThreeMonth: startDate=${start.format('YYYY-MM-DD')}, endDate=${end.format('YYYY-MM-DD')}, now=${now.format('YYYY-MM-DD')}, result=${now.isSameOrAfter(start) && now.isSameOrBefore(end)}`);

  return now.isSameOrAfter(start) && now.isSameOrBefore(end);
}
const isWithinSixMonth = (startDate: Date) => {
  const now = moment();
  const start = moment(startDate).startOf('day');
  const end = moment(startDate).add(6, 'months').endOf('day');

  // 添加日志以便调试
  logger.debug(`isWithinSixMonth: startDate=${start.format('YYYY-MM-DD')}, endDate=${end.format('YYYY-MM-DD')}, now=${now.format('YYYY-MM-DD')}, result=${now.isSameOrAfter(start) && now.isSameOrBefore(end)}`);

  return now.isSameOrAfter(start) && now.isSameOrBefore(end);
}
const isWithinThreeDays = (startDate: Date) => {
  const now = moment();
  const start = moment(startDate).startOf('day');
  const end = moment(startDate).add(3, 'days').endOf('day');
  return now.isSameOrAfter(start) && now.isSameOrBefore(end);
}
/**
 * 是否在阶段日期范围内
 * @param hGroup
 * @returns
 */
const isDateInRange = (hGroup: IStandardizedHealthGroup) => {
  const now = moment();
  const { stageDates } = hGroup;
  const endDate = moment(stageDates[1]).endOf('day');
  return now.isSameOrBefore(endDate);
}
interface ProcessGroupConfig {
  type: 'MONTH1' | 'MONTH3' | 'MONTH6';
  durationMonths: number;
  isWithinDuration: (date: Date) => boolean;
  sendNotification: (agent: EthAddress, msg: string) => Promise<void>;
  groupNamePrefix: string;
}

const GROUP_CONFIGS: Record<string, ProcessGroupConfig> = {
  MONTH1: {
    type: 'MONTH1',
    durationMonths: 1,
    isWithinDuration: isWithinOneMonth,
    sendNotification: (agent: EthAddress, msg: string) =>
      sysNoticeServices.sendMsgToGroupFor1month.bind(sysNoticeServices)(agent, msg),
    groupNamePrefix: '1-'
  },
  MONTH3: {
    type: 'MONTH3',
    durationMonths: 3,
    isWithinDuration: isWithInThreeMonth,
    sendNotification: (agent: EthAddress, msg: string) =>
      sysNoticeServices.sendMsgToGroupForDaily.bind(sysNoticeServices)(agent, msg),
    groupNamePrefix: '3-'
  },
  MONTH6: {
    type: 'MONTH6',
    durationMonths: 6,
    isWithinDuration: isWithinSixMonth,
    sendNotification: (agent: EthAddress, msg: string) =>
      sysNoticeServices.sendMsgToGroupFor6month.bind(sysNoticeServices)(agent, msg),
    groupNamePrefix: '6-'
  }
};

const procMonthGroupPerHour = async (groups: Array<WxGroupInfo>, config: ProcessGroupConfig, wxMsgChannel:string,test = false) => {
  const results: any[] = [];

  await groups.reduce(async (promise, group) => {
    await promise;

    try {
      // 已经创建过了,不需要从名称获取,也许start date 是医生设置了,命名规则不同的微信群
      const hGroup = await getStandardizedHealthGroup(group.objectId)
      const startDate = hGroup?.startDate || getDateFromGroupNameMonth136(EthZero, group.groupNickName, new Date(group.createdAt));
      if (!startDate) {
        logger.warn(`无法获取健康群开始日期: ${group.groupNickName}`);
        return;
      }

      if (!config.isWithinDuration(startDate)) {
        logger.warn(`${group.groupNickName} 超过${config.durationMonths}个月,中断cron处理`);
        setWxGroupHealthExpired(group.agent, group.objectId)
        return;
      }

      // 已经是创建的健康群,执行各种轮询操作
      if (hGroup) {
        if (!healthNoticesServices.isUserFriendlyTime(test)) {
          logger.debug(`非用户友好时间, 暂不发送${config.durationMonths}个月群组的通知!`);
          return;
        }

        const { agent, groupOID, groupName } = hGroup;
        const { groupUserName: groupWxid } = group;

        await setWxGroupKeywordsOfHealth(group.objectId);
        await sendStagesNotice(hGroup, wxMsgChannel,test);
        await sendReVisitNotice(hGroup,  wxMsgChannel,test);
        await sendWeeklyReport(hGroup, wxMsgChannel, test);
        await sendMonthlyReport(hGroup,  wxMsgChannel,test);

        // 一个月内才会各种催促
        if (isWithinOneMonth(startDate)) {
          await cronSpurDailyCheckIn({ group, startDate, test });
          await cronJobSpurCaloriesPunch({ agent, groupOID, groupWxid, groupName, startDate, test });
        }

        await sleep(1000);
        return;
      }
      // 新的健康打卡群,建档
      logger.warn(`建档新的${config.durationMonths}月群: ${group.groupNickName}`);
      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + config.durationMonths);

      const reVisitDates = Array.from({ length: 7 }, (_, i) => {
        const date = new Date(startDate);
        date.setDate(date.getDate() + i * 14);
        return date;
      });

      const healthGroup: IStandardizedHealthGroup = {
        agent: group.agent,
        ownerAccount: group.owner_Account,
        ownerOID: group.owner_ObjectId,
        ownerName: group.owner_NickName,
        groupOID: group.objectId,
        groupName: group.groupNickName,
        type: config.type,
        groupStatus: 'ACTIVE',
        stageDates: [startDate, endDate],
        patientIds: [],
        sentStageDates: [],
        sentWeekDates: [],
        sentMonthDates: [],
        reVisitDates,
        sentReVisitDates: [],
        startDate: startDate,
        weightLossPlan: group.weightLossPlan,
        createAt: group.createdAt,
        updateAt: group.updatedAt
      };

      if (test) {
        results.push(healthGroup);
      } else {
        const result = await addOrUpdateHealthWxGroup(healthGroup);
        logger.info(`设置${config.durationMonths}月群: ${group.groupNickName} ${group.owner_NickName}`);
        logger.info(`设置${config.durationMonths}月群: ${result}`);
        results.push(result);
      }

      await sleep(1000);
      const msg = `${group.groupNickName} 初始化微信群复诊及阶段消息发送设置(${config.groupNamePrefix})`;
      logger.warn(msg);
      await config.sendNotification(group.agent, msg);
    } catch (error) {
      console.log(error);
      const msg = `${config.groupNamePrefix}群组消息发送发生意外: ${group.groupNickName}`;
      logger.error(msg);
      await config.sendNotification(group.agent, msg);
    }
  }, Promise.resolve());

  return results;
};

// 更新现有的cronJob函数使用新的统一处理函数
const cronJobMonth1Group = async (test = false) => {
  const { results: month1list } = await getGroupsBySearch(EthZero, 1, 300, {
    groupNameRegex: /^1-/i,
    status: ['NORMAL'],
  });
  return procMonthGroupPerHour(month1list, GROUP_CONFIGS.MONTH1, test);
};

const cronJobMonth3Group = async (test = false) => {
  const { results: month3list } = await getGroupsBySearch(EthZero, 1, 300, {
    groupNameRegex: /^3-/i,
    status: ['NORMAL'],
  });
  return procMonthGroupPerHour(month3list, GROUP_CONFIGS.MONTH3, test);
};

const cronJobMonth6Group = async (test = false) => {
  const { results: month6list } = await getGroupsBySearch(EthZero, 1, 300, {
    groupNameRegex: /^6-/i,
    status: ['NORMAL'],
  });
  return procMonthGroupPerHour(month6list, GROUP_CONFIGS.MONTH6, test);
};

// 1.检查健康打卡微信群是否已经初始化,确保已经初始化,
// 2.检查健康打卡微信群阶段,根据日期发送阶段通知
// 3.检查健康打卡微信群是否进入复诊,进入复诊则发送复诊通知
// 每小时通知一次
const cronJobHealthPatientsWxGroup = async (test = false) => {
  await cronJobMonth3Group(test);
  await sleep(3000);
  await cronJobMonth6Group(test);
  await sleep(3000);
  await cronJobMonth1Group(test);
}

export {
  assignWeightLossPlanToHealthWxGroup, cronJobHealthPatientsWxGroup,
  getDateFromGroupName3Month,
  getDateFromGroupName6Month, getDateFromGroupNameMonth136, getRecordJson, getStandardizedHealthGroup, isHealthGroup3Month,
  isHealthGroup6Month, isHealthGroupMonth136, sendJoinGroupMsgList
};
