/**
 * 聊天记录管理服务
 *
 * 提供聊天记录相关功能:
 * - 聊天记录存储
 * - 消息统计分析
 * - AI内容生成
 * - 群消息分析
 * - 用户消息分析
 * - 关键词提取
 *
 * <AUTHOR> <<EMAIL>>
 * @date 2024
 */

import { EthAddress, GResponse } from '../../types/types';
import ApiError from '../../utils/api_error';
import logger from '../../utils/logger';
import {
  isEmpty,
  isEthAddress
} from '../../utils/utils';
import userServices from '../users/userServices';

import { ParseDB, ParseRef } from '../../database/database';

import baseServices, { BaseServices } from '../baseServices';
import gateAdminApi from '../gateSSO/gateSSOAdminApi';
import session from '../sessionManager';

import itchatGroup from '../wechat/cowGroupClass';





import { isMyWathingUser } from '../users/watchingServices';
import onWechatApi from '../wechat/onWechatApi';
import { getRecordJson, IChatInfo, TABLE_NAME, WxGroupStatistics } from './chatlistTypes';
import doctorServices from './doctorServices';
import myReplyServices, { getReplyHistoryLocal, IReply } from './myReplyServices';
import * as userStatistics from './userWxMsgStatistics';
import { WatchingUserStatistics, WxUserStatistics } from './userWxMsgStatistics';
import { addOrUpdateWxUser, getItchatUserInfo } from '../wechat/cowUserFunctions';
import { addOrUpdateItchatGroup } from '../wechat/cowGroupFunction';
import { NotAtMsgTasks } from '../health/types';
import { isArray } from 'util';
import { getGroupxUserByItchat } from '../users/userFunction';

const { v4: uuidv4 } = require('uuid');



const MY_KNOWLEDGE = 231;
const CHATLIST_REPLY = 23;
const CHATLIST_UNREPLY = 10;

// 获取指定用户，且是群所有者的聊天记录
const getChatListAll = async ({
  sessionId,
  pageSize,
  pageNum,
  isNoReply = false,
}: {
  sessionId: string;
  pageSize: number;
  pageNum: number;
  isNoReply?: boolean;
}): Promise<IChatInfo[]> => {
  // 验证session
  const sess = await session.validateSession(sessionId);
  if (!sess) throw new ApiError('session is error', 60001);
  // 验证gate sso chatlist 读取权限
  const result =
    (await baseServices.isSuperMan(sessionId)) ||
    (await gateAdminApi.checkPermission(sess.platformUserId, process.env.GATE_SSO_PERMISSION_CHATLIST));
  if (!result) {
    throw new ApiError('无读取问答列表权限', 60002);
  }

  // 当微信端发送信息给机器人的时候,要确保这个医生是群主时才给医生看到
  // const groupsOfDoctor = await myWxGroups.getGroupsOfUser(sess.account);
  // logger.info("groupsOfDoctor:", groupsOfDoctor, sess.name, sess.account);

  // 我是聊��记录中对应的myDoctor
  const query1 = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));
  if (isNoReply) query1.doesNotExist('replyCount');

  query1.startsWith('source', 'iKnow-on-wechat');
  query1.equalTo('myDoctor', sess.account);

  // 我是聊天记录中对应的receiver
  const ChatRecordDB = ParseDB.Object.extend(TABLE_NAME);
  const query2 = new ParseDB.Query(ChatRecordDB);
  if (isNoReply) query2.doesNotExist('replyCount');

  query2.equalTo('receiver', sess.account);

  // 我是聊天记录中对应的receiver或者myDoctor
  const query = ParseDB.Query.or(query1, query2);

  query.descending('updatedAt');
  query.notContainedIn('disLikeAccount', [sess.account]);
  query.limit(pageSize);
  // 执行计数查询
  const count = await query.count();
  const skip = pageNum * pageSize;
  if (skip < count) {
    query.skip(skip);
  } else {
    logger.info('skip is bigger than count');
    return [];
  }
  const data = await query.find();
  return data.map(getRecordJson);
};
// 获取聊天记录(读取自己发送的,不需要权限检查)
const getChatListMySent = async ({
  sessionId,
  pageSize,
  pageNum,
}: {
  sessionId: string;
  pageSize: number;
  pageNum: number;
}): Promise<IChatInfo[]> => {
  // 验证session
  const sess = await session.validateSession(sessionId);
  if (!sess) throw new ApiError('session is error', 60001);

  const ChatRecordDB = ParseDB.Object.extend(TABLE_NAME);
  const query = new ParseDB.Query(ChatRecordDB);

  // 执行计数查询
  const count = await query.count();
  const skip = pageNum * pageSize;
  // 执行查询
  query.descending('updatedAt');
  query.equalTo('account', sess.account);
  query.notContainedIn('disLikeAccount', [sess.account]);
  query.limit(pageSize);
  if (skip < count) {
    query.skip(skip);
  } else {
    logger.info('skip is bigger than count');
    return [];
  }
  const data = await query.find();
  const chatList: IChatInfo[] = [];
  data.forEach(item => {
    const chatInfo: IChatInfo = getRecordJson(item);
    chatList.push(chatInfo);
  });
  logger.info(`getChatListMySent: pageNum:${pageNum},pagesize:${pageSize},resp:${chatList.length} `);
  return chatList;
};

// 获取聊天记录列表(通过conversationId)
const getChatListByCID = async ({
  sessionId,
  conversationId,
  pageSize,
  pageNum,
}: {
  sessionId: string;
  conversationId: string;
  pageSize: number;
  pageNum: number;
}): Promise<IChatInfo[]> => {
  // 验证session
  const sess = await session.validateSession(sessionId);
  if (!sess) throw new ApiError('session is error', 60001);

  const ChatRecordDB = ParseDB.Object.extend(TABLE_NAME);
  const query = new ParseDB.Query(ChatRecordDB);

  // 查询条件
  query.descending('updatedAt');
  query.equalTo('conversationId', conversationId);
  query.notContainedIn('disLikeAccount', [sess.account]);
  query.limit(pageSize);
  // 执行计数查询
  const count = await query.count();
  const skip = pageNum * pageSize;

  if (skip < count) {
    query.skip(skip);
  } else {
    logger.info('skip is bigger than count');
    return [];
  }
  const data = await query.find();
  const chatList: IChatInfo[] = [];
  data.forEach(item => {
    const chatInfo: IChatInfo = getRecordJson(item);
    chatList.push(chatInfo);
  });
  logger.info(`getChatListMySent: pageNum:${pageNum},pagesize:${pageSize},resp:${chatList.length} `);
  return chatList;
};
// 获取聊天记录(读取发送给自己的,不需要权限检查)
const getChatListToMe = async ({
  sessionId,
  pageSize,
  pageNum,
}: {
  sessionId: string;
  pageSize: number;
  pageNum: number;
}): Promise<IChatInfo[]> => {
  // 验证session
  const sess = await session.validateSession(sessionId);
  if (!sess) throw new ApiError('session is error', 60001);

  const ChatRecordDB = ParseDB.Object.extend('AIMyReply');
  const query = new ParseDB.Query(ChatRecordDB);

  // 执行计数查询
  const count = await query.count();
  const skip = pageNum * pageSize;
  // 执行查询
  query.descending('updatedAt');
  query.equalTo('receiver', sess.account);
  query.notContainedIn('disLikeAccount', [sess.account]);
  query.limit(pageSize);
  if (skip < count) {
    query.skip(skip);
  } else {
    logger.info('skip is bigger than count');
    return [];
  }

  const data = await query.find();
  const chatList: IChatInfo[] = [];
  data.forEach(item => {
    const chatInfo: IChatInfo = getRecordJson(item);
    chatList.push(chatInfo);
  });
  logger.info(`getChatListToMe: pageNum:${pageNum},pagesize:${pageSize},resp:${chatList.length} `);
  return chatList;
};
const getChatDetail = async (objectId: string) => {
  const ChatRecordDB = ParseDB.Object.extend(TABLE_NAME);
  const query = new ParseDB.Query(ChatRecordDB);
  query.equalTo('objectId', objectId);
  const data = await query.first();

  const chatInfo: IChatInfo = getRecordJson(data);

  // if (data) {
  //   data.set("status","read")
  //   data.save();
  // }

  logger.info(`getChatDetail: ${JSON.stringify(chatInfo)}`);
  return chatInfo;
};

type IChatHistory = {
  objectId?: string;

  account: EthAddress;
  senderName: string;
  receiver: string;
  receiverName: string;

  messageId: string;
  message: string;
  messageType: string;
  messageDate: string;

  conversationId: string;
  updatedAt?: string;
};
// 获取我��某人的聊天记录,默认获取最后50条记录
const getChatHistoryByCID = async ({
  sessionId,
  conversationId,
  pageSize,
  pageNum,
}: {
  sessionId: string;
  pageSize: number;
  pageNum: number;
  conversationId: string;
}): Promise<IChatHistory[]> => {
  // 验证session
  const sess = await session.validateSession(sessionId);
  if (!sess) throw new ApiError('session is error', 60001);

  const query = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));

  // 设置查询条件
  query.descending('updatedAt');
  query.equalTo('conversationId', conversationId);
  query.notContainedIn('disLikeAccount', [sess.account]);
  query.limit(pageSize);
  // 执行计数查询
  const count = await query.count();
  const skip = pageNum * pageSize;

  if (skip < count) {
    query.skip(skip);
  } else {
    logger.info('skip is bigger than count');
    return [];
  }
  const chatList: IChatHistory[] = [];

  const data = await query.find();
  data.forEach(item => {
    const chatInfo: IChatInfo = getRecordJson(item);

    chatList.push({
      objectId: chatInfo.objectId,
      messageId: chatInfo.messageId,
      account: chatInfo.account,

      message: chatInfo.message,
      messageType: chatInfo.messageType,
      messageDate: chatInfo.messageDate,

      conversationId: chatInfo.conversationId,
      senderName: chatInfo.userName,
      receiverName: chatInfo.receiverName,
      receiver: chatInfo.receiver,
    });
  });
  //-------------------------------------------------
  const data2 = await myReplyServices.getMyReplyListByCID({
    sessionId,
    conversationId,
    pageSize,
    pageNum,
  });

  data2.forEach(item => {
    chatList.push({
      account: item.account,
      objectId: item.objectId,

      messageId: item.messageId,
      message: item.message,
      messageType: '0',
      messageDate: item.updatedAt,
      conversationId: item.conversationId,
      senderName: item.userName,
      receiverName: item.chatUserName,
      receiver: item.chatAccount,
    });
  });
  logger.info(`getChatList: pageNum:${pageNum},pagesize:${pageSize},resp:${chatList.length} `);
  return chatList;
};
// 获取我与某人的聊天记录,包括我发=>对方收,对方发=>我收,同时包括同条件的回复记录
// 无session 检测,需要提供sender 和receiver
const getChatHistoryLocal = async ({
  sender,
  otherSide,
  pageSize = 50,
  pageNum = 1,
}: {
  pageSize?: number;
  pageNum?: number;
  sender: EthAddress; // 默认为sess中的account
  otherSide: EthAddress;
}): Promise<IChatHistory[]> => {
  const query1 = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));
  // 设置查询条件
  query1.equalTo('account', sender);
  query1.equalTo('receiver', otherSide);
  query1.notContainedIn('disLikeAccount', [sender]);
  query1.descending('updatedAt');
  query1.limit(pageSize);
  // 执行计数查询
  const count1 = await query1.count();
  const skip1 = pageNum * pageSize;

  if (skip1 < count1) {
    query1.skip(skip1);
  } else {
    logger.info('skip is bigger than count');
  }

  const query2 = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));
  // 设置查询条件
  query2.equalTo('account', otherSide);
  query2.equalTo('receiver', sender);
  query2.notContainedIn('disLikeAccount', [sender]);
  query2.descending('updatedAt');
  query2.limit(pageSize);
  // 执行计数查询
  const count2 = await query2.count();
  const skip2 = pageNum * pageSize;

  if (skip2 < count2) {
    query1.skip(skip2);
  } else {
    logger.info('skip is bigger than count');
  }

  const mainQuery = ParseDB.Query.or(query1, query2);

  const chatList: IChatHistory[] = [];

  const data = await mainQuery.find();
  data.forEach(item => {
    const chatInfo: IChatInfo = getRecordJson(item);
    chatList.push({
      objectId: chatInfo.objectId,
      account: chatInfo.account,
      senderName: chatInfo.userName,

      conversationId: chatInfo.conversationId,
      messageId: chatInfo.messageId,
      message: chatInfo.message,
      messageType: chatInfo.messageType,
      messageDate: chatInfo.messageDate,

      receiverName: chatInfo.receiverName,
      receiver: chatInfo.receiver,
    });
  });
  //-------------------------------------------------
  const data2 = await getReplyHistoryLocal({
    sender,
    otherSide,
    pageSize,
    pageNum,
  });

  data2.forEach(item => {
    chatList.push({
      objectId: item.objectId,
      senderName: item.userName,
      account: item.account,

      conversationId: item.conversationId,
      messageId: item.messageId,
      message: item.replyMessage,
      messageType: '0',
      messageDate: item.updatedAt,

      receiverName: item.chatUserName,
      receiver: item.chatAccount,
    });
  });
  logger.info(`getChatList: pageNum:${pageNum},pagesize:${pageSize},resp:${chatList.length} `);
  return chatList;
};
// 获取我与某人的聊天记录,包括我发=>对方收,对方发=>我收,同时包括同条件的回复记录
const getChatHistory = async ({
  sessionId,
  sender,
  otherSide,
  pageSize,
  pageNum,
}: {
  sessionId: string;
  pageSize: number;
  pageNum: number;
  sender?: EthAddress; // 默认为sess中的account
  otherSide: EthAddress;
}): Promise<IChatHistory[]> => {
  // 验证session
  const sess = await session.validateSession(sessionId);
  if (!sess) throw new ApiError('session is error', 60001);
  return getChatHistoryLocal({
    sender,
    otherSide,
    pageSize,
    pageNum,
  });
};
const replyChatRecord = async ({
  account,
  sessionId,
  replyData,
  wxMsgChannel,
}: {
  account: EthAddress;
  sessionId: string;
  replyData: IReply;
  wxMsgChannel: string;
}) => {
  if (isEmpty(replyData?.replyMessage)) throw new Error('replyMessage is empty');

  const sess = await session.validateSession(sessionId);
  if (!sess) {
    throw new ApiError('session is error', 60001);
  }
  let user = await userServices.serviceGetUserDetails({ account });
  if (!user) user = await userServices.serviceGetUserBySSO(sess.userId);

  // 验证gate sso chatReply 读取权限
  const p = await gateAdminApi.checkPermission(sess.platformUserId, process.env.GATE_SSO_PERMISSION_CHATREPLY);

  if (!p) throw new ApiError('无回复问答权限', 60002);

  const ChatRecordDB = ParseDB.Object.extend(TABLE_NAME);

  const objectId: string = replyData?.objectId;
  const query = new ParseDB.Query(ChatRecordDB);
  query.equalTo('objectId', objectId);

  const chatRecord = await query.first();

  if (!chatRecord) {
    logger.error(`No chat record found for id ${objectId}`);
    throw new Error(`No chat record found for id ${objectId}`);
  }
  // 修改本条记录为已回复
  chatRecord.set({ status: 'reply' });
  chatRecord.increment('replyCount');

  const result = await chatRecord.save();
  if (!result) {
    throw new Error('save chat record error');
  }

  const chat = getRecordJson(result);

  // 将回复信息添加到回复表中
  const data: IReply = {
    messageId: uuidv4(),
    message: chat.message,
    messageType: chat.messageType,

    aiResponse: chat.aiResponse,
    conversationId: chat.conversationId,
    chatAccount: chat.account,

    replyMessage: replyData.replyMessage,

    account: sess.account,
    chatObjectId: objectId,
    favorited: false,
    chatUserName: chat.userName,
    chatUserObjectId: chat.userObjectId,
    userName: sess.name,
  };
  const replyResult = await myReplyServices.insertReply2DB(data);

  if (replyResult) {
    logger.info(`Updated chat record ${objectId}`);
    // 将答疑人作为我的医生
    await doctorServices.setMyDoctorToDB({
      account: chat.account,
      name: chat.userName,
      groupName: chat.wxGroupName,
      agent: chat.receiver,
      doctorAccount: sess.account,
      doctorName: user.professionalName || sess.name,
      doctorDepartment: user.department,
      refDoctor: {
        __type: 'Pointer',
        className: 'Users',
        objectId: user.objectId,
      },
    });
    // 通过微信发送'有新消息'通知:
    const userId = (
      await getItchatUserInfo(chat.receiver, {
        objectId: chat.userObjectId,
        NickName: chat.userName,
        UserName: chat.userId,
        HeadImgUrl: chat.userAvatar,
      })
    )?.UserName;
    const groupId = (await itchatGroup.getWxGroupInfoByObjectId(chat.wxGroupObjectId))?.groupUserName;

    let head = '';
    const body = data.replyMessage;
    const tail = `${process.env.IKNOW_WEB_URL}/#/pages/sso-gate/sso-redirect?url=/pages/tabbar/tabbar-2/message-page`;

    let toUserId = userId;
    if (chat.wxGroupId) {
      head = `@${chat.userName}\n`;
      toUserId = groupId;
    }
    const doctorInfo = `来自: ${user.professionalName}(${user.department})`;

    // 通过 toUserId 前缀@@能区分出是否为群
    onWechatApi.sendText(`${head}${body}\n${tail}\n${doctorInfo}`, toUserId, chat.userName, wxMsgChannel);
  } else {
    logger.error(`Failed to update chat record ${objectId}`);
  }
  return replyResult;
};

const delChat = async ({ sessionId, objectId }: { sessionId: string; objectId: string }) => {
  // 验证session
  const sess = await session.validateSession(sessionId);
  if (!sess) throw new ApiError('session is error', 60001);
  const ChatRecordDB = ParseDB.Object.extend(TABLE_NAME);
  const query = new ParseDB.Query(ChatRecordDB);
  query.equalTo('objectId', objectId);
  const record = await query.first();

  if (record) {
    const disLikeAccount = record.get('disLikeAccount') || [];
    disLikeAccount.push(sess.account);
    record.set('disLikeAccount', disLikeAccount);
    return record.save();
  }
  return null;
};

const updateChatRecord = async ({
  account,
  sessionId,
  updateData,
}: {
  account: EthAddress;
  sessionId: string;
  updateData: IChatInfo;
}) => {
  if (!(await session.validateSession(sessionId))) {
    throw new ApiError('session is error', 60001);
  }
  const objectId: string = updateData?.objectId;
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('objectId', objectId);

  const chatRecord = await query.first();

  if (!chatRecord) {
    logger.error(`No chat record found for id ${objectId}`);
    throw new Error(`No chat record found for id ${objectId}`);
  }

  chatRecord.set({
    status: updateData.status,
  });

  const result = await chatRecord.save();

  if (result) {
    logger.info(`Updated chat record ${objectId}`);
  } else {
    logger.error(`Failed to update chat record ${objectId}`);
  }
  return result;
};
//---------------------------------------------------------------
type TotalInfoUser = {
  account: EthAddress;
  updatedAt: Date;
  mySendMsg: number;
  sendToMe: number;
  unRead: number;
  myAllMsg: number;
  myKnowledge: number;
  myReply: number;
  mySubject: number;
  balanceAITokens: number; // 我的AI代币余额
  usedAITokens: number; // 我的AI代币消耗
  totalAITokens: number; // 我的AI代币总数
};
const getTotalInfoJson = (record: ParseDB.Object): TotalInfoUser => ({
  account: record.get('account'),
  updatedAt: record.get('updatedAt'),
  mySendMsg: record.get('mySendMsg'), // 我发送的消息
  sendToMe: record.get('sendToMe'), // 发送给我的消息
  unRead: record.get('unRead'), // 新的未读消息
  myAllMsg: record.get('myMsg'), // 我的所有消息，包括发送和接收的
  myKnowledge: record.get('myKnowledge'), // 我的知识库
  myReply: record.get('myReply'), // 我回复他人的消息
  mySubject: record.get('mySubject'), // 我参与的主题数
  balanceAITokens: record.get('balanceAITokens') || 0, // 我的AI代币余额
  usedAITokens: record.get('usedAITokens') || 0, // 我的AI代币消耗
  totalAITokens: record.get('totalAITokens') || 0, // 我的AI代币总数
});



// 判断内容是否包含关键词任一一个
function containsKeywordSome(content: string, keywords: Array<string>) {
  return keywords.some(keyword => content.includes(keyword));
}

/**
 * 更新NotAt聊天记录的AI内容
 */
const updateChatRecordForAIContent = async (tableName: string, objectId: string,
  aiGenContent: string, aiGenContentJson: Object,
  aiResponse: any, doneTasks: NotAtMsgTasks | NotAtMsgTasks[]) => {
  try {
    const query = new ParseDB.Query(tableName);
    query.equalTo('objectId', objectId);

    const record = await query.first();
    if (!record) return;

    record.set('aiGenContent', aiGenContent);

    if (Array.isArray(aiGenContentJson)) record.set('aiGenContentJson', aiGenContentJson);
    else record.set('aiGenContentJson', [aiGenContentJson]);

    record.set('aiResponse', aiResponse);

    if (doneTasks) {
      if (Array.isArray(doneTasks)) {
        // 如果是数组，添加每个任务
        doneTasks.forEach(task => record.add("doneTasks", task));
      } else {
        // 如果是单个任务，直接添加
        record.add("doneTasks", doneTasks);
      }
    }
    await record.save();
  }
  catch (e) {
    logger.error(`updateChatRecordForAIContent error:${e}`);
  }
}

//---------------------------------------------------------------
class ChatServices extends BaseServices {
  insertChatRecord = async ({
    account,
    sessionId,
    body,
  }: {
    account: EthAddress;
    sessionId: string;
    body: IChatInfo;
  }): Promise<{
    objectId: string; // 聊天记录 AIChatRecords 的objectId
    groupObjectId: string; // AIItchatGroups 中的objectId
    userObjectId: string; // AIItchatUsers 中的objectId
    account: EthAddress; // 发送人账号
    myDoctor: EthAddress; // 我的医生账号
  }> => {
    let senderAccount = account;
    let userObjectId = ''; // 在 itchatWechatUser中的objectId
    let myDoctor: EthAddress = null;
    let reqGroupObjectId = body.wxGroupObjectId;

    const { receiver } = body;

    // 可以先不验证session,来自微信时,sessionId为空
    if (body?.source?.includes('on-wechat')) {
      const agent = receiver; // 微信端的消息，接收者一定是agent
      const isGroup = !!body.wxGroupId;

      // 发送人，如果没有account，则需要通过 itchatWechatUsers 获取用户的account
      // 如果 itchatWechatUsers 也没有account，则需要通过 Users 获取用户的account

      const user = await addOrUpdateWxUser(agent, body.wxUser, true); // 从itchat中获取用户

      userObjectId = user.objectId;
      if (!isEthAddress(senderAccount)) {
        if (isEthAddress(user?.account)) senderAccount = user?.account;
        else {
          const u = await getGroupxUserByItchat(body.wxUser); // 从Users中获取account
          if (isEthAddress(u?.account)) senderAccount = u.account;
        }
      }

      // 获取医生信息
      let doctor;
      // 群对应的医生
      if (isGroup) {
        // 群,如果用户没有提供groupObjectId,需要通过itchatWechatGroups获取群objectId并更新群信息
        if (!reqGroupObjectId) {
          const g = await addOrUpdateItchatGroup(agent, {
            agent,
            groupUserName: body.wxGroupId,
            groupNickName: body.wxGroupName,
            groupHeadImgUrl: body.wxGroupHeadImgUrl,
          });
          reqGroupObjectId = g?.objectId;
        }
        doctor = await itchatGroup.getDoctorOfGroup({
          objectId: reqGroupObjectId,
          groupId: body.wxGroupId,
          groupName: body.wxGroupName,
        });
      } else {
        // 用户默认对应的医生(专家)
        doctor = await doctorServices.getMyDoctor({
          account,
          agent,
          wxUserName: body.userName,
          wxUserId: body.userId,
        });
      }

      if (doctor) {
        myDoctor = doctor.account;
      }
    } else {
      await this.checkSession(sessionId);
    }
    const req: IChatInfo = {
      account: senderAccount,
      receiver, // 聊天指定接收人,来自微信的消息时，一定是agent
      receiverName: body.receiverName,
      receiverAvatar: body.receiverAvatar,

      conversationId: body.conversationId,
      action: body.action,
      model: body.model,
      internetAccess: body.internetAccess,
      aiResponse: body.aiResponse,

      userName: body.userName,
      userAvatar: body.userAvatar,
      userId: body.userId,
      userObjectId,
      message: body.message,
      messageId: body.messageId,
      messageType: String(body.messageType),
      status: 'first',

      source: body.source,
      wxReceiver: body.wxReceiver,
      wxGroupId: body.wxGroupId,
      wxGroupName: body.wxGroupName,
      wxGroupObjectId: reqGroupObjectId,
      // 专家d
      myDoctor,
    };
    // 存储聊天记录到表中
    const ChatRecordDB = ParseDB.Object.extend(TABLE_NAME);
    const chat = new ChatRecordDB();
    chat.set(req);
    const result = await chat.save();

    if (result?.id) {
      logger.info('insertChatRecord success: ', {
        account: result.account,
        message: result.message,
        wxGroupName: result.wxGroupName,
      });
      // 通过微信发送'有新消息'通知:
      // onWechatApi.sendText(`有新消息：${body.receiverName}`, body.receiver);
      // const msg = req.message?.toLowerCase();
      // 服务器内部消息,检测互通情况
    }
    return { objectId: result.id, account: senderAccount, myDoctor, groupObjectId: reqGroupObjectId, userObjectId };
  };


  getTotalInfo = async (sessionId: string): Promise<TotalInfoUser> => {
    const sess = await this.checkSession(sessionId);

    const query = new ParseDB.Query('AIMyTotalInfo');
    query.equalTo('account', sess.account);

    const result = await query.first();
    if (!result)
      return {
        account: sess.account,
        updatedAt: new Date(),
        mySendMsg: 0, // 我发送的消息
        sendToMe: 0, // 发送给我的消息
        unRead: 0, // 新的未读消息
        myAllMsg: 0, // 我的所有消息，包括发送和接收的
        myKnowledge: 0, // 我的知识库
        myReply: 0, // 我回复他人的消息
        mySubject: 0, // 我参与的主题数
        balanceAITokens: 0, // 我的AI代币余额
        usedAITokens: 0, // 我的AI代币消耗
        totalAITokens: 0, // 我的AI代币总数
      };
    return getTotalInfoJson(result);
  };

  // cronJobMyTotalInfo = cronJobMyTotalInfo;
  getChatHistoryByCID = getChatHistoryByCID;

  getChatListMySent = getChatListMySent;

  getChatListToMe = getChatListToMe;

  getChatListAll = getChatListAll;











  // 获取微信群统计信息
  servicesGetWxGroupStatistics = async ({
    sessionId,
    body,
  }: {
    sessionId: string;
    body: any;
  }): Promise<GResponse<WxGroupStatistics>> => {
    const { groupObjectId } = body;
    const sess = await this.isSuperOrOwnerGroup(sessionId, groupObjectId);
    if (!sess) throw new ApiError('无权限(获取群统计信息)', 401);

    const group = await itchatGroup.getWxGroupInfoByObjectId(groupObjectId);
    if (!group) return this.makeGResponseError();
    const { aiKeywords: keywords } = group;

    const result: WxGroupStatistics = {
      groupObjectId: '',
      chatCount: 0,
      chatCountKeywords: 0,
      lastActiveTime: '',
      mostFrequentChatter: [],
    };
    const refGroup: ParseRef = {
      __type: 'Pointer',
      className: 'AIItchatGroups',
      objectId: groupObjectId,
    };

    const query = new ParseDB.Query(`AIGroupNotAtMsg_${sess.agent}`);
    //---------------------------------------------------
    // 聊天记录总数
    query.equalTo('refItchatGroup', refGroup);
    result.chatCount = await query.count();
    //---------------------------------------------------
    // 最后活跃时间
    query.descending('createdAt');
    const lastChat = await query.first();
    if (lastChat) {
      result.lastActiveTime = lastChat.get('createdAt');
    }
    //---------------------------------------------------
    // 包含关键字的聊天记录数
    if (keywords?.length) {
      // 查询 content 中是否包含 keywords 多个关键词中的一个
      // const keywords = ['打卡', '体重'];
      const regex = new RegExp(keywords.join('|'), 'i'); // 'i' 代表忽略大小写
      query.matches('content', regex);
      result.chatCountKeywords = await query.count();
    }
    //---------------------------------------------------
    // 包含关键字最多的用户(取前三个)
    if (keywords?.length) {
      const query3 = new ParseDB.Query(`AIGroupNotAtMsg_${sess.agent}`);
      query3.equalTo('groupName', group.groupNickName);
      const regex = new RegExp(keywords.join('|'), 'i'); // 'i' 代表忽略大小写
      query3.matches('content', regex);

      const pipeline: any[] = [
        { $group: { _id: '$userName', count: { $sum: 1 } } }, // 按照 userName 字段分组，并统计每个 userName 出现的次数
        { $sort: { count: -1 } }, // 按 count 字段降序排列
        { $limit: 3 }, // 只返回前三个
      ];

      const results = await query3.aggregate(pipeline);
      logger.info('Top 3 most frequent users:');
      result.topKeywordUsers = results.map((item: any) => ({
        userName: item.objectId,
        count: item.count,
      }));
    }
    //---------------------------------------------------
    // 聊天最频繁的3个用户:
    const query2 = new ParseDB.Query(`AIGroupNotAtMsg_${sess.agent}`);
    // query2.equalTo('refItchatGroup', refGroup); // 不知道为什么使用这个查询不出结果,只好用名称
    query2.equalTo('groupName', group.groupNickName);
    // 使用 aggregate 管道进行聚合查询
    const pipeline: any[] = [
      { $group: { _id: '$userName', count: { $sum: 1 } } }, // 按照 userName 字段分组，并统计每个 userName 出现的次数
      { $sort: { count: -1 } }, // 按 count 字段降序排列
      { $limit: 3 }, // 只返回前���个
    ];

    const results = await query2.aggregate(pipeline);
    logger.info('Top 3 most frequent users:');
    result.mostFrequentChatter = results.map((item: any) => ({
      userName: item.objectId,
      count: item.count,
    }));
    //---------------------------------------------------
    return result?.chatCount > 0 ? this.makeGResponse(result) : this.makeGResponseError();
  };

  // 获取关注用户的消息统计汇总
  servicesGetWxUserStatistics = async ({
    sessionId,
    userObjectId,
    range, groupName, sort
  }: {
    sessionId: string;
    userObjectId: string;
    pageSize: number;
    range: [number, number]; groupName: string; sort: "groupName" | "msgCount";
  }): Promise<GResponse<WatchingUserStatistics>> => {
    const sess = await this.checkSession(sessionId);
    const isMyWatchingUser = await isMyWathingUser({
      agent: sess.agent,
      account: sess.account,
      otherObjectId: userObjectId,
    });

    if (!isMyWatchingUser) throw new ApiError('无权限(用户信息统计)', 401);

    const resp = await userStatistics.makeUserStatistics({ agent: sess.agent, userObjectId, range, groupName, sort });

    return this.makeGResponse(resp);
  };

  // 获取我自己的使用统计
  servicesGetWxUserStatisticsMyself = async ({
    sessionId,
    range,
  }: {
    sessionId: string;
    userObjectId: string;
    pageSize: number;
    range: [number, number];
  }): Promise<GResponse<WxUserStatistics>> => {
    const sess = await this.checkSession(sessionId);
    const resp = await userStatistics.makeUserStatisticsHome({
      agent: sess.agent,
      wxUserObjectId: sess.userObjectId,
      account: sess.account,
      range
    });
    return this.makeGResponse(resp);
  };


  replyChatRecord = replyChatRecord;

  updateChatRecord = updateChatRecord;

  getChatDetail = getChatDetail;

  delChat = delChat;

  getChatHistory = getChatHistory;


}

export default new ChatServices();
export {
  getChatHistoryLocal, getRecordJson, updateChatRecordForAIContent
};

