/**
 * 微信服务模块
 * 
 * 提供微信基础功能服务:
 * - 微信登录授权
 * - 获取用户信息
 * - 获取用户手机号
 * - 微信支付签名生成
 * - 用户登出管理
 * 
 * <AUTHOR> <<EMAIL>>
 * @date 2024
 */
import axios from 'axios';
import fs from 'fs';
import WxPay from 'wechatpay-node-v3'; // 支持使用require

import { createDecipheriv } from 'crypto';
import { PhoneInfo } from '../../types/prisma';
import { getLogger } from '../../utils/logger';
import { BaseServices } from '../baseServices';
import session from '../sessionManager';
const { v4: uuidv4 } = require('uuid');
const logger = getLogger('wechat');
const getAccessToken = async (code: string) => {
  // https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code
  const url = `https://api.weixin.qq.com/sns/oauth2/access_token?appid=${process.env.WX_APPID}&secret=${process.env.WX_SECRET}&code=${code}&grant_type=authorization_code`;
  logger.info('getAccessToken url:', url);
  const { data } = await axios.get(url);
  return data;
};
const getSessionByMiniAppCode = async (jsCode: string) => {
  // https://api.weixin.qq.com/sns/jscode2session?appid=APPID&secret=SECRET&js_code=JSCODE&grant_type=authorization_code
  const url = `https://api.weixin.qq.com/sns/jscode2session?appid=${process.env.WX_MINI_APPID}&secret=${process.env.WX_MINI_SECRET}&js_code=${jsCode}&grant_type=authorization_code`;
  logger.info('getSessionByMiniAppCode url:', url);
  const { data } = await axios.get(url);
  logger.info('getSessionByMiniAppCode data:', data);
  return data;
};
const getUserinfoByAccessToken = async (accessToken: string, openid: string) => {
  // https://api.weixin.qq.com/sns/userinfo?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN
  const url = `https://api.weixin.qq.com/sns/userinfo?access_token=${accessToken}&openid=${openid}&lang=zh_CN`;

  logger.info('getUserinfoByAccessToken url:', url);
  const { data } = await axios.get(url);
  return data;
};
const getWxOpenId = async (reqQuery: any) => {
  const { code } = reqQuery;
  return getAccessToken(code);
};

const getUserInfo = async (body: any) => {
  const { account, code } = body?.payload || body;
  const { access_token: accessToken, openid } = await getAccessToken(code);
  const data = await getUserinfoByAccessToken(accessToken, openid);

  logger.info('getUserInfo:', data);
  if (!data.openid) {
    logger.error('getUserInfo:', data);
    return {
      code: 0,
      errmsg: '获取用户信息失败',
    };
  }
  // https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx298297660860d110&redirect_uri=http%3A%2F%2F192.168.15.223%3A8080&response_type=code&scope=snsapi_userinfo&state=1&connect_redirect=1#wechat_redirect

  const loginInfo = await session.login(account, {
    account,
    userId: data.openid,
    accessToken,
    platformName: 'WECHAT',
    platformUserId: data.openid,
    name: data.nickname,
    agent: body.agent,
  });

  return {
    code: 1,
    sessionId: loginInfo.sessionId,
    expiresAt: loginInfo.expiresAt,
    accessToken,
    userId: data.unionid || data.openid,
    wxUserInfo: {
      openid: data.openid,
      nickname: data.nickname,
      headimgurl: data.headimgurl,
      sex: data.sex,
      country: data.country,
    },
  };
};
const getH5PayUrl = async (body: any) => {
  const { message, openid } = body;
  // const appid = 'wx454391c23c439801';
  const appid = 'wx298297660860d110';

  // const util = require("util");
  // const readFile = util.promisify(fs.readFile);
  logger.info('------------------------', __dirname);

  const privateFile = `${__dirname}/apiclient_key.pem`;
  const pubkeyFile = `${__dirname}/apiclient_cert.pem`;
  logger.info('privateFiel:', privateFile);
  //---------------------------------------
  const pay = new WxPay({
    appid,
    mchid: '1516869391',
    publicKey: fs.readFileSync(pubkeyFile), // 公钥
    privateKey: fs.readFileSync(privateFile), // 秘钥
  });
  //----------------------------------------
  // # 这里以h5支付为例
  try {
    // # 参��介绍请看h5支付文档 https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_3_1.shtml
    const params = {
      appid,
      mchid: '1516869391',
      description: '测试',
      out_trade_no: `1217752501201407${parseInt(`${+new Date() / 1000}`, 10).toString()}`,
      notify_url: 'https://wx.qq.com',
      amount: {
        total: 1,
        currency: 'CNY',
      },
      scene_info: {
        payer_client_ip: '127.0.0.1',
        h5_info: {
          type: 'Wap',
        },
      },
    };
    const nonce_str = Math.random().toString(36).substr(2, 15); // 随机字符串
    const timestamp = parseInt(`${+new Date() / 1000}`, 10).toString(); // 时间戳 秒
    const url = '/v3/pay/transactions/h5';

    // # 获取签名
    const signature = pay.getSignature('POST', nonce_str, timestamp, url, params);
    // # 如果是get 请求 则不需要params 参数拼接在url上 例如 /v3/pay/transactions/id/12177525012014?mchid=1230000109
    // # 获取头部authorization 参数
    const authorization = pay.getAuthorization(nonce_str, timestamp, signature);

    // H5 二维码付款,已经成功
    const result = await axios.post('https://api.mch.weixin.qq.com/v3/pay/transactions/h5', params, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: authorization,
      },
    });
    logger.info('wx pay h5_url:', result?.data);
    return { h5_url: result?.data?.h5_url };
  } catch (error) {
    logger.error('getPaySignature error:', error);
    return { error };
  }
};

const getPaySignature = async (body: any) => {
  const { message, openId } = body;
  // const appid = 'wx298297660860d110';
  const appid = 'wx454391c23c439801';
  // const appid = 'wx2e013ac2b601ceb1';

  // const util = require("util");
  // const readFile = util.promisify(fs.readFile);
  logger.info('------------------------', __dirname);

  const privateFile = `${__dirname}/apiclient_key.pem`;
  const pubkeyFile = `${__dirname}/apiclient_cert.pem`;
  logger.info('privateFiel:', privateFile);
  //---------------------------------------
  const pay = new WxPay({
    appid,
    mchid: '1516869391',
    publicKey: fs.readFileSync(pubkeyFile), // 公钥
    privateKey: fs.readFileSync(privateFile), // 秘钥
  });
  //----------------------------------------
  // jsapi支付
  try {
    const params2 = {
      appid,
      mchid: '1516869391',
      description: '测试',
      out_trade_no: `1217752501201408${parseInt(`${+new Date() / 1000}`, 10).toString()}`,
      notify_url: 'https://wx.qq.com',
      goods_tag: '2222string',
      amount: {
        total: 1, // 分
        currency: 'CNY',
      },
      payer: {
        openid: openId,
      },
    };
    const pack = await pay.transactions_jsapi(params2);
    if (pack?.status !== 200) {
      logger.error('pay.transactions_jsapi pack', pack);
      throw new Error(`Error signing message: ${pack?.status} , ${pack?.message}`);
    }
    logger.info('pay.transactions_jsapi return:', pack);
    return pack;
  } catch (error: any) {
    logger.error('error', error);
    throw new Error(`支付发生意外,请稍后再试.`);
  }
};

const getPhoneFromCodeMiniApp = async (code: string): Promise<PhoneInfo> => {
  try {
    const tokenUrl = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${process.env.WX_MINI_APPID}&secret=${process.env.WX_MINI_SECRET}`;
    logger.info('servicesGetUserPhoneNumber url:', tokenUrl);
    const token: any = await axios.get(tokenUrl);

    // 通过token和code来获取用户手机号
    const url = `https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=${token?.data?.access_token}`;
    logger.info('getuserphonenumber ', url);

    const result: any = await axios.post(url, { code });

    logger.info('getuserphonenumber:', result.data);

    if (result?.data && result.status === 200 && result.data.errcode === 0) {
      // 通过code获取到的手机号信息
      const timestamp = result.data.phone_info.watermark.timestamp;
      const now = Math.floor(Date.now() / 1000);
      // 允许5分钟的时间误差范围
      const TIME_DIFF_ALLOWED = 300; // 5分钟
      if (Math.abs(timestamp - now) > TIME_DIFF_ALLOWED) {
        logger.error('getuserphonenumber: phone_info timestamp invalid', {
          timestamp,
          now,
          diff: Math.abs(timestamp - now)
        });
        throw new Error('获取手机号码失败: phone_info timestamp invalid');
      }
      if (result.data.phone_info.watermark.appid !== process.env.WX_MINI_APPID) {
        logger.error('getuserphonenumber: phone_info appid invalid', {
          appid: result.data.phone_info.watermark.appid,
          wxMiniAppId: process.env.WX_MINI_APPID
        });
        throw new Error('获取手机号码失败: phone_info appid invalid');
      }

      logger.info('getuserphonenumber:', result.data);
      const phoneInfo = result.data.phone_info;
      return {
        ...phoneInfo,
        phoneVerified: true,
      };
    }
  } catch (error: any) {
    logger.error('getPhoneFromCodeMiniApp error:', error);
    throw new Error(`获取手机号码失败:${error?.message}`);
  }
  return undefined;
};

class WechatServices extends BaseServices {
  constructor() {
    super();
  }
  servicesGetUserProfile = async (body: any) => {
    const { encryptedData, iv, code } = body.res;
    // const { access_token: accessToken, openid } = await getAccessTokenByJscode(code);
    const session = await getSessionByMiniAppCode(code);
    try {
      // Base64 解码
      const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');
      const ivBuffer = Buffer.from(iv, 'base64');
      const sessionKeyBuffer = Buffer.from(session.session_key, 'base64');

      // 创建 AES 解密器
      const decipher = createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
      decipher.setAutoPadding(true);

      // 解密
      let decrypted = decipher.update(encryptedDataBuffer, undefined as any, 'utf8');
      decrypted += decipher.final('utf8');

      // 解析 JSON
      const result = JSON.parse(decrypted);

      return this.makeGResponse(result)
    } catch (e) {
      return { userInfo: null, error: String(e) };
    }
  }
  servicesGetUserPhoneNumber = async (body: any) => {
    const { code } = body.payload;
    const phoneInfo = await getPhoneFromCodeMiniApp(code);
    if (!phoneInfo) {
      throw new Error(`获取手机号码失败`);
    }

    return this.makeGResponse(phoneInfo);
  };

}
function logout(userId: any) {
  return session.logout(userId);
}

const wechatServices = {
  getWxOpenId,
  getUserInfo,
  getPaySignature,
  logout,
};
export { getSessionByMiniAppCode, wechatServices };
export default new WechatServices();
