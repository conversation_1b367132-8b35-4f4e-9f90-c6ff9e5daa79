
import { v4 as uuid } from 'uuid';
import { IKnowReferer } from '../controllers/controller';
import { PlatformName } from '../types/platformReward';
import { User } from '../types/prisma';
import { EthAddress } from '../types/types';
import ApiError from '../utils/api_error';

import { getLogger } from '../utils/logger';
import baseServices from './baseServices';
import { ParseDB, ParseObject } from './services';
import { getUserBySSO } from './users/userFunction';
import { getWxUserByObjectId } from './wechat/cowUserFunctions';
const logger = getLogger('sessMan');
const DBNAME: string = 'UsersSession';
export type SessionInfo = {
  agent: EthAddress;
  userId: string; // 用户ID
  account: EthAddress;
  name: string;
  userObjectId?: string;
  wxid?: string // 当来自wcferry时,用户的唯一id
  alias?: string; // 真实微信号,100%唯一

  platformName: PlatformName;
  platformUserId: string; // 平台ID
  sessionId?: string; // 用户 session id
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: any;

  id?: string;
  permissions?: Array<string>, // 权限
  roles?: Array<string>, // 角色

  createdAt?: Date;
  updatedAt?: Date;
  referer?: IKnowReferer;
  tags?: string[];
};
// 过期天数,默认为12
const EXPIRE_DAYS = 12;

class SessionManager {

  parse: any;
  //  const query = new ParseDB.Query(ChatRecordDB);

  constructor() {
    this.parse = ParseDB.Object.extend(DBNAME); // 初始化 Parse
  }

  getJson(obj: ParseObject): SessionInfo {
    if (!obj || !obj.id) return null;
    return {
      id: obj.id,

      createdAt: obj.createdAt,
      updatedAt: obj.updatedAt,

      agent: obj.get('agent'),
      account: obj.get('account'),
      userObjectId: obj.get('userObjectId'),
      wxid: obj.get('wxid'),
      sessionId: obj.get('sessionId'),
      accessToken: obj.get('accessToken'),
      expiresAt: obj.get('expiresAt'),

      userId: obj.get('userId'),
      name: obj.get('name'),
      platformName: obj.get('platformName'),
      platformUserId: obj.get('platformUserId'),

      permissions: obj.get('permissions') || [],
      roles: obj.get('roles') || [],
    };
  }

  async makeSession(sess: SessionInfo): Promise<SessionInfo> {
    const sessionId = uuid();
    const expiresAt = new Date(Date.now() + 1000 * 3600 * 24 * EXPIRE_DAYS); // 12天后过期

    const Session = ParseDB.Object.extend(DBNAME);
    const session = new Session();

    // 准确控制输入项
    session.set({
      sessionId,
      expiresAt,

      agent: sess.agent,

      account: sess.account,
      userObjectId: sess.userObjectId,
      wxid: sess.wxid,
      userId: sess.userId,
      name: sess.name,
      platformName: sess.platformName,
      platformUserId: sess.platformUserId,
      accessToken: sess.accessToken,
      refreshToken: sess.refreshToken,
      permissions: sess.permissions,
      roles: sess.roles,
      referer: sess.referer,
      tags: sess.tags
    });
    const newSession = await session.save(null, { useMasterKey: true });

    return this.getJson(newSession);
  }

  // 用户登录,返回 session ID
  async login(account: EthAddress, data: SessionInfo): Promise<SessionInfo> {
    // account直接由客户端传递时会出现错误
    let account2 = account;
    // role 不允许客户端传入
    data.roles = []
    data.permissions = []

    // 直接使用 ParseDB 查询用户信息
    const userRecord = await getUserBySSO(data.userId);
    if (userRecord) {
      const userName = userRecord.get('name');
      if (userName === data.name) account2 = userRecord.get('account');

      const roles = userRecord.get('roles') || [];
      if (data.tags?.includes("R_FROM_PC")) { roles.push("R_FROM_PC") }
      if (data.tags?.includes("R_FROM_MOBILE")) { roles.push("R_FROM_MOBILE") }
      if (data.tags?.includes("R_FROM_WECHAT")) { roles.push("R_FROM_WECHAT") }

      //  如果Users已经有设置userObjectid,那么跳过
      //  如果没有userObjectId,则从传入的数据库中获取
      let userObjectId = userRecord.get('userObjectId');
      if (!userObjectId) {
        const wxUser = await getWxUserByObjectId(data.userObjectId);
        if (wxUser) {
          userObjectId = wxUser.objectId;
          userRecord.set('userObjectId', userObjectId);
          await userRecord.save();
        }
      }

      return this.makeSession({
        ...data,
        userObjectId,
        account: account2,
        permissions: userRecord.get('permissions'),
        roles
      });
    }

    // 如果没有找到用户，直接使用传入的信息
    return this.makeSession({ ...data, account: account2 });
  }

  // 用户登录,返回 session ID
  async makeSessionByUser(user: User, agent: EthAddress, referer: IKnowReferer): Promise<SessionInfo> {
    const plat = user.platformUsers?.[0]
    const body = {
      agent,
      account: user.account,
      userObjectId: user.objectId,
      wxid: user.wxid,
      userId: user.ssoID,
      name: user.name,
      platformName: plat?.platformName,
      platformUserId: plat?.platformUserId,
      permissions: user.permissions,
      roles: user.roles,
      referer
    }

    return this.makeSession(body)
  }

  // 用户退出,删除 session
  async logout(body: SessionInfo) {
    const { sessionId } = body;
    const query = new ParseDB.Query(DBNAME);
    query.equalTo('sessionId', String(sessionId));

    const session = await query.first();
    if (session) {
      session.destroy();
      return true;
    }
    throw new Error('session not found');
  }

  async validateSession(sessionId: string): Promise<SessionInfo> {
    if (!sessionId) {
      logger.error('sessionId is empty');
      return null;
    }

    const query = new ParseDB.Query(DBNAME);
    query.equalTo('sessionId', sessionId);

    const session = await query.first();
    if (!session) {
      logger.error('session is not exist');
      return null;
    }
    const expiresAt = session?.get('expiresAt');
    if (expiresAt && expiresAt.getTime() >= Date.now()) {
      // await this.refreshSession(sessionId); // 使用了就刷新一下
      return this.getJson(session);
    }
    logger.error('session is not valid');
    session.destroy();
    return null;
  }



  async refreshSession(sessionId: string) {
    const query = new ParseDB.Query(DBNAME);
    query.equalTo('sessionId', sessionId);
    const session = await query.first();
    if (session) {
      session.set('expiresAt', new Date(Date.now() + 1000 * 3600 * 24 * EXPIRE_DAYS)); // 12天后过期
      session.save();
    }
  }

  getSessionList = async ({ pageNum, pageSize }: { pageNum: number, pageSize: number }) => {
    let start = pageNum
    if (start < 1) start = 1;

    const query = new ParseDB.Query(DBNAME);
    query.descending('createdAt');
    const total = await query.count();

    const skipStep = (pageNum - 1) * pageSize
    if (total > skipStep && skipStep)
      query.skip(skipStep);
    query.limit(pageSize);
    logger.info('session getSessionList:', pageNum, pageSize);

    const results = await query.find();
    if (results?.length) {
      const records = await Promise.all(results.map(async item => this.getJson(item)))
      return { code: 200, data: { current: start, size: pageSize, total, records }, error: 0 }
    }
    return { code: 200, data: { current: start, size: pageSize, total, records: [] }, error: 0 }
  }

  serviceGetSessionList = async ({ sessionId, pageNum, pageSize }: { sessionId: string, pageNum: number, pageSize: number }) => {
    if (! await baseServices.isSuperMan(sessionId)) throw new ApiError('没有权限', 60002);

    return this.getSessionList({ pageNum, pageSize });
  }

  serviceDeleteSession = async ({ authorization, sessionId }: { authorization: string, sessionId: string }): Promise<void> => {
    if (! await baseServices.isSuperMan(authorization)) throw new ApiError('没有权限', 60002);

    const query = new ParseDB.Query(DBNAME);
    query.equalTo('sessionId', sessionId);
    const result = await query.first();
    if (result) {
      await result.destroy();
      logger.warn(`session ${sessionId} is deleted`);
    }
  }

  // 定期清理已经过期的session
  cronClearSession() {
    const query = new ParseDB.Query(DBNAME);

    // 12天后过期
    query.lessThanOrEqualTo('expiresAt', new Date(Date.now()));
    query.find()?.then(results => {
      logger.info('session cronClearSession:', results?.length);
      results?.forEach(session => {
        session.destroy();
        logger.info('session expired:', session.id);
      });
    });
  }
}

export default new SessionManager();
