import { PlatformBody } from "../services/platformServices";
import { PlatformName } from "../types/platformReward";
import { EthAddress } from "../types/types"
import logger from "../utils/logger";
import { ParseObject, ParseDB } from "./database";

const TABLE_NAME = "UsersPlatformUser";
// export type PlatformBody = {
//   id?: string;

//   userId: number;
//   username: string;
//   displayName: string;
//   avatar: string;

//   account: EthAddress;
//   platformName: PlatformName;
//   platformUserId: string;
//   platformUserData: any;
// };
const getJson = (result: ParseObject): PlatformBody => {
  if (!result || !result.id) return null;
  return {
    id: result.id,

    // userId: result.get("userId"),
    account: result.get("account"),

    platformType: result.get("platformType"),
    platformName: result.get("platformName"),
    platformUserId: result.get("platformUserId"),
    platformUserData: result.get("platformUserData"),

    username: result.get("username"),
    displayName: result.get("displayName"),
    avatar: result.get("avatar"),
  };
};
const getRecord = async (id: string): Promise<ParseObject> => {
  const query = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));
  return query.get(id);
};

const getRecordByAccount = async (account: EthAddress): Promise<ParseObject> => {
  try {
    const query = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));

    query.equalTo("account", account);
    query.ascending("createdAt");

    return query.first();
  } catch (e: any) {
    logger.error("getRecordByAccount error:", e?.message);
    return null;
  }
};
// 获取用户对应的platformusers
const getPlatsByAccount = async (
  account: EthAddress,
): Promise<Array<PlatformBody>> => {
  const query = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));

  query.equalTo("account", account);

  const results = await query.find();

  return results?.map((item: any) => getJson(item));
};

// 通过account，platformName
const getPlatformInfoByAccount = async (
  account: EthAddress,
  platformName: PlatformName,
): Promise<PlatformBody> => {
  const query = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));
  query.equalTo("account", account);
  query.equalTo("platformName", platformName);
  const result = await query.first();
  return getJson(result);
};

const getAll = async (): Promise<PlatformBody[]> => {
  const query = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));
  query.limit(1000);
  query.descending("updatedAt");
  const result = await query.find();
  return result.map((item: any) => getJson(item));
};

// 用户的platform user 信息使用单独的表存储，关联到 Users表的 platformUserId(ObjectId)中
const insertOrUpdate = async (data: PlatformBody): Promise<ParseObject> => {

  logger.info("==>usersPlatformUserTB insertOrUpdate:");

  const {
    // userId,
    account,
    platformName,
    platformUserId,
    platformUserData: userinfo,
  } = data;

  if (!account || !platformUserId || !userinfo || !platformName) {
    logger.error("==>usersPlatformUserTB insertOrUpdate error:", data);
    throw new Error("insertOrUpdate params error!");
  }

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo("account", account);
  query.equalTo("platformName", platformName);
  query.equalTo("platformUserId", platformUserId);

  const record = await query.first();
  if (record?.id) {
    record.set(data);
    return record.save();
  }

  const UsersPlatformUser = ParseDB.Object.extend(TABLE_NAME);
  const table = new UsersPlatformUser();

  table.set(data);

  return table.save();
};
const removeInfo = async (account: EthAddress, platformName: PlatformName) => {
  const query = new ParseDB.Query(ParseDB.Object.extend(TABLE_NAME));

  query.equalTo("account", account);
  query.equalTo("platformName", platformName);

  const result = await query.first();
  return result?.destroy();
};
const usersPlatformUserTB = {
  getRecord,
  getJson,
  getAll,
  insertOrUpdate,
  getPlatsByAccount,
  getPlatformInfoByAccount,
  removeInfo,
};

export default usersPlatformUserTB;
