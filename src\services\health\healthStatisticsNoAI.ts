// 这里所有算法都没有使用AI

import logger from "../../utils/logger";
import { updateChatRecordForAIContent } from "../chatlist/chatlistServices";

import { ParseDB } from "../../database/database";
import { IMealType } from "../../types/health";
import { EthAddress } from '../../types/types';
import { calculateHash } from '../../utils/utils';
import { INotAtMsg } from "../chatlist/groupNotAtMsgTypes";
import gpt from '../gpt/gptServerApi';
import wxGroupServices from '../wechat/cowGroupClass';
import wxUserServices from '../wechat/cowUserClass'
import { getWxUserByAccount, getWxUserByObjectId } from '../wechat/cowUserFunctions';
import { WxGroupInfo, WxUserInfo } from "../wechat/types";
import caloriesServices from "./calories/healthCaloriesServices";
import { getFoodsInContent } from "./calories/healthFood";
import { AIRespCaloriesJson } from "./calories/types";
import { isHealthGroup3Month, isHealthGroup6Month, isHealthGroupMonth136 } from "./healthPatientsWxGroup";
import {
    extractAerobicMinutes,
    extractAnaerobicMinutes,
    extractFlexibilityMinutes,
    extractSleepMinutes,
    extractWater,
    extractWeight
} from './healthRegexUtils';
import { HealthItem, HealthRecordStatistics, IAnalyzedFood, NotAtMsgTasks, NotAtMsgTasksAiResponse } from "./types";
import weightLossServices from "./weightLoss/healthWeightLossService";
import userCardServices from "./healthUserCardServices";
import { getCalorieCard, refreshCaloriesCardByGPTResp, refreshUserCaloriesCard } from "./calories/healthCaloriesPlan";
import { getBeijingTime } from "../../utils/date_tools";
import { getCaloriesRecordsToday, makeCaloriesRecordText } from "./calories/healthCaloriesFunctions";
import { addOrUpdateItchatGroup, makeWxGroupRef } from '../wechat/cowGroupFunction';
import userServices from '../users/userServices';
import { createUser, getUserInfo, getUserJson } from '../users/userFunction';
// 从文本中提取日期信息并转换为date
// function extractDate(text: string): Date
const { v4: uuidv4 } = require('uuid');

// 计算健康打卡的统计数据,计算记录中最晚和最早记录之间的体重差
const calcWeightStatistics = (list: INotAtMsg[], keywords: string[]): Promise<
    { weightStart: number, weightEnd: number, unit: string, }
> => {
    let weightEnd = 0;
    let weightStart = Infinity; // 初始化为无穷大，以便找到最早的体重记录

    // 找到最早的体重记录
    for (let i = 0; i < list.length; i += 1) {
        const { content } = list[i];
        // const content = testData[i]
        const weight = extractWeight(content.toLocaleLowerCase())
        if (weight) {
            weightEnd = Math.min(weightStart, weight);
            break;
        }
    }

    // 由于 results 是倒序的，我们要找到最后一个有效的体重记录
    let lastValidWeight = 0;
    for (let i = list.length - 1; i >= 0; i -= 1) {
        const { content } = list[i];
        // const content = testData[i > 3 ? 3 : i]

        const weight = extractWeight(content.toLocaleLowerCase())
        if (weight) {
            lastValidWeight = weight;
            break;
        }
    }
    weightStart = lastValidWeight;

    // 计算体重差别
    const weightDiff = (weightStart > weightEnd) ? (weightStart - weightEnd) : 0;
    logger.info(`weightDiff: ${weightDiff}`); // 使用 console.log 作为示例
    return Promise.resolve({ weightStart, weightEnd, unit: 'kg' });
};




const getFromJson = (json: HealthItem[]) => {
    const water = json.find(item => item.name.includes('喝水') || item.name.includes('喝水量'))
    const sleep = json.find(item => item.name.includes('睡眠') || item.name.includes('睡眠时间'))
    const aerobic = json.find(item => item.name.includes('有氧') || item.name.includes('有氧运动'))
    const anaerobic = json.find(item => item.name.includes('无氧') || item.name.includes('无氧运动'))
    const flexibility = json.find(item => item.name.includes('柔韧性') || item.name.includes('柔韧运动'))
    return {
        water, sleep, aerobic, anaerobic, flexibility
    }
}
const getFromString = (content: string) => {
    const water = extractWater(content)
    const sleep = extractSleepMinutes(content)
    const aerobic = extractAerobicMinutes(content)
    const anaerobic = extractAnaerobicMinutes(content)
    const flexibility = extractFlexibilityMinutes(content)
    return {
        water, sleep, aerobic, anaerobic, flexibility
    }
}

// 计算健康打卡的统计数据(平均时间:平均喝水时间,平均睡眠时间,平均有氧运动时间 ,平均无氧运动时间,平均柔韧性时间)
const calcAvgTotalStatistics = (list: INotAtMsg[], keywords: string[]): Promise<{
    water: { totalWaterIntake: number, countWater: number, unit: string },
    sleep: { totalSleepTime: number, countSleep: number, unit: string },
    aerobic: { totalAerobicExercise: number, countAerobic: number, unit: string },
    anaerobic: { totalAnaerobicExercise: number, countAnaerobic: number, unit: string },
    flexibility: { totalFlexibilityExercise: number, countFlexibility: number, unit: string }
}> => {
    let totalWaterIntake: number = 0;
    let totalSleepTime: number = 0;
    let totalAerobicExercise: number = 0;
    let totalAnaerobicExercise: number = 0;
    let totalFlexibilityExercise: number = 0;
    let countWater: number = 0;
    let countSleep: number = 0;
    let countAerobic: number = 0;
    let countAnaerobic: number = 0;
    let countFlexibility: number = 0;

    list.forEach((item, index) => {
        const { aiGenContentJson } = item
        if (aiGenContentJson) {
            const { water, sleep, aerobic, anaerobic, flexibility } = getFromJson(aiGenContentJson)
            if (water && Number(water.value)) {
                totalWaterIntake += Number(water.value)
                countWater += 1
            }
            if (sleep && Number(sleep.value)) {
                totalSleepTime += Number(sleep.value)
                countSleep += 1
            }
            if (aerobic && Number(aerobic.value)) {
                totalAerobicExercise += Number(aerobic.value)
                countAerobic += 1
            }
            if (anaerobic && Number(anaerobic.value)) {
                totalAnaerobicExercise += Number(anaerobic.value)
                countAnaerobic += 1
            }
            if (flexibility && Number(flexibility.value)) {
                totalFlexibilityExercise += Number(flexibility.value)
                countFlexibility += 1
            }
        } else if (item.aiGenContent) {
            const json = JSON.parse(item.aiGenContent)
            // 如果 json 不是数组，将其转换为数组
            const itemArray: HealthItem[] = Array.isArray(json) ? json : Object.values(json);

            const { water, sleep, aerobic, anaerobic, flexibility } = getFromJson(itemArray)
            if (water && Number(water.value)) {
                totalWaterIntake += Number(water.value)
                countWater += 1
            }
            if (sleep && Number(sleep.value)) {
                totalSleepTime += Number(sleep.value)
                countSleep += 1
            }
            if (aerobic && Number(aerobic.value)) {
                totalAerobicExercise += Number(aerobic.value)
                countAerobic += 1
            }
            if (anaerobic && Number(anaerobic.value)) {
                totalAnaerobicExercise += Number(anaerobic.value)
                countAnaerobic += 1
            }
            if (flexibility && Number(flexibility.value)) {
                totalFlexibilityExercise += Number(flexibility.value)
                countFlexibility += 1
            }
        } else {
            const content = item.content.toLowerCase();
            // 饮水量
            const waterAmount = extractWater(content)
            if (waterAmount) {
                totalWaterIntake += Number(waterAmount)
                countWater += 1
            }
            // 睡眠时间
            const sleepTime = extractSleepMinutes(content)
            if (sleepTime && Number(sleepTime)) {
                totalSleepTime += Number(sleepTime)
                countSleep += 1
            }

            // 有氧运动时间
            const aerobicTime = extractAerobicMinutes(content)
            if (aerobicTime && Number(aerobicTime)) {
                totalAerobicExercise += Number(aerobicTime)
                countAerobic += 1
            }

            // 无氧运动时间
            const anaerobicTime = extractAnaerobicMinutes(content)
            if (anaerobicTime && Number(anaerobicTime)) {
                totalAnaerobicExercise += Number(anaerobicTime)
                countAnaerobic += 1
            }

            // 柔韧性训练时间（以平板支撑为例）
            const flexibilityTime = extractFlexibilityMinutes(content)
            if (flexibilityTime && Number(flexibilityTime)) {
                totalFlexibilityExercise += Number(flexibilityTime)
                countFlexibility += 1
            }
        }
    });

    return Promise.resolve({
        water: { totalWaterIntake, countWater, unit: 'ml' },
        sleep: { totalSleepTime, countSleep, unit: 'h' },
        aerobic: { totalAerobicExercise, countAerobic, unit: 'min' },
        anaerobic: { totalAnaerobicExercise, countAnaerobic, unit: 'min' },
        flexibility: { totalFlexibilityExercise, countFlexibility, unit: 'min' }
    });
};

// 计算关键词统计数据
const calcKeywordsStatistics = async (list: INotAtMsg[], keywords: string[]): Promise<HealthRecordStatistics> => {
    const weight = await calcWeightStatistics(list, keywords);


    const { userName, refItchatUser, groupName, refItchatGroup } = list[0]
    const { water, sleep, aerobic, anaerobic, flexibility } = await calcAvgTotalStatistics(list, keywords);
    return Promise.resolve({
        userObjectId: refItchatUser.objectId,
        userName,
        groupObjectId: refItchatGroup.objectId,
        groupName,
        weight,
        water,
        sleep,
        aerobic,
        anaerobic,
        flexibility,
        totalRecords: list?.length
    });
};

interface HealthMetric {
    name: string;
    value: number;
    unit: string;
}

interface WeightLossRegexResult {
    json?: HealthMetric[];
    jsonStr?: string;
    response?: {
        model: string;
        weight: number;
        water: number;
        sleep: number;
        aerobic: number;
        anaerobic: number;
        flexibility: number;
        aiTokens: number;
        completionTokens: number;
        promptTokens: number;
    };
}


/**
 * 从群名称,配置文件,日期判断是否为健康打卡群组
 * @param agent
 * @param groupName
 * @returns
 */
const isHealthGroup = (agent: EthAddress, groupName: string, createdAt: Date) => {

    if (isHealthGroupMonth136(agent, groupName, createdAt)) return true;

    if (isHealthGroup3Month(agent, groupName, createdAt)) return true;

    if (isHealthGroup6Month(agent, groupName, createdAt)) return true;

    const healthGroupList = process.env.HEALTH_GROUP_LIST;
    if (healthGroupList) {
        const list: string[] = JSON.parse(healthGroupList.replace(/'/g, '"'));
        if (list.includes('ALL_GROUP')) return true;
        return list.includes(groupName);
    }

    return false;
};
// 判断是否需要立即答复打卡记录情况的群组
const isNeedReplyGroup = (groupName: string) => {
    const groupList = process.env.HEALTH_GROUP_NEED_REPLY_LIST;
    if (groupList) {
        const list: string[] = JSON.parse(groupList.replace(/'/g, '"'));
        if (list.includes('ALL_GROUP')) return true;
        return list.includes(groupName);
    }
    if (groupName.startsWith("wechatmp"))
        return true
    return false;
};

/**
 * 移动无效的记录
 * @param tableName 数据表名称
 * @param objectId 记录ID
 */
const moveInvalidRecords = async (tableName: string, objectId: string) => {
    const query = new ParseDB.Query(tableName);
    query.equalTo('objectId', objectId);
    const record = await query.first();
    if (record) {
        logger.warn(`移动无效的记录: ${tableName}, ${objectId}`);
        const newTableName = `invalid_${tableName}`;
        const newRecord = new ParseDB.Object(newTableName);
        // 复制所有字段
        Object.keys(record.attributes).forEach(key => {
            newRecord.set(key, record.get(key));
        });
        await newRecord.save();
        await record.destroy();
    }
}

// 📢DAY4
// ❤打卡日期：2025-03-06
// 🌈初始体重：75.2kg
// 🌈今日体重：73.4kg
// 🌈对比前日：无变化
// 🌈累计减重：-1.8kg
// 🍚早餐:9:00一个鸡蛋、一片全麦面包、一杯牛奶咖啡
// 🍚中餐:半根玉米、一片全麦面包
// 🍓加餐:一个梨、一杯酸奶
// 🍚晚餐：17:00香椿炒鸡蛋+香菇娃娃菜、50g米饭
// 💦喝水：2000ml
// 排便：1次
// 🏃‍♀️运动：散步40分钟+八段锦15分钟
// 🦶泡脚：20min
// 💤睡觉：24:00
/**
 * 判断是否为减重运动打卡
 * 当包含关键字中的2个以上时,返回true
 * keywords 会自动加上系统配置的减重关键字
 * @param content
 * @returns
 */
const isWeightLossCheckIn = (content: string, doneTasks: string[] = [], keywords: string[] = [], minMatchCount: number = 2) => {
    // 已经完成一些 task,直接返回false
    const contains = ['EXTRACT_WEIGHT_LOSS_ERR', 'IS_DUPLICATE'].some(item => doneTasks.includes(item));
    if (contains) return false;
    // 加上系统配置的减重打卡关键字
    const keywordsAll = keywords.concat(process.env.HEALTH_CHECKIN_KEYWORDS?.split(',') || [])
    // 计算匹配个数
    const count = keywordsAll.filter(keyword => content.includes(keyword)).length
    return count >= minMatchCount
}

const extraMealType = (content: string) => {
    const mealTypeMap: { [key: string]: IMealType } = {
        '早餐': '早餐',
        '午餐': '午餐',
        '晚餐': '晚餐',
        '早饭': '早餐',
        '午饭': '午餐',
        '晚饭': '晚餐',

        "上午加餐": "早餐加餐",
        "早餐加餐": "早餐加餐",

        "午餐加餐": "午餐加餐",
        "下午加餐": "午餐加餐",

        "晚餐加餐": "晚餐加餐",
        "晚上加餐": "晚餐加餐",

        "早": "早餐",
        "中": "午餐",
        "午": "午餐",
        "晚": "晚餐",

        "中餐加餐": "午餐加餐",
    };
    const mealType = Object.keys(mealTypeMap).find(keyword => content.includes(keyword));
    return mealType ? mealTypeMap[mealType] : "其他";
}
/**
 * 判断是否为餐食卡路里摄入打卡
 */
const isFoodCalorieCheckIn = async (content: string, doneTasks: string[], agent: EthAddress)
    : Promise<IAnalyzedFood> => {
    // 已经完成一些 task,直接返回false
    const contains = ['EXTRACT_CALORIES_ERR', 'EXTRACT_CALORIES_SUCESS', 'IS_CALORIES_DUPLICATE']
        .some(item => doneTasks?.includes(item));
    if (contains) {
        logger.warn(`检测到餐食卡路里打卡: 已经完成任务 [${doneTasks?.length}]个,直接返回false`);
        return undefined;
    }
    logger.info(`已完成任务:${doneTasks?.length}个,${doneTasks}`);
    // 确定的关键字,只要包含,直接返回true
    const foodCaloriesKeywords = ['早餐打卡', '午餐打卡', '晚餐打卡', '早饭打卡', "加餐打卡",
        '午饭打卡', '晚饭打卡', '夜宵打卡', "今日餐食", "上午加餐", "中午加餐", "下午加餐", "晚上加餐",
    ]
    const keys = foodCaloriesKeywords.filter(keyword => content.includes(keyword))
    if (keys?.length && content.length > 8) {
        logger.info(`检测到餐食卡路里打卡2: ${keys?.length} 个关键字`);
        const result = (await getFoodsInContent(content, agent))
        return {
            mealType: extraMealType(content),
            foods: result?.foods
        }
    }

    // 加上系统配置的减重打卡关键字
    const keywordsAll = process.env.HEALTH_FOOD_CALORIE_CHECKIN_KEYWORDS?.split(',') ||
        ['早餐', '午餐', '晚餐', '早饭', '午饭',
            '晚饭', '夜宵', "下午加餐", "晚上加餐"]

    const countPrefix = keywordsAll.filter(keyword => content.includes(keyword)).length

    // 首先包含餐食关键字
    if (countPrefix > 0) {
        // 同时再必须包含食物名称个数
        const { count, searchCount, foods } = await getFoodsInContent(content, agent);
        if (count >= 2) {
            logger.info(`餐食打卡:${count}食物,搜索食物${searchCount}`);
            return {
                mealType: extraMealType(content),
                foods
            }
        } else {
            logger.warn(`非餐食打卡: ${count}个食物,搜索食物 ${searchCount} ${content}`);
        }
    }
    return undefined
}


/**
 * 处理减重打卡消息
 * @param params 打卡消息参数
 * @returns 处理结果
 */
const weightLossCheckInFun = async (params: {
    msg: INotAtMsg, test?: boolean;
}): Promise<NotAtMsgTasksAiResponse> => {
    const { msg, test } = params;
    const { objectId, agent, account, content, createdAt,
        userName, groupName, refItchatUser: usr, refItchatGroup: grp } = msg;
    const tableName = `AIGroupNotAtMsg_${agent}`

    // 计算记录hash
    const hash = calculateHash(`${content}${userName}${grp.objectId}${agent}`);
    // 判断是否重复
    const repeatResult = await weightLossServices.isRepeatCheckIn({
        hash,
        agent,
        wxUserObjectId: usr.objectId,
        wxGroupObjectId: grp.objectId,
        message: content,
        createdAt
    });
    if (repeatResult) {
        logger.warn('重复提交,忽略。', repeatResult.nickName, repeatResult.hash);
        return {
            ...repeatResult,
            error: undefined,
            doneTasks: ['IS_DUPLICATE', 'EXTRACT_WEIGHT_LOSS_SUCESS']
        }
    }

    // 通过gpt获取健康打卡数据(或regex)
    const gptResponse = await gpt.analysisWeightLoss(grp.objectId, content);
    if (!gptResponse) {
        return {
            ...repeatResult,
            doneTasks: ['EXTRACT_WEIGHT_LOSS_ERR'],
            error: undefined,
            showText: undefined,
            showText2: undefined,
            json: undefined
        };
    }

    // 保存体重记录
    const result = await weightLossServices.saveOrMergeWeightLossRecord({
        hash,
        agent,
        account,
        nickName: userName,
        wxGroupObjectId: grp.objectId,
        wxUserObjectId: usr.objectId,
        groupName,
        message: content,
        action: 'weight_loss',
        createdAt,
        conversationId: usr.objectId,
        gptResponse,
    });

    // 保存ai返回结果到聊天记录
    if (result?.showText) {
        logger.info(`记录已保存,可发送文本: ${groupName}, ${result.showText}`);
        await updateChatRecordForAIContent(tableName, objectId,
            gptResponse.aiJsonStr, gptResponse.aiJson, gptResponse.aiResponse
            , ["EXTRACT_WEIGHT_LOSS_SUCESS"]);
        return {
            ...result,
            doneTasks: ['EXTRACT_WEIGHT_LOSS_SUCESS']
        };
    }

    return {
        ...result,
        doneTasks: ['EXTRACT_WEIGHT_LOSS_ERR'],
        error: undefined,
        showText: undefined,
        showText2: undefined,
        json: undefined
    };
};
/**
 * 处理餐食卡路里打卡打卡(会调用GPT)
 *
 */
const foodCalorieCheckInFunGPT = async (params: {
    msg: INotAtMsg; group: WxGroupInfo; foodInfo: IAnalyzedFood, test?: boolean
}): Promise<NotAtMsgTasksAiResponse<AIRespCaloriesJson>> => {
    const { msg, group, test, foodInfo } = params;
    const { agent, account, objectId } = msg
    const tableName = `AIGroupNotAtMsg_${agent}`

    // 判断群组开关
    if (group?.switchs?.includes('REPLY_FOOD_CHECKIN_CLOSE')) { logger.warn(`${group?.groupNickName} 餐食卡路里打卡群组开关关闭,不处理!`); return undefined; }

    if (test) { logger.info(`测试模式,不调用AI处理餐食卡路里打卡! \n${msg.content}`); return undefined; }

    const records = await getCaloriesRecordsToday({ agent, groupOID: group.objectId })
    let { baseText, recordsText } = makeCaloriesRecordText(records)
    if (!baseText) baseText = `用户昵称: ${msg.userName}\n微信群: ${group.groupNickName}\n`
    if (!recordsText) recordsText = ""

    const calPlanText = await weightLossServices.makeCaloriesPlanText(agent, group.objectId)
    logger.info(`卡路里打卡,通用信息:\n${baseText}`);
    logger.info(`卡路里打卡,今日已有打卡记录:\n${recordsText}`);
    // 调用AI
    const resp = await gpt.makeMealCheckInSummary({
        userid: group.objectId, mealType: foodInfo.mealType, message: msg.content, baseText, recordsText, calPlanText
    });
    // 保存ai返回结果到聊天记录
    if (resp) {
        logger.info(`卡路里打卡,可发送文本: ${group?.groupNickName}, ${resp.showText}`);
        let doneTasks: NotAtMsgTasks = 'NORMAL'
        //-------------------------------------------
        // 为卡路里打卡处理后续的操作,保存统计数据,修改NotAtMsg的doneTasks
        const result = await caloriesServices.procCaloriesResults({
            agent, account, msg, aiResponse: resp,
            mealType: foodInfo.mealType, foods: foodInfo.foods
        });
        //-------------------------------------------
        if (result?.total) {
            result.total += 180; //加上基础摄入,如:这里是植物油20克即180千卡
            resp.showText += "\n------------------------\n"
            resp.showText += `今日卡路里约:${result.total}千卡`
        }
        if (resp.json?.nutrition?.length > 0) {
            const list = resp.json.nutrition.filter(item => Number(item.value) > 0)
            if (list?.length > 0 && list?.length < 10) {
                const icons = ["1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣", "7️⃣", "8️⃣", "9️⃣"]
                resp.showText2 = "🥕 本次营养评估(仅供参考):\n"
                resp.showText2 += resp.json.nutrition.map((item, index) => `${icons[index]} ${item.name}:  ${item.value}${item.unit ?? ""}`).join("\n")

                // 蛋白质重量体重比
                const userCard = await userCardServices.getUserWeightLossCard(agent, msg.refItchatUser.objectId, group.objectId);
                if (result.totalNutrition) {
                    resp.showText2 += "\n------------------------\n"
                    resp.showText2 += "今日营养汇总:\n"
                    const nutritionList = ['蛋白质', '脂肪', '碳水化合物']
                    Object.entries(result.totalNutrition).forEach(([key, value]) => {
                        //非关注营养跳过
                        if (!nutritionList.includes(key)) { return; }
                        if (key === '脂肪') value += 20; //加上基础摄入,如:这里是植物油(脂肪)20克即180千卡
                        resp.showText2 += `${key}: ${value}克 `;
                        if (result?.total) {//与卡路里的比例
                            // 营养成份 * 4 / 总卡路里
                            resp.showText2 += `(${Math.round((value * 4 / result.total) * 100)}%)`;
                        }

                        if (userCard?.lastWeight && value) {
                            const bodyWeight = userCard.lastWeight;
                            const proteinWeightPercentage = (value / bodyWeight);
                            resp.showText2 += `\n  📊【${proteinWeightPercentage.toFixed(2)}】克每公斤\n`;
                        } else {
                            resp.showText2 += `\n`;
                        }

                    });
                    // 获取用户的减重方案:
                    const card = await refreshCaloriesCardByGPTResp(agent, result);
                    if (card?.plan) {
                        resp.showText2 += `\n今日目标卡路里: ${card.plan.calories}千卡\n`;
                        card.plan.meals.map((meal, index) => {
                            resp.showText2 += `${icons[index]} ${getBeijingTime(meal.time.toISOString(), 'HH:mm')} ${meal.type}: ${meal.calories}千卡\n`;
                        })
                    }
                    // const proteinWeight = result.totalNutrition["蛋白质"];
                    // if (userCard?.lastWeight && proteinWeight) {
                    //     const bodyWeight = userCard.lastWeight;
                    //     const proteinWeightPercentage = (proteinWeight / bodyWeight);
                    //     resp.showText2 += `\n📊 蛋白质体重比: ${proteinWeightPercentage.toFixed(2)}克每公斤`;
                    // }
                }
            }
        }

        //-------------------------------------------
        // 保存ai返回结果到NotAt聊天记录
        logger.info(`记录已保存,可发送文本: ${group.groupNickName}, ${resp.showText} ${resp.showText2}`);
        await updateChatRecordForAIContent(tableName, objectId, resp.showText, resp.json, resp, [doneTasks]);
        //-------------------------------------------
        return {
            objectId, account, balanceAITokens: 0, hash: '',
            showText: resp.showText,
            showText2: resp.showText2,
            json: resp.json,
            doneTasks: ['EXTRACT_CALORIES_SUCESS']
        };
    }
    return undefined;
};
// 微信公众号无群组信息,使用公众号的id创建一个
const makeWxGroupInfoByWechatmp = async (agent: EthAddress, user: WxUserInfo, wechatMpId: string, wechatMpName: string): Promise<WxGroupInfo> => {
    let agentInfo = await getUserInfo(agent)
    if (!agentInfo) {
        const newUser = await createUser({
            agent,
            account: agent, // 微信公众号的account,也就是agent
            addresses: [agent],// 微信公众号的account,也就是agent
            wxid: wechatMpId,
            ssoID: `wechatmp:${wechatMpId}-${uuidv4()}`,
            name: wechatMpName, nickName: wechatMpName,
            objectId: '', userObjectId: '', avatar: '', publicKey: '', balanceAITokens: 0,
            balanceScore: 0, balanceCNY: 0, usedAITokens: 0, totalAITokens: 0
        })
        if (!newUser) throw new Error(`公众号信息未初始化,无法记录 ${user} ${wechatMpId} ${wechatMpName}`)
        agentInfo = await getUserJson(newUser, true);
    }
    const mpGroup = {
        agent, groupUserName: agentInfo.wxid,
        groupNickName: agentInfo.nickName || agentInfo.name,
        groupHeadImgUrl: agentInfo.avatar
    }

    let group = await wxGroupServices.getWxGroupInfo(agent, mpGroup, true)
    if (!group) group = await addOrUpdateItchatGroup(agent, mpGroup, [user])

    //const userCard = await userCardServices.getUserInfoCard(agent, user.objectId, wechatmpId)
    const userDetail = await wxUserServices.getWxUserInfo(agent, user, true)
    // 公众号模拟的微信群,需要有很多个性化的内容,通过用户详情,信息卡进行个性化 #TODO yhk
    group.createdAt = userDetail.createdAt
    //group.switchs = userCard.

    return group
}
/**
 * 处理健康打卡消息的异步函数(处理单条记录)
 * @param tableName - 数据表名称
 * @param data - 消息数据对象
 * @param test - 是否为测试模式，默认为false
 * @returns Promise<void>
 */
const processHealthCheckInThread = async ({ tableName, data, test = false, isRealTime = false }: {
    tableName: string, data: INotAtMsg & { to_user_id?: string, to_user_nickname?: string },
    isRealTime?: boolean, //是否实时处理,默认为false
    test?: boolean
}): Promise<NotAtMsgTasksAiResponse> => {
    try {
        // 解构基础数据
        const { refItchatGroup, agent, account, refItchatUser, groupName, content, objectId, is_at, source } = data;

        // 获取用户信息
        let user = await getWxUserByObjectId(refItchatUser.objectId);
        if (!user) {
            user = await getWxUserByAccount(account);
            await moveInvalidRecords(tableName, objectId);
            throw new Error(`找不到用户信息: ${objectId} ${groupName}`);
        }

        let group: WxGroupInfo;
        if (source?.startsWith("wechatmp")) {
            group = await makeWxGroupInfoByWechatmp(agent, user, data.to_user_id, data.to_user_nickname)
            data.refItchatGroup = makeWxGroupRef(group.objectId)
        } else {
            group = await wxGroupServices.getWxGroupInfoByObjectId(refItchatGroup?.objectId);
        }
        // 获取群组信息

        if (!group) {
            await moveInvalidRecords(tableName, objectId);
            logger.error(`移到无效记录:${tableName},${content}`)
            throw new Error(`找不到群信息: ${objectId} ${groupName}`);
        }


        //有些关键字确定是"打卡"关键字,是关键字则[跳过]
        const keywordsIgnore = ['减重打卡关键字', '减重打卡格式', '减重', '减重周报', '减重月报',
            '【减重打卡格式说明】', '减重打卡格式说明', "已成功打卡!", "已成功打卡"]
            .concat(process.env.HEALTH_CHECKIN_KEYWORDS_IGNORE?.split(',') || [])
        // 全字匹配,是指令,[跳过]
        if (keywordsIgnore.some(keyword => content === keyword)) {
            // logger.warn(`打卡消息完全匹配指令,跳过: ${groupName}, 全字匹配内容: ${content}`);
            return {
                objectId,
                account,
                balanceAITokens: 0,
                hash: '',
                showText: '',
                doneTasks: ['HAS_KEYWORD'],
                error: '关键字内容'
            };
        }
        // content包含关键字,是提示信息,[跳过]
        const keywordsIgnoreIncludes = ['【减重打卡格式说明】', '减重打卡格式说明', "已成功打卡!", "已成功打卡"]
        if (keywordsIgnoreIncludes.some((keyword: string) => content.includes(keyword))) {
            logger.warn(`打卡消息包含提示关键字跳过: ${groupName}, 内容: ${content}`);
            return {
                objectId, account, balanceAITokens: 0, hash: '', showText: '', doneTasks: ['HAS_IGNORE'], error: '需要忽略的关键字内容'
            };
        }
        //--------------------------------------------------------------
        // 判断是否为健康打卡群
        if (!isHealthGroup(agent, groupName, new Date(group.createdAt)))
            return {
                objectId, account, balanceAITokens: 0, hash: '', showText: '', doneTasks: ['NOT_WEIGHT_LOSS_GROUP']
            };
        //--------------------------------------------------------------
        // 减重打卡相关命令
        if (isRealTime) {
            const result = await weightLossServices.procCommand(agent, content, user.objectId, group.objectId);
            if (result) {
                return {
                    ...result, showText: result.data?.showText, doneTasks: ['WEIGHT_LOSS_CMD']
                };
            }
        }
        //--------------------------------------------------------------
        // 验证是否为减重打卡[content]
        if (isWeightLossCheckIn(content, data.doneTasks, group.aiKeywords)) {
            logger.info(`=====>减重打卡消息:${content} |---- ${groupName}, AI开关状态: ${group.aiSwitch ? '开启' : '关闭'}`);
            return weightLossCheckInFun({ msg: data, test });
        } else {
            // 是否为餐食卡路里打卡(现在只能处理实时信息)
            // [注意] 当处理所有历史记录时,需要避免重复消耗aiTokens
            let isFoodsResult: IAnalyzedFood = undefined
            if ((isRealTime || test) && (!is_at) && (isFoodsResult = await isFoodCalorieCheckIn(content, data.doneTasks, agent))) {
                logger.info(`=====>餐食卡路里打卡消息:${content} |---- ${groupName}, AI开关状态: ${group.aiSwitch ? '开启' : '关闭'}`);

                if (test) return undefined

                return foodCalorieCheckInFunGPT({ msg: data, group, foodInfo: isFoodsResult, test });
            }
            logger.warn(`非健康打卡${isRealTime ? '[实时]' : '[定时轮询]'}:${data.content.length > 10 ? data.content.substring(0, 10) + '...' : data.content.replace(/\r\n|\n|\r/g, ",")} |----- ${data.groupName}`)
            return {
                objectId, account, balanceAITokens: 0, hash: '', showText: '', doneTasks: ['NOT_WEIGHT_LOSS_CHECKIN', 'NOT_FOOD_CALORIE_CHECKIN']
            }
        };
    } catch (error: any) {
        logger.error(`处理健康打卡消息发生意外: ${error.message} ${data.groupName} `);
    }
    return undefined;
};
export default calcKeywordsStatistics;
export {
    calcKeywordsStatistics,
    foodCalorieCheckInFunGPT,
    isFoodCalorieCheckIn,
    isHealthGroup,
    isNeedReplyGroup,
    processHealthCheckInThread
};


