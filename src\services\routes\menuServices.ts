import { ParseDB } from '../../database/database';
import { GResponse } from '../../types/types';
import ApiError from '../../utils/api_error';
import { BaseServices } from "../baseServices";
import routeServices from './routeServices';
import {
    TABLE_NAME_MENU,
    Menu,
    getMenuRecordJson,
    getMenuChildren,
    createMenuRecursive,
    isMenuExists,
    isMenuExistsExcludeId,
    getMenu,
    hasChildMenus,
    createMenuItem,
    clearMenus,
    mergeAndDeduplicateRoutes
} from './routeUtils';

const TABLE_NAME = TABLE_NAME_MENU;

class MenuServices extends BaseServices {
    constructor() {
        super();
    }
    // 初始化菜单，从路由数据创建菜单
    servicesInitMenu = async ({ sessionId }: { sessionId: string }): Promise<GResponse<any>> => {
        const sess = await this.isSuperMan(sessionId); // 检查是否为管理员

        // 获取路由数据
        const constantRoutesResponse = await routeServices.servicesGetConstantRoutes({ sessionId });
        const userRoutesResponse = await routeServices.servicesGetUserRoutes({ sessionId });

        const constantRoutes = constantRoutesResponse.data;
        const userRoutes = userRoutesResponse?.data?.authRoutes;
        if (!constantRoutes && !userRoutes) {
            throw new ApiError('获取路由数据失败', 500);
        }

        // 合并和去重路由数据
        const uniqueRoutes = mergeAndDeduplicateRoutes(constantRoutes, userRoutes);

        // 清空现有菜单
        await clearMenus(sess.agent);

        // 创建菜单
        const createdMenus = [];
        const uidCounter = { value: 1000 }; // 使用对象以便在递归中传递引用

        // 开始创建顶级菜单
        for (const route of uniqueRoutes) {
            const menuData = await createMenuRecursive(sess.agent, route, 0, 0, uidCounter);
            if (menuData) {
                createdMenus.push(menuData);
            }
        }

        return this.makeGResponse({
            message: `成功创建 ${createdMenus.length} 个菜单`,
            count: createdMenus.length,
            menus: createdMenus
        });
    }
    servicesGetMenuList = async ({ sessionId }: { sessionId: string }) => {
        const sess = await this.checkSession(sessionId);
        const query = new ParseDB.Query(TABLE_NAME);
        query.equalTo('agent', sess.agent)
        query.equalTo('parentId', 0)
        const results = await query.find()
        if (results && results.length) {
            const promise = results.map(async item => {
                const result = getMenuRecordJson(item);
                // 获取所有子菜单（包括嵌套子菜单）
                const children = await getMenuChildren(sess.agent, item.get('uid'));

                // 如果有子菜单，添加到结果中
                if (children && children.length > 0) {
                    return { ...result, children };
                }

                return result;
            })
            const data = await Promise.all(promise)
            if (data?.length > 0)
                return this.makeGResponseList(data, 1, data.length, data.length)
            return this.makeGResponseListError()
        }

        return this.makeGResponseList([{
            id: 1,
            menuType: '启用',
            menuName: '菜单1',
            routeName: 'home',
            routePath: '/home',
            status: '启用',
            hideInMenu: false,
            icon: 'icon1',
            iconType: '1',
            parentId: 1,
            order: 1,
            component: 'layout.base$view.home',
            buttons: [{
                code: 'button1',
                desc: '按钮1'
            }],
            children: [{
                menuName: '子菜单1',
                routeName: 'childRoute1',
                routePath: '/childRoute1',
                icon: 'childIcon1',
                iconType: '1',
                buttons: [{
                    code: 'childButton1',
                    desc: '子按钮1'
                }]
            }]
        }], 1, 10, 1)
    }

    servicesGetAllPages = async ({ sessionId }: { sessionId: string }) => {
        await this.checkSession(sessionId);
        return this.makeGResponse(["home", "login"])
    }

    // 添加菜单
    servicesAddMenu = async ({ sessionId, menuData }: { sessionId: string, menuData: Menu }): Promise<GResponse<any>> => {
        const sess = await this.checkSession(sessionId);
        await this.isSuperMan(sessionId); // 检查是否为管理员

        // 检查必要字段
        if (!menuData.menuName || !menuData.routeName || !menuData.routePath) {
            throw new ApiError('缺少必要字段', 400);
        }

        // 检查是否已存在相同routeName的菜单
        const exists = await isMenuExists(sess.agent, menuData.routeName);
        if (exists) {
            throw new ApiError(`已存在相同路由名称(${menuData.routeName})的菜单`, 400);
        }

        // 创建新菜单
        const result = await createMenuItem(sess.agent, menuData);
        return this.makeGResponse(result.toJSON());
    }

    // 更新菜单
    servicesUpdateMenu = async ({ sessionId, objectId, menuData }: { sessionId: string, objectId: string, menuData: Partial<Menu> }): Promise<GResponse<any>> => {
        const sess = await this.checkSession(sessionId);
        await this.isSuperMan(sessionId); // 检查是否为管理员

        if (!objectId) {
            throw new ApiError('缺少菜单ID', 400);
        }

        const menu = await getMenu(sess.agent, objectId);
        if (!menu) {
            throw new ApiError('菜单不存在', 404);
        }

        // 如果更新了routeName，检查是否与其他菜单冲突
        if (menuData.routeName && menuData.routeName !== menu.get('routeName')) {
            const exists = await isMenuExistsExcludeId(sess.agent, menuData.routeName, objectId);
            if (exists) {
                throw new ApiError(`已存在相同路由名称(${menuData.routeName})的菜单`, 400);
            }
        }

        // 更新字段
        Object.entries(menuData).forEach(([key, value]) => {
            if (value !== undefined) {
                menu.set(key, value);
            }
        });

        const result = await menu.save();
        return this.makeGResponse(result.toJSON());
    }

    // 删除菜单
    servicesDeleteMenu = async ({ sessionId, objectId }: { sessionId: string, objectId: string }): Promise<GResponse<boolean>> => {
        const sess = await this.checkSession(sessionId);
        await this.isSuperMan(sessionId); // 检查是否为管理员

        if (!objectId) {
            throw new ApiError('缺少菜单ID', 400);
        }

        const menu = await getMenu(sess.agent, objectId);
        if (!menu) {
            throw new ApiError('菜单不存在', 404);
        }

        // 检查是否有子菜单
        const hasChildren = await hasChildMenus(sess.agent, menu.id);
        if (hasChildren) {
            throw new ApiError('该菜单下有子菜单，无法删除', 400);
        }

        await menu.destroy({ useMasterKey: true });
        return this.makeGResponse(true);
    }
}

export default new MenuServices();
