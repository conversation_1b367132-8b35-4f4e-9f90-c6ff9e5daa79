import logger from '../utils/logger';

import { IKnowReferer } from '../controllers/controller';
import { PlatformName, PlatformType } from '../types/platformReward';
import { EthAddress, Guild, GuildPlatform, Role, RolePlatform } from '../types/types';
import ApiError from '../utils/api_error';
import { fetchOauthByCode, OAuthUserInfo } from './gateSSO/oauth2Api';
import { getGuildPlatformsByProjectId, getGuildPlatformsByUserId } from './guildPlatformServices';
import { serviceGetGuild } from './guildServices';
import session, { SessionInfo } from './sessionManager';
import { ParseDB, ParseObject } from '../database/database';

export type PlatformBody = {
  id?: string;
  platformType: PlatformType;

  // userId?: number;
  account?: string;

  platformName: PlatformName;
  platformProjectId?: string;
  platformUserId: string;
  platformUserData: any;

  displayName: string;
  username: string;
  avatar: string;

  // & {
  //   accessToken: string;
  //   createAt: string;
  //   createdTimestamp: number;
  //   joinAt: string;
  // } ;
};
type PlatformUserJoin = {
  platformName: PlatformName;
  platformProjectId: number;
  platformUserId: string;
  platformUserData: any;
};

type GuildPlatformData = {
  guildName: string;
  platformProjectId: string;
  roles: {
    name: string;
    platformRoleId: string;
    [key: string]: string;
  }[];
};
type PlatformJoinResponse =
  | (GuildPlatformData & { inviteLink?: never })
  | {
    guildName?: never;
    platformProjectId?: never;
    roles?: never;
    inviteLink: string;
  };
type PlatformStatusResponse = (GuildPlatformData & {
  platformProjectName: string;
})[];
const getRecordJson = (record: ParseObject): PlatformUserJoin =>
  record
    ? {
      platformName: record.get('platformName'),
      platformProjectId: record.get('platformProjectId'),
      platformUserId: record.get('platformUserId'),
      platformUserData: record.get('platformUserData'),
    }
    : null;
const addOrUpdateJoinRecord = async (
  platformName: PlatformName,
  platformProjectId: string,
  platformUserId: string,
  platformUserData: any,
): Promise<ParseObject> => {
  const query = new ParseDB.Query('PlatformUserJoin');
  query.equalTo('platformName', platformName);
  query.equalTo('platformProjectId', platformProjectId);
  query.equalTo('platformUserId', platformUserId);

  const result = await query.first();
  if (result) {
    result.set('platformUserData', platformUserData);
    return result;
  }

  const Tables = ParseDB.Object.extend('PlatformUserJoin');
  const userJoin = new Tables();

  userJoin.set('platformName', platformName);
  userJoin.set('platformProjectId', platformProjectId);
  userJoin.set('platformUserId', platformUserId);
  userJoin.set('platformUserData', platformUserData);
  userJoin.set('operation', 'join');
  return userJoin.save();
};
async function platformUserJoin(platformBody: PlatformBody): Promise<PlatformJoinResponse> {
  logger.info(`/platform/user/join platformUserJoin:`, platformBody?.platformName, platformBody);

  const { platformName, platformProjectId, platformUserId, platformUserData } = platformBody;

  if (!platformUserId || !platformName || !platformProjectId) {
    throw new Error('platformUserJoin: missing params');
  }
  const record = await addOrUpdateJoinRecord(platformName, platformProjectId, platformUserId, platformUserData);
  logger.info('addOrUpdateJoinRecord:', !!record);

  const guildPlatforms = await getGuildPlatformsByProjectId(platformName, platformProjectId);
  if (guildPlatforms?.length > 0) {
    const guildPlat = guildPlatforms?.find((gp: GuildPlatform) => gp.platformProjectId === platformProjectId);
    if (guildPlat?.guildId) {
      const guild: Guild = await serviceGetGuild(guildPlat.guildId);
      const inviteLink = guildPlat.invite;

      // 确定是否有包含platform
      const accessRoles = await Promise.all(
        guild?.roles.map(async (role: Role) => {
          const rolePlatforms = role?.rolePlatforms?.find(
            async (rp: RolePlatform) => rp.guildPlatform?.platformName === platformName,
          );
          return rolePlatforms
            ? {
              name: role?.name,
              platformRoleId: role?.id.toString(),
            }
            : null;
        }),
      );

      const roles = accessRoles?.filter(Boolean);
      return roles?.length > 0
        ? {
          // 有符合要求的role
          guildName: guild.name,
          platformProjectId,
          roles,
          // inviteLink,
        }
        : {
          // 无符合要求的role
          inviteLink,
        };
    }
  }
  return { guildName: null, platformProjectId, roles: [] };
}

const platformUserLeave = async (platformBody: PlatformBody): Promise<PlatformUserJoin> => {
  logger.info(`/platform/user/leave`, platformBody);

  const { platformName, platformProjectId, platformUserId, platformUserData } = platformBody;

  const Tables = ParseDB.Object.extend('PlatformUserJoin');

  const userLeave = new Tables();

  userLeave.set('platformName', platformName);
  userLeave.set('platformProjectId', platformProjectId);
  userLeave.set('platformUserId', platformUserId);
  userLeave.set('platformUserData', platformUserData);
  userLeave.set('operation', 'leave');

  const result = await userLeave.save();
  return getRecordJson(result);
};
const getPlatformUserInfo = async (platformName: PlatformName, platformUserId: string): Promise<PlatformUserJoin> => {
  const query = new ParseDB.Query('PlatformUserJoin');

  query.equalTo('platformName', platformName);
  query.equalTo('platformUserId', platformUserId);
  query.equalTo('operation', 'join');

  query.descending('createdAt');

  const result = await query.first();
  return getRecordJson(result);
};

const platformGetGuild = async ({
  platformName,
  platformSpecificGuildId: platformProjectId,
}: {
  platformName: PlatformName;
  platformSpecificGuildId: string;
}): Promise<any> => {
  const guildPlatforms = await getGuildPlatformsByProjectId(platformName, platformProjectId);
  if (guildPlatforms?.length > 0) {
    return serviceGetGuild(guildPlatforms[0].guildId);
  }
  return null;
};
const platformUserStatus = async (platformBody: PlatformBody): Promise<PlatformStatusResponse> => {
  logger.info('platformUserStatus:', platformBody?.platformName, platformBody);

  const { platformName, platformUserId } = platformBody;

  if (!platformUserId || !platformName) {
    throw new Error('platformUserStatus: missing params');
  }

  const guildPlatforms = await getGuildPlatformsByUserId(platformName, platformUserId);
  const rets = await Promise.all(
    guildPlatforms?.map(async (gp: GuildPlatform) => {
      const guild = await serviceGetGuild(gp?.guildId);
      if (!guild) return null;
      return { guild, plat: gp };
    }),
  );
  const results = rets?.filter(Boolean);
  return results?.map(({ guild, plat }) => ({
    guildName: guild?.name,

    platformName,
    platformUserId,
    platformProjectId: guild?.id?.toString(),
    platformProjectName: plat?.name,

    roles: [],
  }));
};
// 通过第三方平台的code获取access_token 及用户信息
async function getPlatformUserInfoByCode({
  account,
  code, clientId,
  platformName,
  agent, tags
}: {
  account: EthAddress;
  code: string; clientId: string;
  platformName: PlatformName;
  agent: EthAddress;
  tags: string[];
}, referer: IKnowReferer): Promise<OAuthUserInfo & SessionInfo> {
  const userinfo: OAuthUserInfo = await fetchOauthByCode(agent, code, platformName, clientId);
  logger.warn(`通过${platformName}获取用户信息:`, userinfo.name, userinfo.roles, userinfo.permissions);
  if (userinfo) {
    const sess = await session.login(userinfo.account || account, { ...userinfo, agent, referer, tags });
    logger.warn(`创建用户登录session:`,
      sess.name, sess.account, sess.roles, sess.permissions,
      sess.platformName, sess.platformUserId);
    logger.warn(`agent:${sess.agent} wxid:${sess.wxid} userObjectId:${sess.userObjectId}`);
    return {
      ...userinfo,
      ...sess,
    };
  }
  throw new ApiError(`get userinfo from ${platformName} by ${code} fail`);
}

const platformServices = {
  platformUserLeave,
  platformGetGuild,
  getPlatformUserInfo,
  platformUserStatus,
  getPlatformUserInfoByCode,
  platformUserJoin,
};

export default platformServices;
