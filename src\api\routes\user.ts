import { Router } from 'express';
import { body } from 'express-validator';
import * as userController from '../../controllers/userController';
import * as chatController from '../../controllers/chatlistController';
import { SupportPlatform } from '../../types/platformReward';
import validators from '../validators';

const router: Router = Router();

/**
 * 用户管理路由
 * 
 * 功能列表:
 * 1. 用户信息管理
 *    - 创建/更新/获取用户信息
 *    - 获取用户列表
 * 2. 平台连接管理
 *    - 连接/断开第三方平台账号
 * 3. 群组管理
 *    - 加入/退出群组
 *    - 获取群组信息
 * 4. 医生管理
 * 5. 微信群管理
 * 6. 定时器管理
 * 7. AI Token管理
 */

export default (app: Router) => {
  app.use('/user', router);

  router.get('/sso/:ssoID', validators.paramTrimmer('ssoID'), userController.getUserBySSO);
  router.get('/:account', validators.paramTrimmer('account'), userController.getUser);
  router.get('/list/:pageNum/:pageSize', validators.headerStringLengthValidator('authorization', 4, false),
    validators.paramNumberValidator('pageNum', 1, false, 100),
    validators.paramNumberValidator('pageSize', 2, false, 100),
    userController.getUserListForAdmin
  )
  // doctor list
  router.get(
    '/doctor/list/:sessionId/:sign',
    validators.paramStringLengthValidator('sessionId', 24, false),
    validators.paramStringLengthValidator('sign', 4, false),
    userController.getDoctorList,
  );

  router.get('/:account/statusUpdate/:guildId', validators.paramTrimmer('account'), userController.getStatusUpdate);
  router.post(
    '/details/:account',
    validators.authentication(),
    validators.paramAddressValidator('account'),
    userController.getUserDetails,
  );
  router.get('/membership/:address', validators.paramTrimmer('address'), userController.getUserGuildMemberships);
  router.get('/upvotyAuth/:account', validators.paramTrimmer('account'), userController.getUserUpvotyAuth);
  router.post(
    '/join',
    validators.authentication(),
    [body('payload.platform').isIn(['DISCORD', 'TELEGRAM']).optional(), validators.bodyIdValidator('payload.guildId')],
    userController.joinGuild,
  );
  router.post(
    '/leaveGuild',
    validators.authentication(),
    [validators.bodyIdValidator('payload.guildId')],
    userController.leaveGuild,
  );
  router.post('/create/:account', validators.paramAddressValidator('account'), userController.createUser);
  // 更新groupx用户信息,同 patch
  router.put('/:account',
    [
      validators.paramAddressValidator('account'),
      validators.bodyStringLengthValidator('payload.account', 1, false, 64)],
    userController.updateUser);
  // 更新groupx用户信息,同 put
  router.patch('/:account', [
    validators.paramAddressValidator('account'),
    validators.bodyStringLengthValidator('payload.account', 1, false, 64)],
    userController.updateUser);
  // 通过code获取平台用户信息同时添加或更新到表UsersPlatformUser中
  // 微信公众号,小程序等都通过这个请求建立用户
  router.post(
    '/connect',
    validators.authentication(),
    [body('payload.platformName').isIn(SupportPlatform).optional(false), body('payload.authData').isObject()],
    userController.connect,
  );

  router.post(
    '/disconnect',
    validators.authentication(),
    [body('payload.platformName').isIn(SupportPlatform).optional(false)],
    userController.disconnect,
  );
  //-----------------------------------------------
  // my friends
  router.get(
    '/friends/:sessionId/:sign',
    validators.paramStringLengthValidator('sessionId', 24, false),
    validators.paramStringLengthValidator('sign', 4, false),
    userController.getMyFriends,
  );

  router.post(
    '/friend/get-timers/:account',
    validators.paramAddressValidator('account'),
    userController.getFriendTimers,
  );
  router.post('/friend/timer/:account', validators.paramAddressValidator('account'), userController.addFriendTimer);

  router.delete(
    '/friend/timer/:account',
    validators.paramAddressValidator('account'),
    validators.bodyStringLengthValidator('payload.objectId', 1, false),
    userController.delFriendTimer,
  );
  router.post(
    '/friend/get-knowledge/:account',
    validators.paramAddressValidator('account'),
    userController.getFriendKnowledge,
  );
  //--------------------------------------
  // my groups

  // 获取我拥有的微信群
  router.get(
    '/wxgroups/my-own/:pageNum/:pageSize',
    validators.paramNumberValidator('pageNum', 1, false, 100),
    validators.paramNumberValidator('pageSize', 4, false, 100),
    userController.getMyWxGroupsOwn,
  );
  // 获取我拥有的所有微信群的用户
  router.get(
    '/wxusers/my-own/:pageNum/:pageSize',
    validators.paramNumberValidator('pageNum', 1, false, 100),
    validators.paramNumberValidator('pageSize', 4, false, 100),
    userController.getMyWxUsersOwn,
  );
  // 获取我拥有的微信群(同上)
  router.get(
    '/wxgroups/:sessionId/:sign',
    validators.paramStringLengthValidator('sessionId', 24, false),
    validators.paramStringLengthValidator('sign', 4, false),
    userController.getMyWxGroups,
  );
  //  获取我加入的微信群
  router.get(
    '/wxgroups/joined/:pageNum/:pageSize',
    validators.headerStringLengthValidator('authorization', 24, false),
    userController.getMyWxGroupsOfJoined,
  );
  // 邀请用户入群
  router.post(
    '/wxgroups/invite',
    userController.inviteUserToWxGroups,
  );
  router.get(
    '/wxgroup/get/:groupId/:sessionId/:sign',
    validators.paramStringLengthValidator('groupId', 5, false),
    userController.getWxGroup,
  );

  router.post(
    '/wxgroup/get',
    userController.getWxGroupInfo,
  );
  router.post(
    '/wxgroup/members',
    userController.getWxGroupMembers,
  );
  router.post(
    '/wxgroup/statistics',
    chatController.getWxGroupStatistics,
  );
  //----------------------------------------
  // 定时器

  router.post(
    '/wxgroup/public-timer/add', [validators.headerStringLengthValidator('BotAgent', 5, false)],
    userController.addGroupTimerPublic,
  );
  router.get(
    '/wxgroup/public-timers/get/:groupObjectId', [validators.paramStringLengthValidator('groupObjectId', 6, false)],
    userController.getGroupTimerPublic,
  );
  // 获取我的定时器列表(可搜索) ********
  router.get(
    '/timer/list/:pageNum/:pageSize',
    validators.headerStringLengthValidator("session-id", 24, false, 100),
    validators.headerStringLengthValidator("iknow-agent", 24, false, 100),
    validators.paramNumberValidator("pageSize", 3, false, 100),
    validators.paramNumberValidator("pageNum", 1, false, 500),
    userController.getMyTimers,
  );
  // 删除定时器(同'/group/timer/:account') ********
  router.delete(
    '/timer',
    validators.headerStringLengthValidator("session-id", 24, false, 100),
    validators.headerStringLengthValidator("iknow-agent", 24, false, 100),
    validators.bodyStringLengthValidator('payload.timerObjectId', 6, false, 100),
    userController.delMyTimer,
  );
  router.post(
    '/group/get-timers/:account',
    validators.paramAddressValidator('account'),
    userController.getMyGroupTimers,
  );
  router.post('/group/timer/:account', validators.paramAddressValidator('account'), userController.addGroupTimer);
  // 同时为多个群添加定时器
  router.post('/groups/timer/:account', validators.paramAddressValidator('account'), userController.addGroupsTimer);
  // router.post("/group/timer/:account", validators.paramAddressValidator("account"), userController.addGroupTimer);
  router.delete(
    '/group/timer/:account',
    validators.paramAddressValidator('account'),
    validators.bodyStringLengthValidator('payload.objectId', 1, false),
    userController.delGroupTimer,
  );
  // 定时器
  //----------------------------------------
  router.post(
    '/wxgroup/update/:objectId',
    validators.paramStringLengthValidator('objectId', 1, false),
    userController.updateWxGroup,
  );
  router.delete(
    '/wxgroup/:objectId',
    validators.paramStringLengthValidator('objectId', 1, false),
    userController.delMyWxGroup,
  );
  //--------------------------------------
  // 随访信息管理
  router.post('/follow-up/:account', validators.paramAddressValidator('account'), userController.addFollowUpInfo);
  router.delete('/follow-up/:account', validators.paramAddressValidator('account'), userController.delFollowUpInfo);
  router.post('/follow-up/list/:account', validators.paramAddressValidator('account'), userController.getFollowUpList);
  //--------------------------------------
  // 关注用户
  router.post('/watching', userController.addWatching);
  router.delete('/watching', userController.delWatching);
  router.get('/watching/list/:pageNum/:pageSize', userController.getWatchingList);
  router.get('/watching/:objectId', userController.isWatching);
  //--------------------------------------
  // AI Tokens 消费
  router.post('/ai-tokens/consume/:account', userController.consumeAiTokens);
  router.post('/ai-tokens/recharge/:account', userController.rechargeAiTokens);
  //--------------------------------------
};
