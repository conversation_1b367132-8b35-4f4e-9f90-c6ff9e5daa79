// y

import ApiError from '../../utils/api_error';
import { fetcherOnWechat } from '../../utils/fetcher';
import logger from '../../utils/logger';
import signServices from '../sign/signServices';

type WxMsgType = 'text' | 'image' | 'wx_link' | 'video_url' | 'image_url'
type WxMsgContent = {
  type: WxMsgType,
  content: any
}
const makeApiResponse = (code: number, msg: string) => ({ code, msg })
// 连接 iKnow-on-wechat api 服务器,通过微信私聊或公众号发送信息给指定用户
class IKnowOnWechatApi {
  private rsaUser = process.env.API_ON_WECHAT_NAME

  privateKey = ''

  constructor() {
    this.privateKey = signServices.readPrivateKeyFile(`${this.rsaUser}_prikey.pem`);
  }


  // toUser: 接收消息的微信用户的UserName,如:@xxxxxxx
  async sendWxMsg(type: WxMsgType, message: string, toUser: string, toUserNickName: string, wxMsgChannel: string) {
    const sign = signServices.sign(message, this.privateKey);
    try {
      // 构建一个post请求:
      const result = await fetcherOnWechat('/send', {
        body: {
          user: this.rsaUser,
          type,
          msg: message,
          to_user_id: toUser,
          to_user_nickname: toUserNickName,
          sign,
        },
      }, wxMsgChannel);
      if (result.status === 200) {
        const res = await result.json?.();
        return res;
      }
      logger.error('sendText failed: ', result.status, result.statusText, result);
      return result.statusText;
    } catch (error) {
      logger.error('sendText sign error: ', error);
    }
    return '';
  }

  async sendText(message: string, toUser: string, toUserNickName: string, wxMsgChannel: string) {
    return this.sendWxMsg("text", message, toUser, toUserNickName, wxMsgChannel)
  }

  async sendMsgToGroups(msg: WxMsgContent, groupWxids: Array<string>, test: boolean, wxMsgChannel: string): Promise<boolean> {
    const sign = signServices.sign(msg.type + msg.content, this.privateKey);
    try {
      if (test) {
        logger.warn('test=true,不真实发送群消息: ', msg, groupWxids);
        return true;
      }
      const result = await fetcherOnWechat('/send/groups', {
        body: {
          user: this.rsaUser,
          type: msg.type,
          msg,
          groupObjectIds: groupWxids,
          sign,
        },
      }, wxMsgChannel);
      if (result.status === 200) {
        const res = await result.json?.();
        logger.info('sendMsgToGroups success: ', res?.actual_user_nickname);
        return true;
      }
      const text = await result.text();
      logger.error('sendMsgToGroups failed: ', result.status, result.statusText, text);
      throw new ApiError(text, 400);
    } catch (error) {
      logger.error('sendMsgToGroups error: ', error);
    }
    return false;
  }

  async sendPluginsProc(message: string, toUser: string, toUserNickName: string, wxMsgChannel: string, ext: any = null) {
    const sign = signServices.sign(message, this.privateKey);
    try {
      // 构建一个post请求:
      const result = await fetcherOnWechat('/send/plugins', {
        body: {
          user: 'groupx',
          type: 'text',
          msg: message,
          to_user_id: toUser,
          to_user_nickname: toUserNickName,
          ...ext,
          sign,
        },
      }, wxMsgChannel);
      if (result.status === 200) {
        const res = await result.json?.();
        return res;
      }
      logger.error('sendText failed: ', result.status, result.statusText, result);
      return result.statusText;
    } catch (error) {
      logger.error('sendText sign error: ', error);
    }
    return '';
  }

  async sendUrl(type: string, url: string, filename: string, toUser: string, toUserNick: string, wxMsgChannel: string) {
    const sign = signServices.sign(url, this.privateKey);
    try {
      // 构建一个post请求:
      const result = await fetcherOnWechat('/send/url', {
        body: { user: this.rsaUser, type, msg: url, filename, to_user_id: toUser, to_user_nickname: toUserNick, sign },
      }, wxMsgChannel);
      if (result.status === 200) {
        const res = await result.json?.();
        return res;
      }
      logger.error('sendText failed: ', result.status, result.statusText, result);
      return result.status === 200;
    } catch (error) {
      logger.error('sendText sign error: ', error);
    }
    return '';
  }

  // 邀请用户进入微信群
  async inviteUserToGroup({ userWxid, groupWxid, wxMsgChannel }: { userWxid: string, groupWxid: string, wxMsgChannel: string }) {
    const sign = signServices.sign(userWxid, this.privateKey);
    try {
      const result = await fetcherOnWechat('/invite/user/to/group', {
        body: { user: this.rsaUser, userWxid, groupWxid, sign },
      }, wxMsgChannel);
      if (result.status === 200) {
        const res = await result.json?.();
        logger.info('inviteUserToGroup success: ', res);
        return res;
      }
      logger.error('inviteUserToGroup failed: ', result.status, result.statusText, result);
      return result.statusText;
    } catch (error) {
      logger.error('inviteUserToGroup sign error: ', error);
    }
    return '';
  }



  async updatesendAiSetting(agent: string, descption: string, modelName: string, wxMsgChannel: string) {
    const sign = signServices.sign(descption, this.privateKey);
    try {
      // 构建一个post请求:
      const result = await fetcherOnWechat('/update/ai/setting', {
        body: {
          user: this.rsaUser,
          type: 'text',
          agent,
          character_desc: descption,
          mode_name: modelName,
          sign,
        },
      }, wxMsgChannel);
      if (result.status === 200) {
        const res = await result.json?.();
        return makeApiResponse(200, res);
      }
      logger.error('sendText failed: ', result.status, result.statusText, result);
      return makeApiResponse(400, result.statusText);
    } catch (error) {
      logger.error('sendText sign error: ', error);
    }
    return makeApiResponse(400, "未知错误");;
  }
}

export default new IKnowOnWechatApi();
export { WxMsgContent, WxMsgType };

