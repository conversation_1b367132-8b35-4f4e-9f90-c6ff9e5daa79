// 健康调研报告类型定义

export interface BodyCondition {
  饮食习惯与口腔状况: string;
  水分摄入与皮肤状况: string;
  饮食习惯与饥饿感: string;
  心理状态与压力管理: string;
  [key: string]: string; // 支持扩展其他身体状况
}

export interface WeightManagement {
  initialWeight: number;  // 初始体重
  targetWeight: number;   // 目标体重
  weightLossPeriod: number; // 预期减重周期(月)
  bloodSugar: number; // 血糖水平
  bloodPressure: number; // 血压水平
  cholesterol: number; // 胆固醇水平
}

export interface HealthSurvey {
  objectId?: string; // 对象ID
  account: string;    // 用户账号
  userObjectId?: string; // 用户对象ID
  nickname?: string;   // 昵称
  city?: string;        // 城市
  region: string;       // 省份
  district: string;     // 地区
  gender: 'man' | 'woman'; // 性别
  era: number;          // 年代(如70表示70后)
  bodyCondition: BodyCondition;
  otherConditions?: string; // 其他状况说明
  weightManagement: WeightManagement;
  createdAt?: Date;     // 创建时间
  updatedAt?: Date;     // 更新时间
}
