import { extractDate, formatDateTimeWithWeek, isValidDateString } from '../../utils/date_tools';
import logger from '../../utils/logger';
import gptDeepseek from './gptServerApiDeepseek';
import gptLinAi from './gptServerApiLinkAI';
import gptQWen from './gptServerApiQWen';
import gptDify from './gptServerApiDify';

import { readFileSync } from 'fs';
import { IMealType } from '../../types/health';
import { calculateHash, extractNumber, isValidString } from '../../utils/utils';
import { AIRespCaloriesJson } from '../health/calories/types';
import { genWeightLossDataByRegex } from '../health/healthRegexUtils';
import { IHealthReportSummary } from '../health/healthReportSevices';
import { IWeightLossData } from '../health/weightLoss/weightLossTypes';


import GPTServerComm, { IGPTResponse, ISleepGPTResponse, IWeightLossGPTResponse } from './gptServerComm';

import CacheService from "../../utils/cacheService";
const cache = new CacheService("gpt-cache", 7 * 24 * 60 * 60);// 7天

class GPTServerApi extends GPTServerComm {
  healthPrompt: string;

  // 构造函数
  constructor() {
    super();
    this.healthPrompt = process.env.HEALTH_PROMPT;
    if (!this.healthPrompt) {
      throw new Error('env.HEALTH_PROMPT is not set');
    }
  }

  async callGPT<T>(userid: string, prompt: string, model?: string): Promise<IGPTResponse<T>> {
    // 首先尝试从缓存中获取统计数据
    const cacheKey = calculateHash(prompt);
    const cachedStats = await cache.get<IGPTResponse<T>>(cacheKey);
    if (cachedStats) {
      logger.warn(`从缓存中获取GPT应当结果 ${userid}`);
      return cachedStats;
    }
    // 调用大模型
    const aiName = model || process.env.HEALTH_GPT_MODEL || 'linkai';
    let gptResp: IGPTResponse<T>;
    switch (aiName) {
      case 'qwen':
        gptResp = await gptQWen.qwenQuery(userid, prompt);
        break;
      case 'deepseek':
        gptResp = await gptDeepseek.ReplyText(userid, prompt);
        break;
      case 'dify':
        gptResp = await gptDify.completionQuery(userid, prompt);
        break;
      default:
        gptResp = await gptLinAi.linkaiPostRequest(userid, prompt);
    }
    // 将响应存入缓存
    if (gptResp) {
      await cache.set(cacheKey, gptResp);
    }
    return gptResp;
  }

  // 分析减重消息
  async analysisWeightLoss(userid: string, inputStr: string, isCallGPT: boolean = false): Promise<IWeightLossGPTResponse> {
    if (isCallGPT) {
      logger.warn("=====>调用gpt 分析减重");
      return this.callGPTWeightLoss(userid, inputStr);
    }

    return this.analysisWeightLossByRegex(inputStr);
  }

  // 使用正则表达式提取json
  async analysisWeightLossByRegex(inputStr: string): Promise<IWeightLossGPTResponse> {
    const { jsonStr, json: jsonData, weightLossData, isSuccess } = await genWeightLossDataByRegex(inputStr);
    if (!isSuccess) {
      logger.error('analysisWeightLossByRegex: 正则表达式提取json失败:', inputStr);
      return null;
    }
    const targetWeight = extractNumber('目标体重', inputStr);
    const date = extractDate(inputStr);
    logger.info('analysisWeightLossByRegex:', targetWeight);
    return {
      model: 'Regex',
      date,
      bloodSugar: this.searchValueByKey(jsonData, ['血糖', '血糖值']),
      targetWeight: this.searchValueByKey(jsonData, ['目标', '目标体重']) || targetWeight,
      aiResponse: weightLossData,
      aiJsonStr: jsonStr,
      aiJson: jsonData,
      aiTokens: 0,
      completionTokens: 0,
      promptTokens: 0
    };
  }

  // 调用大模型,分析减重信息
  async callGPTWeightLoss(userid: string, inputStr: string): Promise<IWeightLossGPTResponse> {
    // 使用正则表达式和 replace() 方法删除空行
    const content: string = inputStr.replace(/^\s*[\r\n]/gm, '');
    logger.warn('====>开始调用大模型从文本获取减重信息:', inputStr, content);
    if (!isValidString(content) || content.length < 8) {
      logger.error('callGPTWeightLoss: 输入内容不合法');
      return null;
    }

    const prompt = this.healthPrompt.replace('{content}', content);
    if (!isValidString(prompt)) throw new Error('HealthPrompt is not set');

    let jsonData;
    const gptResp = await this.callGPT(userid, prompt);
    try {
      const jsonText = this.extractJson(gptResp.showText);
      logger.info('extractJson:');
      if (jsonText) {
        jsonData = JSON.parse(jsonText);
      } else {
        jsonData = JSON.parse(gptResp.showText);
      }
      logger.info(JSON.stringify(jsonData, null, 2));
    } catch (e) {
      logger.error('gpt PostRequest error:', e);
    }
    let date = extractDate(content);
    // 获取文本中的目标体重
    const targetWeight = extractNumber('目标体重', content);
    const { model, aiTokens, completionTokens, promptTokens } = gptResp;

    if (!date) date = isValidDateString(this.searchValueStrByKey(jsonData, ['date', '日期'])) ?? new Date();

    const weightLossData: IWeightLossData = {
      weight: this.searchValueByKey(jsonData, ['体重']),
      water: this.searchValueByKey(jsonData, ['喝水', '饮水']),
      sleep: this.searchValueByKey(jsonData, ['睡眠']),
      aerobic: this.searchValueByKey(jsonData, ['有氧', '有氧运动']),
      anaerobic: this.searchValueByKey(jsonData, ['无氧', '无氧运动', '阻抗', '阻抗运动']),
      flexibility: this.searchValueByKey(jsonData, ['柔韧性', '柔韧性运行', '弹性', '弹性运动']),
      model,
    }
    return {
      model,
      date,
      bloodSugar: this.searchValueByKey(jsonData, ['血糖', '血糖值']),
      targetWeight: this.searchValueByKey(jsonData, ['目标', '目标体重']) || targetWeight,
      aiResponse: weightLossData,
      aiJsonStr: gptResp.showText,
      aiJson: jsonData,
      aiTokens,
      completionTokens,
      promptTokens,
    };
  }

  async callGPTSleepInfo(userid: string, inputStr: string): Promise<ISleepGPTResponse> {
    // 使用正则表达式和 replace() 方法删除空行
    const content: string = inputStr.replace(/^\s*[\r\n]/gm, '');
    logger.warn('====>开始调用大模型从文本获取减重信息:', inputStr, content);
    if (!isValidString(content) || content.length < 8) {
      logger.error('callGPTSleepInfo: 输入内容不合法');
      return null;
    }

    const prompt = process.env.HEALTH_SLEEP_PROMPT.replace('{content}', content);
    if (!isValidString(prompt)) throw new Error('HealthPrompt is not set');

    let jsonData;
    const gptResp = await this.callGPT(userid, prompt);
    try {
      // 调用大模型,分析减重 ------------------------

      const jsonString = gptResp.showText;
      logger.info('调用大模型返回:', process.env.HEALTH_GPT_MODEL, jsonString);

      // 使用 eval 计算数学运算公式的值
      const evaluatedJsonString = jsonString.replace(/"value":\s*(.*?)\s*,/, (match, p1) => {
        const evaluatedValue = eval(p1);
        return `"value": ${evaluatedValue},`;
      });
      const jsonText = this.extractJson(evaluatedJsonString);
      logger.info('extractJson:');
      if (jsonText) {
        jsonData = JSON.parse(jsonText);
      } else {
        jsonData = JSON.parse(evaluatedJsonString);
      }
      logger.info(JSON.stringify(jsonData, null, 2));
    } catch (e) {
      logger.error('linkaiPostRequest error:', e);
    }
    let date = extractDate(content);
    const { model, aiTokens, completionTokens, promptTokens } = gptResp;

    if (!date) date = isValidDateString(this.searchValueStrByKey(jsonData, ['date', '日期'])) ?? new Date();
    return {
      model,
      date,
      bloodSugar: this.searchValueByKey(jsonData, ['血糖', '血糖值']),
      weight: this.searchValueByKey(jsonData, ['体重']),
      totalSleep: this.searchValueByKey(jsonData, ['总睡眠']),
      nightSleep: this.searchValueByKey(jsonData, ['夜间']),
      lightSleep: this.searchValueByKey(jsonData, ['浅睡']),
      deepSleep: this.searchValueByKey(jsonData, ['深睡']),
      shortSleep: this.searchValueByKey(jsonData, ['零星']),
      fastEye: this.searchValueByKey(jsonData, ['快速']),

      aiResponse: gptResp.showText,
      aiTokens,
      completionTokens,
      promptTokens,
    };
  }

  async getUserSummary(message: string) {
    return gptQWen.getUserSummary(message);
  }

  async makeSleepSummary(userid: string, message: string): Promise<IGPTResponse> {
    const prompt =
      process.env.HEALTH_SLEEP_SUMMARY_PROMPT.replace('{content}', message) ||
      `'${message}'\n根据上述的睡眠记录信息,总结睡眠情况`;
    try {
      const gptResp = await this.callGPT(userid, prompt);
      return gptResp;
    } catch (e) {
      logger.error('使用AI生成睡眠总结意外:', e);
    }
    return null;
  }


  healthReportSummaryTest = `{
      "summary": "您的体检报告显示存在高血压、超重、心动过速、甘油三酯升高等问题，同时存在阴虚质体质。建议您采取综合措施控制体重、降低血压和血脂，并注意情绪调节。",
      "recommendations": [
          "每日步行6000步以上，结合低强度有氧运动如慢跑或骑自行车",
          "采用低盐、低脂、低糖饮食，多吃蔬菜、水果和全谷物",
          "避免高胆固醇食物，如动物内脏、肥肉等",
          "保持规律作息，每天保证7-8小时睡眠",
          "定期监测血压和血脂，每3个月复查一次",
          "遵医嘱服用降压药物，避免自行停pul药"
      ],
      "otherHealthIndicators": [
          "体质指数 (BMI): 25.76，属于超重",
          "心率: 未明确提及，但心动过速需关注",
          "血氧饱和度: 未提及，但一般情况下正常"
      ]
    }`;

  async makeHealthReport(userOID: string, message: string, test = false): Promise<IGPTResponse<IHealthReportSummary>> {
    if (test) {
      return {
        model: 'test', showText: 'Test response', aiTokens: 0,
        completionTokens: 0, promptTokens: 0,
        json: this.parseJson(this.healthReportSummaryTest)
      }
    }
    const prompt = getHealthCheckPrompt(message);
    try {

      const gptResp = await this.callGPT<IHealthReportSummary>(userOID, prompt);

      return gptResp;
    } catch (e) {
      logger.error('使用AI生成体检分析报告意外:', e);
    }
    return null;
  }

  /**
   * 根据用户减重记录,得出用户最近打卡简报
   * @param userOID 
   * @param data 
   * @returns 
   */
  async makeWeightLossRecordSummary(userOID: string, data: any) {
    const prompt = `
    你是一名健身与健康分析专家。分析提供的减重打卡数据，按指定报告类型生成简洁的纯文字总结（200字以内）。数据包含用户信息、体重、运动（有氧、力量、柔韧性）、睡眠、饮水量和目标。步骤如下：  
    确定报告类型：根据chartType判断（"周报"、"两周报"、"月报"或"最近N天"）。  
    分析进展：评估体重变化趋势、运动模式（有氧、力量、柔韧性）、睡眠和饮水情况，与目标的契合度。  
    生成总结：  
    简述报告类型和时间范围。  
    描述体重进展：是否持续下降、速度快慢。  
    概述运动：是否规律、类型是否均衡。  
    评论睡眠：是否充足或数据缺失。  
    提供洞察：进展是否符合目标、主要优势、改进建议。

    格式要求：纯文字叙述，不列具体数值（如体重、分钟数、百分比），重点突出趋势和建议，字数控制在200字以内。

    输出示例（两周报）
    两周报总结：用户在过去两周内表现出色，体重呈现显著下降趋势，减重速度超出预期目标。运动方面，有氧活动保持稳定且规律，但缺乏力量和柔韧性训练，建议增加多样性以提升效果。睡眠数据缺失，可能影响恢复，推荐每日记录以监控质量。总体来看，减重进展令人满意，目标达成情况良好。继续保持打卡习惯，同时平衡运动类型，关注睡眠改善，将有助于长期成功。
    用户的打卡内容如下:
    ${JSON.stringify(data)}
    `
    const gptResp = await this.callGPT<string>(userOID, prompt, "dify");
    return gptResp;
  }

  // 通过ai提供餐食卡路里打卡鼓励及汇总信息
  async makeMealCheckInSummary({ userid, mealType, message, baseText, recordsText, calPlanText }:
    { userid: string, mealType: IMealType, message: string, baseText?: string, recordsText?: string, calPlanText?: string })
    : Promise<IGPTResponse<AIRespCaloriesJson>> {
    let prompt = getMealCheckInPrompt() ||
      `1. 根据如下的餐食记录信息,从卡路里摄入角度进行分析,结果存入:calories,为减重提供鼓励及建议并存入:suggestion;
      2. 如果信息中包含食物量,那么估算食物的卡路里总热值,说明内容加入:summary,数值存入:caloriesTotal;
      3. 为食物的营养成分做一个分析,得到结果存入nutrition;
      4. 从知识库中获取食物的卡路里和营养成分;
      5. 如果用户未填写食物量,温柔的提醒用户下次打卡时填写食物数量,已填写则不提醒和说明;
      6. 不要让设定的目标及内容未改动的出现在应答文本中;
      7. 其他信息存入:other;
      8. 所有文字信息同时汇总到"text"字段中,包括:食物卡路里组成,营养成分,总卡路里数值及建议,并鼓励用户保持规律打卡.
      9. 请返回一个 JSON 对象，格式必须严格遵循以下结构:
        {
          "suggestion": "string (为减重提供鼓励及建议)",
          "other": "string (其他未尽信息)",
          "summary": "string (简短总结)",
          "text:: "string (所有信息汇总)",
          "calories": [
            {"name": "string (食物名称)", "value": number (卡路里数值),"unit": "string (卡路里单位)"},            
          ],
          "totalCalories": number (今日摄入总卡路里数值)
          "nutrition": [
            {"name": "string (营养素名称)", "value": number (营养素数值),"unit": "string (营养素单位)"}
          ]
        }
      10. 请确保相同的输入总是得到相同的输出。
      餐食记录信息: {content}`;

    prompt = prompt.replace('{content}', message)

    if (baseText) {
      // prompt += `\n- 用户信息: ${baseText}`;
    }
    if (recordsText) {
      //  prompt += `\n- 今天的相关记录: ${recordsText}`;
    }
    if (calPlanText) {
      // prompt += `\n-为用户设定的卡路里方案: ${calPlanText}`;
    }
    try {
      const gptResp = await this.callGPT<AIRespCaloriesJson>(userid, prompt);

      if (gptResp?.showText) {
        const jsonText = this.extractJson(gptResp.showText);
        logger.info('extractJson:', jsonText);
        if (jsonText) {
          gptResp.json = JSON.parse(jsonText);
        } else {
          gptResp.json = JSON.parse(gptResp.showText);
        }
        // 打卡日期
        const activityDate = extractDate(message) ?? new Date()
        const dateStr = formatDateTimeWithWeek(new Date(activityDate))
        gptResp.showText = `❤️ 已成功记录【${mealType}打卡】\n📅 ${dateStr}\n`
        const icons = ["1️⃣", "2️⃣", "3️⃣", "4️⃣", "5️⃣", "6️⃣", "7️⃣", "8️⃣", "9️⃣"]
        let index = 0;
        if (gptResp.json?.summary)
          gptResp.showText += `${icons[index++]} ${gptResp.json?.summary}\n`;
        if (gptResp.json?.other)
          gptResp.showText += `${icons[index++]} ${gptResp.json?.other}\n`;
        if (gptResp.json?.suggestion)
          gptResp.showText += `${icons[index++]} ${gptResp.json?.suggestion}\n`;
        if (gptResp.json?.totalCalories)
          gptResp.showText += `${icons[index++]} 本次打卡卡路里约:${gptResp.json?.totalCalories}千卡`;
        // gptResp.showText += `${icons[index++]} 本次打卡卡路里约:${gptResp.json?.currentCheckInCalories}千卡`;
        // if (gptResp.json?.reference)
        //   gptResp.showText += `\n\n🔗知识库:${gptResp.json?.reference}`;
      }
      return gptResp;
    } catch (e) {
      logger.error('gpt PostRequest error:', e);
    }
    return null;
  }
}

const gptServerApi = new GPTServerApi();
export default gptServerApi;

const getMealCheckInPrompt = () => {
  const file = process.env.HEALTH_PROMPT_MEAL_CHECKIN_FILE
  if (file) {
    return readFileSync(file, 'utf8')
  }
  const prompt = process.env.HEALTH_PROMPT_MEAL_CHECKIN_PROMPT
  if (prompt) return prompt

  return undefined
}

// 体检报告提示词
const getHealthCheckPrompt = (message: string) => {
  let promptStr = `你是一位专业的健康数据分析师，擅长解读体检报告。请基于用户提供的体检报告数据，完成以下任务：
    1. **数据解读**：分析报告中的各项指标（如血压、血糖、血脂、肝功能、肾功能等），说明每项指标的含义、正常范围，以及用户的结果是否正常。
    2. **异常分析**：如果存在异常指标，说明可能的健康风险或原因，并提供初步建议（但避免给出明确的医疗诊断）。
    3. **健康建议**：根据报告结果，提出个性化的健康管理建议，包括饮食、运动、生活方式等。
    4. **清晰表达**：用简洁、通俗的语言输出结果，避免过多医学术语，确保用户易于理解。
    请根据用户提供的体检报告数据进行分析，并确保输出内容专业、准确且易懂。如果数据不完整，请说明需要补充哪些信息以便更准确分析。
    报告数据如下:
    {content}
    `

  const fileName = process.env.HEALTH_PROMPT_CHECK_FILE || "health_check_prompt.txt"
  if (fileName) {
    promptStr = readFileSync(fileName, 'utf8')
  } else {
    logger.error(`体检报告提示词文件不存在 ${fileName}`)
  }

  return promptStr.replace("{content}", message)
}


