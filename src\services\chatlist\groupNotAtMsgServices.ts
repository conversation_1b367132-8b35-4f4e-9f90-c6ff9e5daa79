/**
 * 微信群聊消息服务
 * 主要功能：
 * 1. 管理和处理微信群聊消息
 * 2. 提供群聊消息的CRUD操作
 * 3. 支持AI内容生成和分析
 * 4. 处理群成员打卡信息
 */

import { ParseDB, ParseRef } from "../../database/database";
import logger from "../../utils/logger";
import { calculateHash, isEthAddress, isParseObjectID, isValidString, sleep } from "../../utils/utils";
import { BaseServices } from "../baseServices";

import { CallbackListResult, INotAtMsg, INotAtMsg_INPUT } from "./groupNotAtMsgTypes";

import { EthAddress, GResponseList } from "../../types/types";
import ApiError from "../../utils/api_error";
import DataError from "../../utils/data_error";
import { isValidTimeRange } from "../../utils/date_tools";
import gpt from '../gpt/gptServerApi';
import { isNeedReplyGroup, processHealthCheckInThread } from "../health/healthStatisticsNoAI";
import userNoticeServices from '../notifications/userNotificationServices';
import { isMyWathingUser } from "../users/watchingServices";
import itchatGroup from '../wechat/cowGroupClass';
import { addOrUpdateItchatGroup, getItchatGroupInfo } from "../wechat/cowGroupFunction";
import { addOrUpdateWxUser, getItchatUserInfo, parseRemarkNameString } from '../wechat/cowUserFunctions';
import { WxGroupInfo, WxUserInfo } from "../wechat/types";
import { addDoneTask, createNotAtMsgQuery, cronChatAIGenContent, getKeywordsStatisticsGroupRange, getRecordJson } from "./groupNotAtMsgFunctions";


class GroupNotAtMsgServices extends BaseServices {
    // ================ 群成员消息管理 ================

    /** 获取群成员消息列表(经过群关键字过滤生成的结果) */
    getWxGroupMemberMsgList = async (
        body: {
            agent: string;
            groupObjectId: string;
            userObjectId: string;
            pageNum: number;
            pageSize: number;
            range: [number, number];
        },
        keywords: Array<string> = [],
    ): Promise<GResponseList<INotAtMsg>> => {
        const { agent, groupObjectId, userObjectId, pageNum, pageSize, range } = body;
        const start = Number(pageNum ?? 1);
        const query = createNotAtMsgQuery(agent);
        query.descending('createdAt');

        if (isValidTimeRange(range)) {
            query.greaterThanOrEqualTo('createdAt', new Date(range[0]));
            query.lessThanOrEqualTo('createdAt', new Date(range[1]));
        }

        query.exists('refItchatGroup');
        const refItchatGroup: ParseRef = {
            __type: 'Pointer',
            className: 'AIItchatGroups',
            objectId: groupObjectId,
        };
        query.equalTo('refItchatGroup', refItchatGroup);

        query.exists('refItchatUser');
        const user: ParseRef = {
            __type: 'Pointer',
            className: 'AIItchatUsers',
            objectId: userObjectId,
        };
        query.equalTo('refItchatUser', user);

        if (keywords?.length) {
            const regex = new RegExp(keywords.join('|'), 'i');
            query.matches('content', regex);
        }

        const total = await query.count();
        query.limit(pageSize);
        query.skip((start - 1) * pageSize);

        const results = await query.find();
        const data = results.map(getRecordJson);
        return this.makeGResponseList(data, pageNum, pageSize, total);
    };

    /** 根据要求立即重新生成AI分析结构 */
    genAiContengOfGroupMember = async (body: { agent: string; objectId: string; groupObjectId: string }) => {
        const { agent, objectId, groupObjectId } = body;
        const query = createNotAtMsgQuery(agent);
        query.equalTo('objectId', objectId);
        const result = await query.first();

        if (result) {
            const group = await itchatGroup.getWxGroupInfoByObjectId(groupObjectId);
            const { aiJsonStr, aiResponse, aiJson } = await gpt.analysisWeightLoss(groupObjectId, result.get('content'));//await genAiContentByGPT(group.aiKeywords, result.get('content'));
            if (!aiResponse) {
                logger.error('genAiContengOfGroupMember: genAiContent error: ', agent, objectId, groupObjectId, aiJsonStr);
                throw new ApiError('genAiContent error: ' + aiJsonStr);
            }
            result.set('aiGenContent', aiJsonStr);
            result.set('aiResponse', aiResponse);
            await result.save();
        }
        return result;
    };

    /** 更新微信群成员的打卡信息 */
    updateWxGroupMemberMsg = async (body: { agent: string; objectId: string; aiGenContentJson: any }) => {
        const { agent, objectId, aiGenContentJson } = body;
        const query = createNotAtMsgQuery(agent);
        query.equalTo('objectId', objectId);
        const result = await query.first();

        if (result) {
            result.set('aiGenContentJson', aiGenContentJson);
            await result.save();
        }
        return result;
    };

    /** 删除微信群成员消息 */
    delWxGroupMemberMsg = async (body: { agent: EthAddress; objectId: string }) => {
        const { agent, objectId } = body;
        const query = createNotAtMsgQuery(agent);
        query.equalTo('objectId', objectId);
        const result = await query.first();

        if (result) {
            await result.destroy();
            logger.warn(`delWxGroupMemberMsg: ${objectId}`);
        }
        return result;
    };

    // ================ 群聊消息管理 ================

    /** 获取群聊消息列表 */
    getChatListOfGroup = async (
        agent: EthAddress,
        groupObjectId: string,
        pageNum: number,
        pageSize: number,
        keywords: Array<string> = [],
    ) => {
        if (!isEthAddress(agent)) throw new ApiError('agent is not a valid address');

        const groupPointer = {
            __type: 'Pointer',
            className: 'AIItchatGroups',
            objectId: groupObjectId,
        };
        const query = createNotAtMsgQuery(agent);
        query.descending('createdAt');
        query.equalTo('refItchatGroup', groupPointer);

        if (keywords?.length) {
            const regex = new RegExp(keywords.join('|'), 'i');
            query.matches('content', regex);
        }

        const total = await query.count();
        query.skip(this.getSkipNumber(total, pageNum, pageSize));
        query.limit(pageSize);

        const results = (await query.find())?.map(getRecordJson);

        return { results, total };
    };

    /** 处理微信用户信息 */
    private async processWxUserInfo(agent: EthAddress, user: WxUserInfo): Promise<{ userObjectId: string; wxUser: WxUserInfo }> {
        let userObjectId = user?.objectId;
        let wxUser: WxUserInfo = user;
        wxUser = await addOrUpdateWxUser(agent, user);
        if (!wxUser) {
            logger.error('processWxUserInfo: 找不到用户ID: ', agent, user);
            throw new ApiError('无效的用户信息');
        }
        userObjectId = wxUser?.objectId;

        return { userObjectId, wxUser };
    }

    /** 处理微信群信息 */
    private async processWxGroupInfo(agent: EthAddress, group: any): Promise<{ groupObjectId: string; wxGroup: WxGroupInfo }> {
        let groupObjectId = group.objectId;
        let wxGroupIn: WxGroupInfo = {
            agent,
            objectId: group.objectId,
            groupNickName: group.groupNickName || group.NickName,
            groupUserName: group.groupUserName || group.UserName,
            groupHeadImgUrl: group.groupHeadImgUrl || group.HeadImgUrl,
            MemberList: group.MemberList
        };
        let wxGroup = await getItchatGroupInfo(agent, wxGroupIn, true);
        if (!wxGroup) wxGroup = await addOrUpdateItchatGroup(agent, wxGroupIn);
        if (!wxGroup) {
            logger.error('processWxGroupInfo: 找不到微信群ID: ', agent, group);
            throw new ApiError('无效的群组信息');
        }
        groupObjectId = wxGroup?.objectId;

        return { groupObjectId, wxGroup };
    }

    /** 保存群聊消息记录 */
    private async saveGroupChatRecord(params: {
        agent: EthAddress; account: EthAddress; userObjectId: string; groupObjectId: string; wxUser: WxUserInfo; wxGroup: WxGroupInfo;
        content: string; time: number; msgid: number; source: string; type: string; is_at: boolean; is_group: boolean; thumb: string; extra: string;
        system_name: string;
    }): Promise<{ recordId: string; tableName: string; inData: INotAtMsg }> {
        const {
            agent, account, userObjectId, groupObjectId, wxUser, wxGroup,
            content, time, msgid, source, type, is_at, is_group, thumb, extra, system_name
        } = params;

        const tableName = `AIGroupNotAtMsg_${agent}`;
        const chat = new ParseDB.Object(tableName);

        const refItchatUser: ParseRef = { __type: 'Pointer', className: 'AIItchatUsers', objectId: userObjectId, };
        const refItchatGroup: ParseRef = { __type: 'Pointer', className: 'AIItchatGroups', objectId: groupObjectId, };

        const inData: INotAtMsg = {
            agent, account, userName: wxUser?.NickName, groupName: wxGroup?.groupNickName, refItchatUser, refItchatGroup,
            content, time: Number(time), msgid: Number(msgid), source, type, is_at, is_group, thumb, extra, system_name,
            aiGenContent: '', aiGenContentJson: [], doneTasks: []
        };
        chat.set("agent", agent);
        chat.set(inData)
        const result = await chat.save();
        return {
            recordId: result?.id,
            tableName,
            inData: {
                ...inData, objectId: result?.id,
                createdAt: result?.createdAt?.toISOString(),
                updatedAt: result?.updatedAt?.toISOString()
            }
        };
    }

    /** 保存用户私聊消息记录 */
    private async saveUserChatRecord(params: {
        agent: EthAddress; account: EthAddress; userObjectId: string; wxUser: WxUserInfo;
        content: string; time: number; msgid: number; source: string; type: string; thumb: string; extra: string;
        to_user_id: string, to_user_nickname: string, system_name: string;
    }): Promise<{ recordId: string; tableName: string; inData: any }> {
        const {
            agent, account, userObjectId, wxUser, to_user_id, to_user_nickname,
            content, time, msgid, source, type, thumb, extra, system_name
        } = params;

        const tableName = `AIUserNotAtMsg_${agent}`;
        const chat = new ParseDB.Object(tableName);

        const refItchatUser: ParseRef = { __type: 'Pointer', className: 'AIItchatUsers', objectId: userObjectId, };

        const inData = {
            account, userName: wxUser?.NickName, refItchatUser, groupName: to_user_nickname,
            content, time: Number(time), msgid: Number(msgid), source, type, thumb, extra, systemName: system_name,
            is_at: false, is_group: false, aiGenContent: '', aiGenContentJson: [], doneTasks: []
        };
        chat.set("agent", agent);
        chat.set(inData)
        const result = await chat.save();
        return {
            recordId: result?.id,
            tableName,
            inData: {
                ...inData, objectId: result?.id,
                createdAt: result?.createdAt?.toISOString(),
                updatedAt: result?.updatedAt?.toISOString()
            }
        };
    }

    /**
     * 插入群聊非AT消息到数据库
     *
     * 主要功能：
     * 1. 处理和更新微信用户信息
     * 2. 处理和更新微信群信息
     * 3. 处理新成员加入群组的欢迎消息
     * 4. 保存群聊消息记录
     * 5. 处理消息回调
     *    - 处理打卡记录
     *    - 其他回调可在此处添加
     *
     * @param {Object} params - 输入参数对象
     * @param {EthAddress} params.account - 用户的以太坊地址
     * @param {INotAtMsg_INPUT} params.body - 消息内容对象
     * @param {EthAddress} params.body.agent - 代理地址
     * @param {WxUserInfo} params.body.user - 微信用户信息
     * @param {WxGroupInfo} params.body.group - 微信群信息
     * @param {string} params.body.content - 消息内容
     * @param {number} params.body.time - 消息时间戳
     * @param {number} params.body.msgid - 消息ID
     * @param {string} params.body.source - 消息来源
     * @param {string} params.body.type - 消息类型，如果是 'JOIN_GROUP' 则发送欢迎消息
     * @param {boolean} params.body.is_at - 是否是@消息
     * @param {boolean} params.body.is_group - 是否是群消息
     * @param {string} params.body.thumb - 缩略图
     * @param {string} params.body.extra - 额外信息
     * @param {string} params.body.system_name - 系统名称
     *
     * @returns {Promise<string|undefined>} 返回保存的消息ID，如果处理失败则返回 undefined
     *
     * @throws {ApiError} 当用户信息或群组信息无效时抛出错误
     */
    insertGroupNotAtMsg = async ({ account, body }: { account: EthAddress; body: INotAtMsg_INPUT; })
        : Promise<{ recordId: string; results: CallbackListResult[] } | undefined> => {
        const { agent, user, group, content, time, msgid, source, type, is_at, is_group, thumb, extra, system_name } = body;
        try {
            // 1. 获取微信用户信息
            const { userObjectId, wxUser } = await this.processWxUserInfo(agent, user);
            // 2. 获取微信群信息
            const { groupObjectId, wxGroup } = await this.processWxGroupInfo(agent, group);
            // 3. 处理新加入群组的欢迎消息
            if (type === 'JOIN_GROUP') {
                // 2025-01-22 暂时停用,会和 cow 端"减重须知"关键子触发重复,导致误导用户
                // await healthServices.sendJoinGroupMsgList(wxGroup);
                logger.info(`insertGroupNotAtMsg: 新加入群组的欢迎消息: ${wxGroup.groupNickName}`)
                logger.warn(`==>暂时注释进群欢迎词`)
            }
            // 4. 保存聊天记录
            const { recordId, tableName, inData } = await this.saveGroupChatRecord({
                agent, account, userObjectId, groupObjectId, wxUser, wxGroup, content,
                time, msgid, source, type, is_at, is_group, thumb, extra, system_name
            });
            // 5. 处理消息回调
            // 5.1 处理打卡记录回调
            // 其他回调处理可以在函数里添加
            const results = await this.callbackList({
                userWxid: wxUser?.wxid, groupWxid: wxGroup?.groupUserName,
                tableName,
                inData,
                isRealTime: true, test: source === 'test' ? true : false
            });
            // 6.回写DoneTasks 到数据库
            if (results?.[0]?.result?.doneTasks) {
                const query = new ParseDB.Query(tableName);
                query.equalTo('objectId', recordId);
                const record = await query.first();
                if (record) {
                    // 如果 doneTasks 是数组，遍历每个元素并添加
                    if (Array.isArray(results[0].result.doneTasks)) {
                        results[0].result.doneTasks.forEach((task: string) => {
                            record.set('doneTasks', addDoneTask(record.get('doneTasks'), task));
                        });
                    } else {
                        // 如果是单个值，直接添加
                        record.set('doneTasks', addDoneTask(record.get('doneTasks'), results[0].result.doneTasks));
                    }
                    await record.save();
                }
            }
            return { recordId, results };

        } catch (error) {
            logger.error('insertGroupNotAtMsg error: ', error, user, group, content);
            return undefined;
        }
    };

    /** 插入用户私聊消息 */
    insertUserNotAtMsg = async ({ account, body }: { account: EthAddress; body: any }) => {
        const { agent, user, content, time, msgid, source, type, thumb, extra, system_name: systemName, to_user_id, to_user_nickname } = body;

        let itUser: WxUserInfo = user;
        let { objectId: userObjectId } = parseRemarkNameString(user.RemarkName);
        if (!isParseObjectID(userObjectId)) {
            itUser = await getItchatUserInfo(agent, user);
            if (itUser) userObjectId = itUser.objectId;
            else {
                if (isValidString(user?.NickName) && isValidString(user?.UserName)) {
                    const u = await addOrUpdateWxUser(agent, user);
                    userObjectId = u?.objectId;
                }
                logger.error('insertGroupNotAtMsg: 找不到微信用户: ', agent, user);
            }
        }

        // const ChatRecordDB = ParseDB.Object.extend(`AIUserNotAtMsg_${agent}`);
        // const chat = new ChatRecordDB();

        // const refItchatUser: ParseRef = {
        //     __type: 'Pointer',
        //     className: 'AIItchatUsers',
        //     objectId: userObjectId,
        // };
        // chat.set({
        //     agent, account, userName: user?.NickName || itUser?.NickName, refItchatUser,
        //     content, time, msgid, source, type, thumb, extra, systemName,
        // });

        // const result = await chat.save();

        // 1. 获取微信用户信息
        const { userObjectId: userOID, wxUser } = await this.processWxUserInfo(agent, user);
        if (userOID) userObjectId = userOID

        // 4. 保存聊天记录
        const { recordId, tableName, inData } = await this.saveUserChatRecord({
            agent, account, userObjectId, wxUser, content, to_user_id, to_user_nickname,
            time, msgid, source, type, thumb, extra, system_name: systemName
        });

        // 5. 处理消息回调
        // 5.1 处理打卡记录回调
        // 其他回调处理可以在函数里添加
        const results = await this.callbackList({
            userWxid: wxUser?.wxid, groupWxid: to_user_id, // 私聊消息没有群组
            tableName,
            inData: { ...inData, agent, to_user_id, to_user_nickname },
            isRealTime: true, test: source === 'test' ? true : false
        });
        // 6.回写DoneTasks 到数据库
        if (results?.[0]?.result?.doneTasks) {
            const query = new ParseDB.Query(tableName);
            query.equalTo('objectId', recordId);
            const record = await query.first();
            if (record) {
                // 如果 doneTasks 是数组，遍历每个元素并添加
                if (Array.isArray(results[0].result.doneTasks)) {
                    results[0].result.doneTasks.forEach((task: string) => {
                        record.set('doneTasks', addDoneTask(record.get('doneTasks'), task));
                    });
                } else {
                    // 如果是单个值，直接添加
                    record.set('doneTasks', addDoneTask(record.get('doneTasks'), results[0].result.doneTasks));
                }
                await record.save();
            }
        }

        return { recordId, results };
    };

    // ================ 服务接口 ================

    /** 查询微信群成员的打卡信息 */
    servicesGetWxGroupMemberMsgList = async (params: {
        groupObjectId: string;
        userObjectId: string;
        pageNum: number;
        pageSize: number;
        sessionId: string;
        range: string;
    }) => {
        const { groupObjectId, userObjectId, pageNum, pageSize, sessionId, range } = params;
        if (!groupObjectId) return null;
        if (!userObjectId) return null;
        if (!pageNum || !pageSize) return null;
        const sess = await this.checkSession(sessionId);
        const isHavePermission = await this.checkPermission({ sessionId, permission: 'chatlist' });
        const isSuperMan = await this.isSuperMan(sessionId);

        const group = await itchatGroup.getWxGroupInfoByObjectId(groupObjectId);
        if (!group) throw new ApiError('无效的微信群', 400);

        const keywords: Array<string> = group.aiKeywords;
        if (!isHavePermission && !isSuperMan) {
            const isOwnerOfGroup = await itchatGroup.isGroupOwner2(sess.agent, groupObjectId, sess.account);
            if (!isOwnerOfGroup) throw new ApiError('无权限', 401);

            if (!keywords || !keywords.length) throw new ApiError('群关键字不能为空', 400);
        }

        return this.getWxGroupMemberMsgList(
            {
                agent: sess.agent,
                groupObjectId,
                userObjectId,
                pageNum,
                pageSize,
                range: range.split(',').map(Number) as [number, number],
            },
            keywords,
        );
    };

    /** 更新微信群成员的打卡信息 */
    servicesUpdateWxGroupMemberMsg = async ({
        sessionId,
        body,
    }: {
        sessionId: string;
        body: { agent: string; objectId: string; aiGenContentJson: Object; groupObjectId: string };
    }) => {
        const sess = await this.isSuperOrOwnerGroup(sessionId, body.groupObjectId);
        if (!sess) throw new ApiError('无权限(修正打卡数据)', 401);

        return body.aiGenContentJson
            ? this.updateWxGroupMemberMsg({ agent: sess.agent, ...body })
            : this.genAiContengOfGroupMember({ agent: sess.agent, ...body });
    };

    /** 获取关注用户的消息发送列表 */
    servicesGetWatchingUserMsgList = async ({
        sessionId,
        userObjectId,
        pageNum,
        pageSize,
        range,
        sort,
        groupName,
    }: {
        sessionId: string;
        userObjectId: string;
        pageNum: number;
        pageSize: number;
        range: [number, number];
        sort?: string;
        groupName?: string;
    }): Promise<GResponseList<INotAtMsg>> => {
        const sess = await this.checkSession(sessionId);
        const isMyWatchingUser = await isMyWathingUser({
            agent: sess.agent,
            account: sess.account,
            otherObjectId: userObjectId,
        });

        if (!isMyWatchingUser) throw new ApiError('无权限', 401);

        const refWxUser = {
            __type: 'Pointer',
            className: 'AIItchatUsers',
            objectId: userObjectId,
        };
        const query = new ParseDB.Query(`AIGroupNotAtMsg_${sess.agent}`);
        query.equalTo('refItchatUser', refWxUser);
        query.descending('createdAt');

        if (isValidTimeRange(range)) {
            const [start, end] = [new Date(range[0]), new Date(range[1])];
            query.greaterThanOrEqualTo('createdAt', start);
            query.lessThanOrEqualTo('createdAt', end);
        }

        if (groupName) query.contains('groupName', groupName);

        const total = await query.count();

        query.skip(this.getSkipNumber(total, pageNum, pageSize));
        query.limit(pageSize);

        const result = await query.find();
        if (result) {
            const records = await Promise.all(result.map(item => getRecordJson(item)));
            return this.makeGResponseList(records, pageNum, pageSize, total);
        }

        return this.makeGResponseListError();
    };

    /** 获取关注微信群的消息列表 */
    servicesGetWatchingGroupMsgList = async ({
        sessionId,
        groupObjectId,
        pageNum,
        pageSize,
        range,
    }: {
        sessionId: string;
        groupObjectId: string;
        pageNum: number;
        pageSize: number;
        range: [number, number];
    }): Promise<GResponseList<INotAtMsg>> => {
        const sess = await this.checkSession(sessionId);
        const isMyWatchingUser = await isMyWathingUser({
            agent: sess.agent,
            account: sess.account,
            otherObjectId: groupObjectId,
        });

        if (!isMyWatchingUser) throw new ApiError('无权限', 401);

        const refWxUser = {
            __type: 'Pointer',
            className: 'AIItchatGroups',
            objectId: groupObjectId,
        };
        const query = new ParseDB.Query(`AIGroupNotAtMsg_${sess.agent}`);
        query.equalTo('refItchatGroup', refWxUser);
        query.descending('createdAt');

        if (isValidTimeRange(range)) {
            query.greaterThanOrEqualTo('createdAt', new Date(range[0]));
            query.lessThanOrEqualTo('createdAt', new Date(range[1]));
        }

        const total = await query.count();

        query.skip(this.getSkipNumber(total, pageNum, pageSize));
        query.limit(pageSize);

        const result = await query.find();
        if (result) {
            const records = await Promise.all(result.map(item => getRecordJson(item)));
            return this.makeGResponseList(records, pageNum, pageSize, total);
        }

        return this.makeGResponseListError();
    };

    /** 删除微信群员消息 */
    servicesDelWxGroupMemberMsg = async ({ sessionId, body }: { sessionId: string; body: any }) => {
        const sess = await this.isSuperOrOwnerGroup(sessionId, body.groupObjectId);
        if (!sess) throw new ApiError('无权限(删除记录)', 401);

        return this.delWxGroupMemberMsg({ agent: sess.agent, ...body });
    };

    /** 获取群聊消息列表 */
    serviceGetChatListOfGroup = async ({
        agent,
        sessionId,
        groupObjectId,
        pageNum,
        pageSize,
    }: {
        agent: EthAddress;
        sessionId: string;
        groupObjectId: string;
        pageNum: number;
        pageSize: number;
    }) => {
        if (!isParseObjectID(groupObjectId)) throw new ApiError('无效的微信群', 400);

        const isHavePermission = await this.checkPermission({ sessionId, permission: 'chatlist' });
        const isSuperMan = await this.isSuperMan(sessionId);

        const group = await itchatGroup.getWxGroupInfoByObjectId(groupObjectId);
        if (!group) throw new ApiError('无效的微信群', 400);

        const keywrords: Array<string> = group.aiKeywords;
        if (!isHavePermission && !isSuperMan) {
            const sess = await this.checkSession(sessionId);
            const isGroupMember = await itchatGroup.isGroupMember(sess.agent, groupObjectId, sess.account, sess.wxid);
            const isOwnerOfGroup = await itchatGroup.isGroupOwner2(sess.agent, groupObjectId, sess.account);
            if (!isOwnerOfGroup && !isGroupMember) throw new ApiError('无权限(查看群聊消息)', 401);
            if (!keywrords || !keywrords.length) throw new ApiError('群关键字不能为空', 400);
        }

        const { results, total } = await this.getChatListOfGroup(agent, groupObjectId, pageNum, pageSize, keywrords);
        if (results) {
            const records = await Promise.all(results);
            return this.makeGResponseList(records, pageNum, pageSize, total);
        }
        return this.makeGResponseListError();
    };

    /** 获取群聊消息列表 */
    serviceGetGroupNotAtMsgList = async ({
        sessionId,
        pageNum,
        pageSize,
        search,
    }: {
        sessionId: string;
        pageNum: number;
        pageSize: number;
        search: any;
    }) => {
        const sess = await this.checkSession(sessionId);

        const isSuper = await this.isSuperMan(sessionId);
        if (!isSuper) throw new DataError('无权限(查看微信记录)', 401);

        const { userName, groupName, keyword, account } = search;

        const query = new ParseDB.Query(`AIGroupNotAtMsg_${sess.agent}`);
        query.descending('createdAt');
        if (userName) query.contains('userName', userName);
        if (groupName) query.contains('groupName', groupName);
        if (account) query.equalTo('account', account);
        if (keyword) query.contains('content', keyword);

        const total = await query.count();

        query.skip(this.getSkipNumber(total, pageNum, pageSize));
        query.limit(pageSize);

        logger.info('session getSessionList:', pageNum, pageSize);

        const results = await query.find();
        if (results?.length) {
            const records = await Promise.all(results.map(getRecordJson));
            return this.makeGResponseList(records, pageNum, pageSize, total);
        }
        return this.makeGResponseListError();
    };

    /**
     * 获取群聊关键词统计数据的服务接口
     * 主要功能：
     * 1. 验证用户限（超级管理员或群主）
     * 2. 验证群组是否有效
     * 3. 验证群组是否设置了关键词
     * 4. 调用具体实现获取统计数据
     */
    servicesGetKeywordsStatisticsGroupRnage = async ({
        sessionId,
        groupObjectId,
        search
    }: {
        sessionId: string,
        groupObjectId: string,
        search: any
    }): Promise<GResponseList<INotAtMsg>> => {
        // 1. 会话和权限验证
        const sess = await this.checkSession(sessionId);
        const isSuperMan = await this.isSuperMan(sessionId);

        // 2. 验证群组是否有效
        const group = await itchatGroup.getWxGroupInfoByObjectId(groupObjectId);
        if (!group) throw new ApiError('无效的微信群', 400);

        // 3. 验证群组关键词
        const keywords: Array<string> = group.aiKeywords;
        if (!keywords?.length) throw new ApiError('群关键字为空', 400);

        // 4. 权限验证
        if (!isSuperMan) {
            const isOwnerOfGroup = await itchatGroup.isGroupOwner2(sess.agent, groupObjectId, sess.account);
            if (!isOwnerOfGroup) {
                throw new ApiError('无权限(统计健康打卡)', 401);
            }
        }

        // 5. 获取统计数据
        const range = search?.range.split(',').map(Number) as [number, number];
        const statisticsList = await getKeywordsStatisticsGroupRange(sess.agent, groupObjectId, keywords, range);

        return this.makeGResponseList(statisticsList, 1, statisticsList.length, statisticsList.length);
    };

    // ================ 回调处理 ================

    /** 回调函数,处理'有新消息'通知
     * // TODO: 添加更多回调处理
    */
    callbackList = async ({ tableName, userWxid, groupWxid, inData, isRealTime = false, test = false }: {
        tableName: string, userWxid: string, groupWxid: string, inData: INotAtMsg,
        isRealTime?: boolean,//是否实时消息
        test?: boolean
    }):
        Promise<CallbackListResult[]> => {

        const results: CallbackListResult[] = [];
        const result = await processHealthCheckInThread({ tableName, data: inData, isRealTime, test });
        let sendResult = false;
        if (result) {
            if (isNeedReplyGroup(inData.groupName || inData.source)) {
                const toUser = inData?.source?.startsWith('wechatmp')
                const wxid = toUser ? userWxid : groupWxid

                logger.info(`callbackList: 需要立即答复打卡记录情况的群组: ${inData.groupName}`);
                if (result.showText) {
                    sendResult = await userNoticeServices.sendUserNotification(inData.agent, [wxid], result.showText, toUser ? "user" : 'group', test);
                    if (!sendResult) logger.error(`callbackList: 发送消息失败: ${inData.groupName} ${result.showText}`);
                }
                if (result.showText2) {//还有一条消息要发
                    sleep(1200);
                    sendResult = await userNoticeServices.sendUserNotification(inData.agent, [wxid], result.showText2, toUser ? "user" : 'group', test);
                    if (!sendResult) logger.error(`callbackList: 发送第二条信息失败: ${inData.groupName} ${result.showText2}`);
                }
            }
            results.push({
                result, sendResult, userOID: inData.refItchatUser.objectId, groupOID: inData.refItchatGroup?.objectId, balanceAITokens: 0,
                hash: calculateHash(inData.refItchatUser.objectId + inData.refItchatGroup?.objectId + inData.content)
            });

        };
        return results;
    }

    cronChatAIGenContent = cronChatAIGenContent;
}

export default new GroupNotAtMsgServices();
