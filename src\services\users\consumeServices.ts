// groupx 独立的用户库,微信扫码登录或拥有account的用户才会出现在这个表(Users)中

import { EthAddress } from '../../types/types';
import logger from '../../utils/logger';
import { isValidString } from '../../utils/utils';
import { BaseServices } from '../baseServices';
import { ParseDB } from '../services';
import { addOrUpdateWxUser } from '../wechat/cowUserFunctions';
import { getGroupxUserByItchat, setUserObjectId, updateUserAITokens } from './userFunction';

const TABLE_NAME = 'AI_ConsumeRecord';

// 当用户依托与群组进行消费时，可根据群信息给与一定优惠
const insertAIConsumeRecord = (account: EthAddress, data: any) => {
  const { user, reply_text: replyText, group, total_tokens: totalTokens, completion_tokens: completionTokens, agent, type, source } = data;

  const ConsumeTB = ParseDB.Object.extend(TABLE_NAME);
  const consume = new ConsumeTB();

  consume.set('userObjectId', user?.objectId);
  consume.set('groupObjectId', group?.objectId);
  consume.set('account', account);
  consume.set('total_tokens', totalTokens);
  consume.set('completion_tokens', completionTokens);
  consume.set('reply_text', replyText);
  consume.set('agent', agent);
  consume.set('type', type);
  consume.set('source', source);
  return consume.save();
};

const consumeAiTokens = async ({
  account,
  data,
}: {
  account: EthAddress;
  data: any;
}): Promise<{
  type: string;
  success: boolean;
  totalAITokens: number;
  balanceAITokens: number;
  account: EthAddress;
  userObjectId: string;
  errorType?: string;
}> => {
  const { type, total_tokens: totalTokens, user: inWxUser, agent, exe, source } = data;
  try {
    // 保存记录
    insertAIConsumeRecord(account, data);
    // 修改余额
    const wxUsr = await addOrUpdateWxUser(agent, inWxUser, true);
    const user = await getGroupxUserByItchat(wxUsr);

    const userObjectId = user?.userObjectId || wxUsr.objectId || inWxUser.objectId;
    if (user && isValidString(userObjectId)) {
      if (!user.userObjectId) setUserObjectId(user.objectId, userObjectId, wxUsr.NickName, true)
      const { totalAITokens, balanceAITokens, usedAITokens } = user;
      let balance = balanceAITokens;
      let used = usedAITokens;
      // 只计算，不运行
      if (exe !== false) {
        balance = balanceAITokens - totalTokens;
        used = usedAITokens + totalTokens;
        updateUserAITokens(userObjectId, {
          balanceAITokens: balance,
          usedAITokens: used,
        });
      }
      return {
        type,
        success: balance > Number(process.env.FLOOR_AI_TOKENS || -3000),
        totalAITokens,
        balanceAITokens: balance,
        userObjectId,
        account,
      };
    }
    logger.warn('[consumeAiTokens] 消费积分时未找到对应的用户:', data);
    return {
      type,
      success: false,
      account,
      errorType: 'user_not_exist',
      totalAITokens: 0,
      balanceAITokens: 0,
      userObjectId: '',
    };

  } catch (e) {
    logger.error('consumeAiTokens error: ', e);
  }

  throw new Error('consumeAiTokens error');
};

class ConsumeServices extends BaseServices {


  serviceConsumeAiTokens = async ({ account, data }: { account: EthAddress; data: any }) => {
    logger.info('==>serviceConsumeAiTokens body: ', account, data);
    return consumeAiTokens({ account, data });
  };


}

export default new ConsumeServices();

