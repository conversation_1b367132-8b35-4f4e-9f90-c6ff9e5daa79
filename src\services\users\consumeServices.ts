// groupx 独立的用户库,微信扫码登录或拥有account的用户才会出现在这个表(Users)中

import { EthAddress } from '../../types/types';
import logger from '../../utils/logger';
import { isValidString } from '../../utils/utils';
import { BaseServices } from '../baseServices';
import { ParseDB } from '../services';
import { addOrUpdateWxUser } from '../wechat/cowUserFunctions';
import { getGroupxUserByItchat, setUserObjectId, updateUserAITokens } from './userFunction';

const TABLE_NAME = 'AI_ConsumeRecord';

// 当用户依托与群组进行消费时，可根据群信息给与一定优惠
const insertAIConsumeRecord = (account: EthAddress, data: any) => {
  const { user, reply_text: replyText, group, total_tokens: totalTokens, completion_tokens: completionTokens, agent, type, source } = data;

  const ConsumeTB = ParseDB.Object.extend(TABLE_NAME);
  const consume = new ConsumeTB();

  consume.set('userObjectId', user?.objectId);
  consume.set('groupObjectId', group?.objectId);
  consume.set('account', account);
  consume.set('total_tokens', totalTokens);
  consume.set('completion_tokens', completionTokens);
  consume.set('reply_text', replyText);
  consume.set('agent', agent);
  consume.set('type', type);
  consume.set('source', source);
  return consume.save();
};

const consumeAiTokens = async ({ account, data, }: { account: EthAddress; data: any; }): Promise<{
  type: string;
  success: boolean;
  msg: string;
  totalAITokens: number;
  balanceAITokens: number;
  consumeAmount: number,
  account: EthAddress;
  wxUserOID: string;
  gxUserOID: string;
  errorType?: string;
}> => {

  try {
    const { type, total_tokens, user: inWxUser, agent, exe, source } = data;
    const consumeAmount = Number(total_tokens) || 0;
    // 保存记录
    insertAIConsumeRecord(account, data);
    // 修改余额
    const wxUsr = await addOrUpdateWxUser(agent, inWxUser, true);
    const gxUser = await getGroupxUserByItchat(wxUsr);
    const gxUserOID = gxUser?.objectId;
    const wxUserOID = gxUser?.userObjectId || wxUsr.objectId || inWxUser.objectId;
    if (gxUser && isValidString(wxUserOID)) {
      if (!gxUser.userObjectId) setUserObjectId(gxUser.objectId, wxUserOID, wxUsr.NickName, true)
      const { totalAITokens, balanceAITokens, usedAITokens } = gxUser;
      let balance = balanceAITokens;
      const used = usedAITokens + consumeAmount;
      let success = false
      // 除非明确指出只计算,不扣费才会不扣除余额
      if (exe === false) {
        logger.info(`估算消费:${consumeAmount} 已用:${used} 余额:${balance} ${gxUser.name}, ${gxUser.nickName}, `);
      } else {// 正式消费
        balance = balanceAITokens - consumeAmount;
        logger.warn(`消费AI积分:${consumeAmount} 已用:${used} 余额:${balance} ${gxUser.name}, ${gxUser.nickName}, `);
        success = await updateUserAITokens(gxUser.account, consumeAmount);
      }

      let msg = 'ok'
      if (balance < Number(process.env.FLOOR_AI_TOKENS || -3000)) {
        logger.warn(`AI积分不足:${consumeAmount} 已用:${used} 余额:${balance} ${gxUser.name}, ${gxUser.nickName}, `);
        msg = `AI积分不足,请充值[${balance}]`;
        success = false
      }
      return {
        type, account, success, msg, consumeAmount,
        totalAITokens, balanceAITokens: balance, wxUserOID, gxUserOID
      };
    }
    logger.warn('[consumeAiTokens] 消费积分时未找到对应的用户:', data);
    return {
      type,
      success: false,
      msg: '消费积分时未找到对应的用户',
      account,
      errorType: 'user_not_exist',
      totalAITokens: 0,
      balanceAITokens: 0, consumeAmount,
      wxUserOID,
      gxUserOID
    };

  } catch (e) {
    logger.error('consumeAiTokens error: ', e);
  }

  throw new Error('consumeAiTokens error');
};

class ConsumeServices extends BaseServices {


  serviceConsumeAiTokens = async ({ account, data }: { account: EthAddress; data: any }) => {
    // logger.info('==>serviceConsumeAiTokens body: ', account, data);
    return consumeAiTokens({ account, data });
  };


}

export default new ConsumeServices();

