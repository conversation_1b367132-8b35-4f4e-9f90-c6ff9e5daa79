/**
 * 测试Dify API实现
 * 运行前请确保设置了以下环境变量:
 * - DIFY_API_KEY: Dify API密钥
 * - DIFY_CHAT_URL: Dify对话API URL (例如: https://api.dify.ai/v1/chat-messages)
 * - DIFY_COMPLETION_URL: Dify文本生成API URL (例如: https://api.dify.ai/v1/completion-messages)
 */

// 设置环境变量示例 - 请替换为真实的API密钥
process.env.DIFY_API_KEY = 'your-dify-api-key-here'
process.env.DIFY_CHAT_URL = 'https://api.dify.ai/v1/chat-messages'
process.env.DIFY_COMPLETION_URL = 'https://api.dify.ai/v1/completion-messages'

// 由于是TypeScript文件，我们需要使用编译后的版本或者模拟测试
console.log('注意：由于Dify API类是TypeScript文件，需要先编译或使用ts-node运行')
console.log('当前测试将模拟API调用过程...')

// 模拟测试函数
function simulateAPITest() {
    console.log('开始模拟Dify API测试...')

    // 检查环境变量
    const apiKey = process.env.DIFY_API_KEY
    const chatUrl = process.env.DIFY_CHAT_URL
    const completionUrl = process.env.DIFY_COMPLETION_URL

    console.log('\n=== 环境变量检查 ===')
    console.log('DIFY_API_KEY:', apiKey ? '已设置' : '未设置')
    console.log('DIFY_CHAT_URL:', chatUrl)
    console.log('DIFY_COMPLETION_URL:', completionUrl)

    if (apiKey === 'your-dify-api-key-here') {
        console.log('\n⚠️  警告：请设置真实的DIFY_API_KEY')
        console.log('请在环境变量中设置或修改test_dify_api.js文件中的API密钥')
    }

    console.log('\n=== API调用格式示例 ===')
    console.log('对话模式请求格式:')
    console.log(JSON.stringify({
        inputs: {},
        query: "你好，请介绍一下你自己",
        response_mode: "blocking",
        user: "test-user-123"
    }, null, 2))

    console.log('\n文本生成模式请求格式:')
    console.log(JSON.stringify({
        inputs: {
            query: "你好，请介绍一下你自己"
        },
        response_mode: "blocking",
        user: "test-user-123"
    }, null, 2))

    console.log('\n=== 使用说明 ===')
    console.log('1. 使用ts-node运行TypeScript版本:')
    console.log('   npx ts-node -e "import gptDify from \'./src/services/gpt/gptServerApiDify\'; gptDify.difyQuery(\'test\', \'hello\')"')
    console.log('')
    console.log('2. 或者先编译TypeScript:')
    console.log('   npm run build')
    console.log('   node dist/test_dify_api.js')
    console.log('')
    console.log('3. 在项目中使用:')
    console.log('   import gptServerApi from \'./src/services/gpt/gptServerApi\'')
    console.log('   const result = await gptServerApi.callGPT(userid, prompt, \'dify\')')
}

// 运行模拟测试
if (require.main === module) {
    simulateAPITest()
}

module.exports = { simulateAPITest }
