/**
 * 测试Dify API实现
 * 运行前请确保设置了以下环境变量:
 * - DIFY_API_KEY: Dify API密钥
 * - DIFY_CHAT_URL: Dify对话API URL (例如: https://api.dify.ai/v1/chat-messages)
 * - DIFY_COMPLETION_URL: Dify文本生成API URL (例如: https://api.dify.ai/v1/completion-messages)
 */

// 设置环境变量示例
process.env.DIFY_API_KEY = 'your-dify-api-key-here'
process.env.DIFY_CHAT_URL = 'https://api.dify.ai/v1/chat-messages'
process.env.DIFY_COMPLETION_URL = 'https://api.dify.ai/v1/completion-messages'

const gptDify = require('./src/services/gpt/gptServerApiDify').default

async function testDifyAPI() {
    console.log('开始测试Dify API...')
    
    const userid = 'test-user-123'
    const prompt = '你好，请介绍一下你自己'
    
    try {
        // 测试对话模式
        console.log('\n=== 测试对话模式 ===')
        const chatResult = await gptDify.chatQuery(userid, prompt)
        if (chatResult) {
            console.log('对话模式成功:')
            console.log('- 模型:', chatResult.model)
            console.log('- 响应:', chatResult.showText)
            console.log('- Token使用:', {
                total: chatResult.aiTokens,
                prompt: chatResult.promptTokens,
                completion: chatResult.completionTokens
            })
        } else {
            console.log('对话模式失败')
        }
        
        // 测试文本生成模式
        console.log('\n=== 测试文本生成模式 ===')
        const completionResult = await gptDify.completionQuery(userid, prompt)
        if (completionResult) {
            console.log('文本生成模式成功:')
            console.log('- 模型:', completionResult.model)
            console.log('- 响应:', completionResult.showText)
            console.log('- Token使用:', {
                total: completionResult.aiTokens,
                prompt: completionResult.promptTokens,
                completion: completionResult.completionTokens
            })
        } else {
            console.log('文本生成模式失败')
        }
        
        // 测试兼容方法
        console.log('\n=== 测试兼容方法 ===')
        const difyResult = await gptDify.difyQuery(userid, prompt)
        if (difyResult) {
            console.log('兼容方法成功:')
            console.log('- 模型:', difyResult.model)
            console.log('- 响应:', difyResult.showText)
            console.log('- Token使用:', {
                total: difyResult.aiTokens,
                prompt: difyResult.promptTokens,
                completion: difyResult.completionTokens
            })
        } else {
            console.log('兼容方法失败')
        }
        
    } catch (error) {
        console.error('测试过程中发生错误:', error)
    }
}

// 运行测试
if (require.main === module) {
    testDifyAPI()
}

module.exports = { testDifyAPI }
