/* eslint-disable no-unused-expressions */
import { Router } from "express";
import * as utilsController from "../../controllers/utilsController";
import validators from "../validators";

const router: Router = Router();

export default (app: Router) => {
  app.use("/util", router);

  router.get(
    "/symbol/:address/:chain",
    validators.paramAddressValidator("address"),
    validators.paramStringLengthValidator("chain", 1, false),
    utilsController.getTokenInfo,
  );

  router.get(
    "/contractType/:address/:type/:chain",
    validators.paramAddressValidator("address"),
    validators.paramStringLengthValidator("type", 1, false),
    validators.paramStringLengthValidator("chain", 1, false),
    utilsController.getContractType,
  );
  router.get("/ipfs/gateways", utilsController.getIpfsGateway)
  router.get("/tencent-cos/post-policy/:ext", utilsController.generateCosKey)
  router.post('/feedback/:sessionId', utilsController.feedBack)
  router.get('/ai/setting', utilsController.getAiSetting)
  router.post('/ai/setting', utilsController.setAiSetting)

  // 获取系统统计信息
  router.get('/system/statistics', utilsController.getSystemStatistics)
  // 获取用户量及活跃用户量曲线数据
  router.get('/system/users-count-curve/:start/:end', utilsController.getSystemUsersStatistics)
  // 获取系统报警信息列表
  router.post('/system/alerts/list/:pageNum/:pageSize', utilsController.getSystemAlerts)
  // 获取天气信息
  router.get('/weather/:city', utilsController.getWeather)
};
