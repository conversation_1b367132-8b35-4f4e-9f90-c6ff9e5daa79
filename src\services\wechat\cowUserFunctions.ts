/**
 * 微信用户管理服务
 * 
 * 提供微信用户相关功能:
 * - 用户信息更新
 * - 好友列表管理
 * - 用户消息记录
 * - 用户统计信息
 * - 用户权限验证
 * 
 * <AUTHOR> <<EMAIL>>
 * @date 2024
 */
import { ParseDB } from "../../database/database"
import { EthAddress, EthZero } from "../../types/types"
import logger from "../../utils/logger"
import { isEthAddress, isParseObjectID, isValidString } from "../../utils/utils"
import RemarkNameInfo from "../users/RemarkNameInfo"
import { getUserJson } from '../users/userFunction'
import { WxUserInfo } from "./types"
// 1.微信好友用户信息
// 2.群成员用户信息
// 3.群信息
const TABLE_NAME = "AIItchatUsers" // 微信好友+,群成员用户
const makeWxUserRef = (objectId: string) => ({ __type: 'Pointer' as const, className: 'AIItchatUsers' as const, objectId });
const getRecordJson = (record: ParseDB.Object, detail: boolean = false): WxUserInfo => {
  if (!record)
    return undefined;

  const baseInfo = {
    objectId: record.id,
    account: record.get("account"),
    wxid: record.get("wxid"),
    alias: record.get("alias"),
    UserName: record.get("UserName"),
    NickName: record.get("NickName"),
    HeadImgUrl: record.get("HeadImgUrl"),
  };

  return detail ? {
    ...baseInfo,
    MemberCount: record.get("MemberCount"),
    RemarkName: record.get("RemarkName"),
    Sex: record.get("Sex"),
    Statues: record.get("Statues"),
    Province: record.get("Province"),
    City: record.get("City"),
    DisplayName: record.get("DisplayName"),
    ChatRoomId: record.get("ChatRoomId"),
    KeyWord: record.get("KeyWord"),
    IsOwner: record.get("IsOwner"),
  } : baseInfo;
};

// 使用nickName只能找到一个时使用
const getWxUserByNickNameJustOne = async (agent: EthAddress, nickName: string) => {
  if (!nickName) return undefined;
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('agent', agent);
  query.equalTo('NickName', nickName);
  const results = await query.find();
  return results && results.length == 1 ? getUserJson(results[0]) : null;
};
const getWxUserByObjectId = async (objectId: string) => {
  if (!isValidString(objectId)) return undefined;
  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('objectId', objectId);
  const record = await query.first();
  return getRecordJson(record);
};

const getWxUserByAccount = async (account: EthAddress) => {
  if (!isEthAddress(account)) return undefined;

  const query = new ParseDB.Query(TABLE_NAME);
  query.equalTo('account', account);
  const record = await query.first();
  return getRecordJson(record);
};
// user or group
const getUserRecord = async (agent: EthAddress, user: WxUserInfo, source = "itchat") => {
  const { objectId, account, UserName, NickName, HeadImgUrl, RemarkName, wxid, alias } = user

  let record

  if (isValidString(alias)) {
    // 如果通过微信号(alias)都找不到,那就无法再找到了.
    const query = new ParseDB.Query(TABLE_NAME)
    query.equalTo("agent", agent)
    query.equalTo("alias", alias)
    query.ascending("createdAt")
    record = await query.first()
    if (record) {
      logger.warn(`通过alias:${alias}查找用户:${record.id}`)
      return record
    }
  }
  if (!record && isValidString(wxid)) { // wcferrry会提供wxid,不会发生变化
    const query = new ParseDB.Query(TABLE_NAME)
    query.equalTo("agent", agent)
    query.equalTo("wxid", wxid)
    query.ascending("createdAt")
    record = await query.first()
    if (record) {
      logger.warn(`通过wxid:${wxid}查找用户:${record.id}`)
      return record
    }
  }
  if (!record && isValidString(UserName)) {
    const query = new ParseDB.Query(TABLE_NAME)
    query.equalTo("agent", agent)
    query.equalTo("UserName", UserName)
    query.ascending("createdAt")
    record = await query.first()
    if (record) {
      logger.warn(`通过UserName:${UserName}查找用户:${record.id}`)
      return record
    }
  }
  if (isValidString(objectId)) {
    const query = new ParseDB.Query(TABLE_NAME)
    query.equalTo("objectId", objectId)
    record = await query.first()
    if (record) {
      logger.warn(`通过objectId:${objectId}查找用户:${record.id}`)
      return record
    }
  }

  if (!record && isEthAddress(account)) {
    const query = new ParseDB.Query(TABLE_NAME)
    query.equalTo("account", account)
    query.equalTo("agent", agent)
    query.ascending("createdAt")
    record = await query.first()
    if (record) {
      logger.warn(`通过account:${account}查找用户:${record.id}`)
      return record
    }
  }

  return record
}
// 获取用户对应的ethAddress,通过NickName,Remark,UserName等根据不同优先级查找
const getUserEthAddress = () => { }

// 添加用户,微信用户的 UserName 就是 id
const getItchatUserInfo = async (agent: EthAddress, user: WxUserInfo, detail: boolean = false): Promise<WxUserInfo> => {
  const record = await getUserRecord(agent, user)
  if (!record) return undefined

  return getRecordJson(record, detail)
}
// 添加用户,微信用户的 UserName 就是 id
const addOrUpdateWxUser = async (agent: EthAddress, user: WxUserInfo, detail: boolean = false, source = "itchat") => {
  try {
    // 如果UserName,wxid,alias都为空,则无法创建用户
    if (!user || (!user.UserName && !user.wxid && !user.alias && !user.account)) {
      logger.error(`用户信息太少,无法创建: ${user.NickName} ${user.account} ${user.UserName} `)
      return undefined
    }
    let record = await getUserRecord(agent, user, source)
    if (!isValidString(record?.id)) {
      record = new ParseDB.Object(TABLE_NAME)
      logger.info(`用户不存在，添加: ${user.NickName} ${user.account} ${user.UserName} `)
    }

    // wxid 只有在空的时候可填入
    if (!record.get('wxid') && user.wxid) record.set("wxid", user.wxid)
    // alias 只有在空的时候可填入
    if (user.alias && !record.get('alias')) record.set("alias", user.alias)
    // account不允许更新,只有为空时可填入
    const account: EthAddress = record.get("account")
    // 表中没有记录account
    if (!isEthAddress(account)) {
      const rm = new RemarkNameInfo(user.RemarkName)
      const acc = user.account || rm.getAccount()
      if (isEthAddress(acc)) record.set("account", acc)
    }

    // username 是id,服务重启即更新
    record.set("agent", agent)
    record.set("UserName", user.UserName)
    record.set("NickName", user.NickName)

    if (user.code) record.set("Code", user.code)
    // 会作为搜索条件，必须保存
    record.set("Country", user.Country)
    record.set("City", user.City)
    record.set("Sex", user.Sex)
    record.set("Province", user.Province)
    record.set("HeadImgUrl", user.HeadImgUrl)

    if (user.RemarkName) record.set("RemarkName", user.RemarkName) // 保持有account，objectId的json
    if (user.Statues) record.set("Statues", user.Statues)
    if (user.DisplayName) record.set("DisplayName", user.DisplayName)
    if (user.ChatRoomId) record.set("ChatRoomId", user.ChatRoomId)
    // Group
    if (user.MemberCount) record.set("MemberCount", user.MemberCount)
    if (user.KeyWord) record.set("KeyWord", user.KeyWord)
    if (user.IsOwner) record.set("IsOwner", user.IsOwner)

    record.set("IsAdmin", user.IsAdmin)

    const result = await record.save()
    return getRecordJson(result, detail)
  } catch (e) {
    logger.error('addOrUpdateWxUser: 添加或更新微信用户失败: ', agent, user);
    logger.error(e)
    return undefined;
  }
}

// 添加或更新用户来自itchat的群及agent的好友列表
const batchUpdateItchatUsers = async (agent: EthAddress, friends: WxUserInfo[], source = "itchat") => {
  const results = await Promise.all(
    friends.map((friend: WxUserInfo) => {
      try {
        if (friend.NickName) return addOrUpdateWxUser(agent, friend, false, source)
      } catch (error) {
        logger.error(`batchUpdateItchatUsers,更新好友失败:${JSON.stringify(friend)}`)
        logger.error(error)
      }
      return undefined
    }),
  )

  return results.filter(item => item)
}

const parseRemarkNameString = (str: string): { account: EthAddress, objectId: string } => {
  let account
  let objectId
  try {
    const parsedObject = JSON.parse(str)
    // 检查是否包含所需属性
    if (parsedObject && typeof parsedObject === 'object') {
      account = parsedObject.account || EthZero
      objectId = isParseObjectID(parsedObject.object_id) ? parsedObject.object_id : ''
    }
  } catch (error) {
    // logger.error(`parseRemarkNameString,解析失败:${str}`) // 频率非常高
  }
  return { account, objectId }
}

export {
  makeWxUserRef,
  addOrUpdateWxUser,
  batchUpdateItchatUsers,
  getItchatUserInfo,
  getWxUserByAccount, getWxUserByNickNameJustOne,
  getWxUserByObjectId,
  parseRemarkNameString,
  getRecordJson,
}

