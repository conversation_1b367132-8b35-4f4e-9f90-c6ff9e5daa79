import logger from '../../utils/logger';
import { isValidString } from "../../utils/utils";
import { IAIResItem, IWeightLossData } from "../health/weightLoss/weightLossTypes";

type ISleepBaseInfo = {
  nightSleep: number// 夜间睡眠
  totalSleep: number// 总睡眠  8 小时 19 分钟|  
  shortSleep: number// 零星小睡 2 小时 32 分钟  
  deepSleep: number// 深睡 1 小时 10 分钟  
  lightSleep: number// 浅睡 2 小时 58 分  
  fastEye: number// 快速眼动 1 小时 39 分钟
}
type ISleepGPTResponse = ISleepBaseInfo & {
  date: Date
  aiResponse: string
  aiTokens: number
  completionTokens: number
  promptTokens: number

  model: string // 大模型名称
  weight: number // 重量:kg
  bloodSugar: number // 血糖,毫摩尔/L
}
type IWeightLossGPTResponse = {
  date: Date

  aiResponse: IWeightLossData, // 健康打卡ai响应字符串
  aiJsonStr: string,// 健康打卡json字符串
  aiJson: any, // 健康打卡json数据

  aiTokens: number
  completionTokens: number
  promptTokens: number
  model: string
  bloodSugar: number // 血糖,毫摩尔/L
  sports?: Array<IAIResItem>

  targetWeight: number // 目标重量,kg
}
type IGPTResponse<T = unknown> = {
  model: string,
  json?: T, // json数据
  showText: string, // 发送给用户的主文本信息
  showText2?: string, // 发送给用户的副本信息
  aiTokens: number,
  completionTokens: number,
  promptTokens: number,
}
class GPTServerComm {


  extractJson(text: string): string | null {
    // 使用正则表达式匹配 ```json 和 ``` 之间的内容
    const match = text.match(/```json([\s\S]+?)```/)

    // 如果匹配到，则返回匹配的内容
    return match ? match[1].trim() : text
  }


  parseJson(jsonText: string) {
    let jsonData: any;
    if (!jsonText) {
      return null;
    }
    try {
      jsonData = JSON.parse(this.extractJson(jsonText) || jsonText);
    } catch (e) {
      logger.error('解析GPT返回的字符串为JSON时发生意外:', e);
    }
    return jsonData;
  }


  // 搜索字典中的值，返回str
  searchValueStrByKey(data: any, keysToSearch: string[]) {
    if (!data) return 0
    let value: any
    if (Array.isArray(data)) {
      // eslint-disable-next-line no-restricted-syntax
      for (const searchItem of keysToSearch) {
        value = data.find(obj => obj.name.includes(searchItem))
        if (value) break
      }
    } else if (typeof data === "object") {
      const keyContainsChars = Object.keys(data).find(key => keysToSearch.some(char => key.includes(char)))
      if (!keyContainsChars) {
        return 0
      }
      value = data[keyContainsChars]
    }

    if (!value) return 0
    if (isValidString(value.unit) && ["h", "hour", "小时"].some(keyH => value.unit.toLowerCase().includes(keyH))) {
      return String(Number(value?.value) * 60)
    }
    if (isValidString(value.unit) && ["秒"].some(keyH => value.unit.toLowerCase().includes(keyH))) {
      return String(Number(value?.value) / 60)
    }

    return value?.value
  }

  // 搜索字典中的值，转为number
  searchValueByKey(data: any, keysToSearch: string[]) {
    return Number(this.searchValueStrByKey(data, keysToSearch))
  }
}

export default GPTServerComm
export { GPTServerComm, IGPTResponse, ISleepBaseInfo, ISleepGPTResponse, IWeightLossGPTResponse };

