/**
 * 微信群管理服务
 * 
 * 提供微信群相关功能:
 * - 群列表管理
 * - 群成员管理
 * - 群消息记录
 * - 群统计信息
 * - 群关键词设置
 * - 群AI内容分析
 * 
 * <AUTHOR> <<EMAIL>>
 * @date 2024
 */
import { EthAddress } from '../../types/types';
import ApiError from '../../utils/api_error';
import DataError from '../../utils/data_error';
import { getLogger } from '../../utils/logger';

import { isEthAddress, isValidString } from '../../utils/utils';
import { BaseServices } from '../baseServices';
import { ParseDB } from '../services';
import { getDoctorOfGroup, getGroupsBySearch, getGroupsOfJoined, getItchatGroupInfo, getRecordJson, getWxGroupInfoByObjectId, getWxGroupMembers, isGroupMember, isGroupOwner2, updateGroups } from './cowGroupFunction';
import { getItchatUserInfo } from './cowUserFunctions';
import onWechatApi from './onWechatApi';
// 微信群信息，除微信群基本信息外，还包括医生给微信群添加的附属信息
// 表中可能出现分数不同agent的相同微信群.
// 每个群都有所属的agent账号.因此，微信群信息的唯一性是由groupId和agent唯一确定
const TABLE_NAME = 'AIItchatGroups';
const logger = getLogger('itchatWXGp');
export const TABLE_NAME_WXGROUPS = TABLE_NAME;

class WxGroupServices extends BaseServices {
  getRecordJson = getRecordJson

  isGroupMember = isGroupMember

  isGroupOwner2 = isGroupOwner2

  getDoctorOfGroup = getDoctorOfGroup;

  getWxGroupInfo = getItchatGroupInfo;

  getWxGroupInfoByObjectId = getWxGroupInfoByObjectId;

  getItchatGroupInfo = getItchatGroupInfo

  // 获取群欢迎信息列表,用户进去发送多条信息
  servicesGetJoinGroupMsgList = async ({ sessionId, groupObjectId }: { sessionId: string, groupObjectId: string }) => {
    const sess = await this.isSuperOrOwnerGroup(sessionId, groupObjectId)
    const query = new ParseDB.Query(TABLE_NAME);
    query.equalTo('objectId', groupObjectId);
    const record = await query.first();
    const group = getRecordJson(record, true);
    const members = group.MemberList

    return this.makeGResponseList(members, 1, members?.length, members?.length)
  }

  // 设置群欢迎信息列表,区别joinWelcomeInfo,是对条不同类型的信息( 群主或管理员),
  servicesSetJoinGroupMsgList = async ({ sessionId, groupObjectId }: { sessionId: string, groupObjectId: string, msgList: any }) => {
    // const sess = await this.isSuperOrOwnerGroup(sessionId, groupObjectId)
    // const { joinWelcomeInfo } = msgList;
    // const query = new ParseDB.Query(TABLE_NAME);
    // query.equalTo('objectId', groupObjectId);
    // const record = await query.first();
    // record.set('joinWelcomeInfo', joinWelcomeInfo);
    // return this.makeGResponse(await record.save());
  }

  servicesGetWxGroupMembers = async ({ sessionId, groupObjectId }: { sessionId: string, groupObjectId: string }) => {
    const sess = await this.isSuperOrOwnerGroup(sessionId, groupObjectId)

    if (!sess) throw new ApiError("无权查看:不是超级用户或群管理员", 60001);

    const members = await getWxGroupMembers({ groupObjectId })

    return this.makeGResponseList(members, 1, members?.length, members?.length)
  };

  // 邀请用户进入微信群
  servicesInviteUserToWxGroups = async ({ sessionId, agent, data }: {
    agent: EthAddress, sessionId: string,
    data: { userWxid: string, groupWxids: string[], wxMsgChannel: string }
  }) => {
    const { userWxid, groupWxids, wxMsgChannel } = data;
    const sess = await this.checkSession(sessionId);
    const results = await Promise.all(groupWxids?.map(async (groupWxid) => {
      const user = await getItchatUserInfo(sess.agent, {
        wxid: userWxid,
        UserName: userWxid,
        NickName: '',
        HeadImgUrl: ''
      });
      const group = await getItchatGroupInfo(sess.agent, {
        agent,
        wxid: groupWxid,
        groupUserName: groupWxid,
        groupNickName: '',
        groupHeadImgUrl: ''
      });
      if (!group) {
        logger.warn(`微信群不存在:${groupWxid} ${userWxid}`)
        return { userNickName: user.NickName, groupName: group.groupNickName, groupObjectId: group.objectId, userWxid, success: false, message: `微信群不存在` }
      }
      const members = await getWxGroupMembers({ groupObjectId: group.objectId })
      if (members?.some(member => member.UserName === userWxid)) {
        logger.warn(`用户已存在群中:${groupWxid} ${userWxid}`)
        return { userNickName: user.NickName, groupName: group.groupNickName, groupObjectId: group.objectId, userWxid, success: false, message: `⚠️用户"${user.NickName}"已加入群"${group.groupNickName}"` }
      }
      logger.warn(`邀请用户进群:${groupWxid} ${userWxid}`)
      const res = await onWechatApi.inviteUserToGroup({ userWxid, groupWxid: group.groupUserName, wxMsgChannel })
      return {
        userNickName: user.NickName, groupName: group.groupNickName,
        groupObjectId: group.objectId, userWxid, success: res,
        message: res ? `✅邀请"${user.NickName}"加入"${group.groupNickName}"发送成功` : `❌邀请"${user.NickName}"加入"${group.groupNickName}"发送失败`
      }
    }))
    return this.makeGResponse(results)
  }

  // 获取我加入的微信群
  serviceGetMyWxGroupsOfJoined = async ({ sessionId, sign, pageNum, pageSize }: { sessionId: string; sign: string, pageNum: number, pageSize: number }) => {
    const sess = await this.checkSession(sessionId);
    // return getGroupsOfJoined(sess.account, sess.agent, pageNum, pageSize);

    const { results, total } = await getGroupsOfJoined(sess.account, sess.wxid, sess.agent, pageNum, pageSize);
    if (results) {
      return this.makeGResponseList(results, pageNum, pageSize, total);
    }
    return this.makeGResponseListError()
  };





  servicesPostUpdateGroups = updateGroups;

  servicesUpdateWxGroupInfo = async ({ account, sessionId, agent, data, }: { account: EthAddress; sessionId: string, agent: EthAddress; data: any; }) => {
    if (!isEthAddress(agent)) throw new ApiError('agent is not eth address', 400);
    const sess = await this.checkSession(sessionId);

    const { id, objectId, groupObjectId, groupId, groupAvatar, description, joinWelcomeInfo, aiKeywords, aiSwitch } = data;
    const gId = groupObjectId || objectId || id

    const isPromise = await this.isSuperOrOwnerGroup(sessionId, gId);
    if (!isPromise)
      throw new ApiError('无权限(设置微信群)', 401)

    let record = null;

    if (isValidString(gId)) {
      const query = new ParseDB.Query(TABLE_NAME);
      query.equalTo('objectId', gId);
      record = await query.first();
    }
    if (!record && groupId) {
      const query = new ParseDB.Query(TABLE_NAME);
      query.equalTo('agent', agent);
      query.equalTo('groupUserName', groupId);
      record = await query.first();
    }

    if (record) {
      if (description) record.set({ description });
      if (joinWelcomeInfo) record.set({ joinWelcomeInfo });
      if (aiKeywords) record.set({ aiKeywords });
      if (aiSwitch) record.set({ aiSwitch });
      return this.makeGResponse(await record.save());
    }
    throw new DataError('指定的微信群不存在', 404);
  };

  // 获取所有微信群
  servicesGetGroupsAll = async ({
    sessionId,
    search,
    pageNum,
    pageSize,
  }: {
    search: any;
    sessionId: string;
    agent: EthAddress;
    pageNum: number;
    pageSize: number;
  }) => {
    const sess = await this.checkSession(sessionId);
    const isSuperMan = await this.isSuperMan(sessionId);
    if (!isSuperMan) throw new ApiError('没有权限', 403);


    const { results, total } = await getGroupsBySearch(sess.agent, pageNum, pageSize, search);
    if (results) {

      return this.makeGResponseList(results, pageNum, pageSize, total);
    }
    return this.makeGResponseListError()
  };
}


export default new WxGroupServices();


