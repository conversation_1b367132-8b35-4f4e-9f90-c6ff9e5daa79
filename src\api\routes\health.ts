/* eslint-disable no-unused-expressions */
import { Router } from "express";
import * as control from "../../controllers/healthController";
import { healthReportCtl } from "../../controllers/healthReportController";
import validators from "../validators";

const router: Router = Router();

export default (app: Router) => {
  app.use("/health", router);

  // 用于 test ,勿删
  router.get("/hello", control.getHello);
  router.get("/test", control.test);

  // 健康调研报告
  router.post("/survey/:account", validators.paramAddressValidator("account"), control.postUserHealthSurvey);
  router.get("/survey/:account", validators.paramAddressValidator("account"), control.getUserHealthSurvey);
  router.get("/survey/list/:pageNum/:pageSize",
    validators.paramNumberValidator("pageNum", 1, false, 500),
    validators.paramNumberValidator("pageSize", 1, false, 100),
    control.getHealthSurveyList
  )
  // 获取用户信息档案卡列表
  router.get("/user-info-card/list/:pageNum/:pageSize",
    validators.paramNumberValidator("pageNum", 1, false, 500),
    validators.paramNumberValidator("pageSize", 1, false, 100),
    control.getUserInfoCardList
  )
  // 设置用户信息档案卡
  router.post("/user-info-card/set/:userObjectId", validators.parseObjectIdValidator("userObjectId"),
    control.postUserInfoCardSet)
  //------------------------------------------
  // 减重打卡
  router.post("/weight-loss/:account", validators.paramAddressValidator("account"),
    control.postWeightLoss);
  // 减重打卡
  router.post("/weight-loss/last-data/:account", validators.paramAddressValidator("account"),
    control.postWeightLossLastData,
  );
  // // 减重打卡:统计指定微信群,一段时间内,聊天记录中的各项统计信息:减重,平均喝水,平均睡眠,有氧 ,无氧,柔韧性
  router.get("/weight-loss/checkin/statistics/wxgroup/:groupObjectId",
    validators.headerStringLengthValidator("session-id", 24, false),
    validators.headerStringLengthValidator("iknow-agent", 24, false, 100),
    // validators.paramStringLengthValidator("query.range", 24, false, 100),
    control.getCheckinStatisticsOfGroupInRange)
  //------------------------------------------
  // 卡路里打卡(食物摄入)
  router.post("/calories-record/:account", validators.paramAddressValidator("account"),
    control.postCaloriesRecord)
  //------------------------------------------
  // 卡路里饮食方案
  router.get("/calories/plan/list/:pageNum/:pageSize",
    validators.paramNumberValidator("pageNum", 1, false, 500),
    validators.paramNumberValidator("pageSize", 1, false, 100),
    control.getCaloriesPlanList)
  // 获取卡路里饮食方案 
  router.get("/calories/plan/:planObjectId",
    validators.parseObjectIdValidator("planObjectId"),
    control.getCaloriesPlan)
  // 删除卡路里饮食方案
  router.delete("/calories/plan/remove/:planObjectId",
    validators.parseObjectIdValidator("planObjectId"),
    control.removeCaloriesPlan)
  // 设置卡路里饮食方案
  router.post("/calories/plan/set",
    control.postCaloriesPlanSet)

  // 更新卡路里饮食
  router.post("/calories/meal/set",
    control.postCaloriesMeal)
  // 删除卡路里饮食
  router.delete("/calories/meal/remove/:mealObjectId",
    validators.parseObjectIdValidator("mealObjectId"),
    control.removeCaloriesMeal)
  // 获取卡路里饮食列表
  router.get("/calories/meal/list/:pageNum/:pageSize",
    validators.paramNumberValidator("pageNum", 1, false, 500),
    validators.paramNumberValidator("pageSize", 1, false, 100),
    control.getCaloriesMealList)
  // 获取卡路里饮食
  router.get("/calories/meal/:mealObjectId",
    control.getCaloriesMeal)
  // 获取系统可用的卡路里摄入食物列表
  router.get('/calories/food/list/:pageNum/:pageSize',
    validators.paramNumberValidator("pageNum", 1, false, 500),
    validators.paramNumberValidator("pageSize", 1, false, 100),
    control.getCaloriesFoods)
  // 获取系统可用的卡路里摄入食物
  router.get('/calories/food/get/:foodObjectId', control.getCaloriesFood)
  // 设置系统可用的卡路里摄入食物列表
  router.post('/calories/food/set', control.postCaloriesFood)
  // 获取用户卡路里档案卡
  router.get('/calories/card/:userObjectId', control.getCaloriesCard)
  // 设置用户卡路里档案卡
  router.post('/calories/card/:userObjectId', control.postCaloriesCard)


  //------------------------------------------
  // 图片ocr
  router.post("/image-ocr-record/:account",
    control.postImageOcrRecord)
  //------------------------------------------
  // 体检报告
  router.get("/health-report/:reportType/:userObjectId",
    validators.paramStringLengthValidator("reportType", 4, false, 100),
    validators.paramStringLengthValidator("userObjectId", 4, false, 100),
    healthReportCtl.getHealthReport)
  router.get("/health-report/:reportType/list/:userObjectId/:pageNum/:pageSize",
    validators.paramStringLengthValidator("userObjectId", 4, false, 100),
    validators.paramStringLengthValidator("reportType", 4, false, 100),
    validators.paramNumberValidator("pageNum", 1, false, 500),
    validators.paramNumberValidator("pageSize", 1, false, 100),
    healthReportCtl.getHealthReportList)

  //------------------------------------------
  // 睡眠打卡
  router.post("/sleep-record/:account", validators.paramAddressValidator("account"),
    control.postSleepRecord)
  router.post(
    "/sleep-record/last-data/:account",
    validators.paramAddressValidator("account"),
    control.getSleepRecordLastData,
  )
  //------------------------------------------
  // 健康打卡第二阶段消息
  router.post("/setting/second-stage/msg/list",
    control.postSettingSecondStageMsgList)

  router.get("/setting/second-stage/msg/list",
    control.getSettingSecondStageMsgList)
  // 健康打卡第二阶段设置
  router.get("/setting/second-stage",
    control.getSettingSecondStage)
  // 进群通知
  router.post("/setting/join-group/msg/list",
    control.postSettingJoinGroupMsgList)

  router.get("/setting/join-group/msg/list",
    control.getSettingJoinGroupMsgList)
  // 进群通知设置
  router.get("/setting/join-group",
    control.getSettingJoinGroup)
  // 两周复诊提醒
  router.post("/setting/two-week-reminder/msg/list",
    control.postSettingTwoWeekReminderMsgList)

  router.get("/setting/two-week-reminder/msg/list",
    control.getSettingTwoWeekReminderMsgList)
  // 两周复诊提醒设置
  router.get("/setting/two-week-reminder",
    control.getSettingTwoWeekReminder)
  // 获取系统报警信息列表
  // router.get('/sent/notices/logs', control.getSentNoticesLogs)

};
