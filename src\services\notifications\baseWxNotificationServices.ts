import { ParseDB } from "../../database/database";
import { EthAddress } from "../../types/types";
import logger from "../../utils/logger";
import { isValidString } from '../../utils/utils';
import { BaseServices } from "../baseServices";
import onWechatApi, { WxMsgContent } from "../wechat/onWechatApi";

export type NotificationType = 'system' | 'sys_1month' | 'sys_3month' | 'sys_6month' | 'sys_daily' | 'user' | 'group' | 'user_spurCheckIn';

/**
 * 微信通知基础服务类
 * 
 * 功能：
 * 1. 提供基础的消息发送功能
 * 2. 处理消息发送记录的存储
 * 3. 管理消息发送开关
 * 4. 实现消息发送的延迟和重试机制
 * 
 * 说明：
 * - 该类作为所有微信通知服务的基类
 * - 不直接处理群组管理，只负责消息发送
 * - 所有发送的消息都会被记录到数据库
 */
class BaseWxNotificationServices extends BaseServices {
    protected switchSendMsg: string;

    constructor() {
        super();
        this.switchSendMsg = process.env.HEALTH_SWITCH_SEND_MSG ?? 'true';
    }

    // 记录消息发送
    protected async recordMsgSent(agent: EthAddress, groupWxid: string, msg: string, type: NotificationType): Promise<{ recordResult: boolean, objectId: string }> {
        try {
            const MessageRecord = ParseDB.Object.extend("NotificationWXGroupsMessages");
            const record = new MessageRecord();
            record.set('agent', agent);
            record.set('groupWxid', groupWxid);
            record.set('message', msg);
            record.set('type', type);
            const result = await record.save(null, { useMasterKey: true });
            return { recordResult: true, objectId: result.id };
        } catch (error) {
            logger.error('记录消息发送失败:', error);
            return { recordResult: false, objectId: '' };
        }
    }

    // 删除消息发送记录
    protected async removeMsgSent(objectId: string): Promise<boolean> {
        try {
            const query = new ParseDB.Query("NotificationWXGroupsMessages");
            query.equalTo('objectId', objectId);
            const result = await query.first({ useMasterKey: true });
            if (result) {
                await result.destroy({ useMasterKey: true });
            }
        } catch (error) {
            logger.error('删除消息发送记录失败:', error);
            return false;
        }
        return true;
    }
    // 发送消息到用户(私聊,公众号)
    protected async sendMsgToUsers(agent: EthAddress, userWxids: Array<string>, msg: string, type: NotificationType = 'user', test: boolean = false): Promise<boolean> {
        if (this.switchSendMsg !== 'true') {
            logger.warn(`微信通知: 发送消息开关未开启,不发送!`);
            return false;
        }
        if (!isValidString(msg)) {
            logger.warn('待发送消息为空')
            return false
        }

        const results = await Promise.all(userWxids.map(async (userWxid) => {
            const delay = Math.floor(Math.random() * 2000) + 1000;
            await new Promise<void>((resolve) => { setTimeout(() => resolve(), delay) });

            const result = await onWechatApi.sendText(msg, userWxid, "", agent);
            logger.info(`微信通知: 发送消息到微信${userWxid}结果: ${result}`);
            return result;
        }));

        return results.some(result => result);
    }
    // 发送消息到微信群
    protected async sendMsgToGroups(agent: EthAddress, groupWxids: Array<string>, msg: string, type: NotificationType = 'group', test: boolean = false): Promise<boolean> {
        if (this.switchSendMsg !== 'true') {
            logger.warn(`微信通知: 发送消息开关未开启,不发送!`);
            return false;
        }
        if (!isValidString(msg)) {
            logger.warn('待发送消息为空')
            return false
        }

        const results = await Promise.all(groupWxids.map(async (groupWxid) => {
            const delay = Math.floor(Math.random() * 2000) + 1000;
            await new Promise<void>((resolve) => { setTimeout(() => resolve(), delay) });

            const wxMsg: WxMsgContent = {
                type: 'text',
                content: msg
            };
            const result = await onWechatApi.sendMsgToGroups(wxMsg, [groupWxid], test, agent);
            logger.info(`微信通知: 发送消息到微信群${groupWxid}结果: ${result}`);
            return result;
        }));

        return results.some(result => result);
    }
}

export default BaseWxNotificationServices; 