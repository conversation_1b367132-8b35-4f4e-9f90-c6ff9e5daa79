/**
 * 患者管理服务
 * 功能包括:
 * 1. 患者基础信息管理
 * 2. 医生-患者关系管理
 * 3. 患者问答记录管理
 * 4. 定时提醒服务
 */

import { DoctorInfo } from "../../types/prisma"
import { EthAddress } from "../../types/types"
import { getLogger } from '../../utils/logger'

import { isEthAddress } from "../../utils/utils"
import { BaseServices } from "../baseServices"
import doctorServices from "../chatlist/doctorServices"
import knowServices, { IKnowledge } from "../chatlist/myknowledgeServices"
import gpt from "../gpt/gptServerApi"
import { ParseDB } from "../services"
import { addFriendTimer, deleteFriendTimer, getFriendTimer, getFriendTimers } from "../timer/timerFunction"
import { TIMER_CLIENT } from "../timer/types"
import RemarkNameInfo from "../users/RemarkNameInfo"
import { getDoctorInfoByAccount } from "../users/userFunction"
import { getDoctorOfGroup } from "../wechat/cowGroupFunction"
import { getItchatUserInfo } from "../wechat/cowUserFunctions"

const logger = getLogger('healthPSRV');
// 病人和医生进行沟通时,只要病人主动发给医生聊天的就算是好友.
// 病人,好友,客户都保存paitents表中
const TABLE_NAME = "AIHealthPatients"
// 医生对应的好友(病人)
type PATIENT_INFO = {
  id?: string
  ownerAccount: EthAddress
  account: EthAddress
  name: string // wx 的 NickName
  userId: string // wx 的 UserName
  avatar: string // wx 的 HeadImgUrl
  userObjectId?: string // 用户表中的objectId

  questionCount?: number // 提问次数,向自己提问
  questionTextAll?: string // 提问文本汇总
  questionTextSize?: number // 提问文本字节数

  notics?: number // 我向其通知次数

  responseCount?: number // 我向其回复次数
  responseTextAll?: string // 回复文本汇总
  responseTextSize?: number // 回复文本字节数
}

const getPaitents = async (account: EthAddress) => {
  const query = new ParseDB.Query(TABLE_NAME)
  query.equalTo("ownerAccount", account)
  const results = await query.find()
  return results.map((item: any) => item.toJSON())
}

const getFriend = (account: EthAddress, friendAccount: string) => {
  const query = new ParseDB.Query(TABLE_NAME)
  query.equalTo("ownerAccount", account)
  query.equalTo("friendAccount", friendAccount)
  return query.first()
}
const isSummaryOpen = (doctor: EthAddress) =>
  //  if (doctor === "a") return true;
  false

const addPatients = async (doctor: EthAddress, friend: PATIENT_INFO) => {
  logger.info(`添加患者：${friend.name} ${doctor}`)
  const query1 = new ParseDB.Query(TABLE_NAME)
  query1.equalTo("ownerAccount", doctor)
  if (isEthAddress(friend.account))
    query1.equalTo("account", friend.account || "0x00001")

  const query2 = new ParseDB.Query(TABLE_NAME)
  query2.equalTo("ownerAccount", doctor)
  query2.equalTo("userId", friend.userId || "aaaa")

  const query3 = new ParseDB.Query(TABLE_NAME)
  query3.equalTo("ownerAccount", doctor)
  query3.equalTo("name", friend.name || "bbbb")

  const query = ParseDB.Query.or(query1, query2, query3)

  let oldQuestionSummarySize = 0
  let oldResponseSummarySize = 0
  let record = await query.first()
  if (record) {
    oldQuestionSummarySize = record.get("questionSummarySize") || 0
    oldResponseSummarySize = record.get("responseSummarySize") || 0
  } else record = new ParseDB.Object(TABLE_NAME)

  record.set("ownerAccount", doctor)
  record.set("account", friend.account)
  record.set("name", friend.name)
  if (friend.avatar) record.set("avatar", friend.avatar)

  record.set("userObjectId", friend.userObjectId)
  record.set("refItchatUsers", {
    __type: "Pointer",
    className: "AIItchatUsers",
    objectId: friend.userObjectId,
  })

  record.set("notics", friend.notics)
  record.set("questionCount", friend.questionCount)
  record.set("questionTextAll", friend.questionTextAll)
  record.set("questionTextSize", friend.questionTextSize)
  if (isSummaryOpen(doctor) && friend.questionTextSize - oldQuestionSummarySize > 100) {
    const summary: any = await gpt.getUserSummary(friend.questionTextAll)
    record.set("questionSummary", summary?.content)
    record.set("questionSummarySize", friend.questionTextSize)
  }

  record.set("responseCount", friend.responseCount)
  record.set("responseTextAll", friend.responseTextAll)
  record.set("responseTextSize", friend.responseTextSize)
  if (isSummaryOpen(doctor) && friend.responseTextSize - oldResponseSummarySize > 100) {
    const summary: any = await gpt.getUserSummary(friend.responseTextAll)
    record.set("responseSummary", summary?.content)
    record.set("responseSummarySize", friend.responseTextSize)
  }

  return record.save()
}

const deleteFriend = async (account: EthAddress, friendAccount: string) => {
  const query = new ParseDB.Query(TABLE_NAME)
  query.equalTo("ownerAccount", account)
  query.equalTo("friendAccount", friendAccount)
  const result = await query.first()
  if (result) {
    await result.destroy()
  }
}

type IKNOWLEDGE_REQUEST = {
  account: EthAddress // BOT Account
  receiver: string // BOT wx UserName
  receiverName: string // BOT wx NickName
  isGroup: boolean
  groupName: string
  user: any
}
// 根据itchat user信息获取医生信息，医生信息存储在Users表中。
const getDoctorOfWxUser = async (data: any): Promise<DoctorInfo> => {
  const { user, groupName, groupId, isGroup, receiver } = data
  if (isGroup) {
    return getDoctorOfGroup(data)
  }
  if (user) {
    const { account: rmAccount, objectId } = new RemarkNameInfo(user.RemarkName).getData()
    let userAccount = rmAccount
    let userObjectId = objectId
    if (!userObjectId && !isEthAddress(userAccount)) {
      // 到 itchat user 表中在找用户
      const u = await getItchatUserInfo(receiver, {
        objectId,
        UserName: user.UserName,
        NickName: user.NickName,
        account: rmAccount,
        HeadImgUrl: ""
      })
      if (u) {
        if (isEthAddress(u.account)) userAccount = u.account
        else userObjectId = u.objectId
      }
    }

    const doctor = await doctorServices.getMyDoctor({ userObjectId, account: userAccount, agent: receiver })
    if (!doctor) return null

    logger.info(`getDoctorOfWxUser 用户(${user.NickName})的 doctor是(:${doctor.professionalName})-${doctor.account}`)
    return getDoctorInfoByAccount(doctor?.account || doctor?.doctorAccount)
  }
  return undefined
}
const makePrompt = (doctor: DoctorInfo) => {
  const name = doctor.professionalName || doctor.name

  const prompt = `现在你扮演'${name}'医生的小助理，我们的团队所属科室是${doctor.department}，我们是${doctor.intro}。我们擅长${doctor.skill}。我们的其他一些信息是${doctor.other}。`
  return prompt
}
const getFriendKnowledge = async ({ account, data }: { account: EthAddress; data: IKNOWLEDGE_REQUEST }) => {
  const doctor: any = await getDoctorOfWxUser(data)
  let know
  if (doctor) {
    const name = doctor.professionalName || doctor.name
    const firstKnow: IKnowledge = {
      title: makePrompt(doctor),
      content: "好的，记住了！",
      id: "first11111",
      createAt: "",
      replyObjectId: "",
      updatedAt: "",
    }
    know = (await knowServices.getKnowledges({ account: doctor.account, pageNum: 0, pageSize: 60 })) || []
    // 插入知识库开头位置
    know.unshift(firstKnow)
    logger.info(`===》使用用户知识库，找到对应的医生 ${doctor?.name} : ${firstKnow?.title}`)
  } else {
    know = [
      {
        title: process.env.SYSTEM_PROMPT,
        content: "好的，记住了！",
        id: "first11112",
        createAt: "",
        replyObjectId: "",
        updatedAt: "",
      },
    ]
    logger.warn("===》使用系统默认知识库，找不到对应的医生")
  }
  return {
    doctorName: doctor?.name,
    doctor: doctor?.account,
    doctorProName: doctor?.professionalName || doctor?.name,
    doctorIntro: doctor?.intro,
    doctorSkill: doctor?.skill,
    doctorOtherInfo: doctor?.other,
    knowledges: know,
  }
}

// const getFriendTimers = async ({account: EthAddress, friendAccount: string}) => {
//   const query = new ParseDB.Query(TABLE_TIMER);
//   query.equalTo("account", account);
//   query.equalTo("friendAccount", friendAccount);
//   const results = await query.find();
//   return results.map((item: any) => item.toJSON());
// };

class HealthPatientsServices extends BaseServices {
  getRecordJson = async (record: any) =>
    record
      ? {
        objectId: record.id,
        ownerAccount: record.get("ownerAccount"),
        account: record.get("account"),
        name: record.get("name"),
        userId: record.get("userId"),
        period: record.get("period"),
        time: record.get("time"),
        event: record.get("event"),
        msg: record.get("msg"),
      }
      : {};

  servicesDelFriendTimer({ account, data }: { account: EthAddress; data: TIMER_CLIENT }) {
    return deleteFriendTimer({ account, data })
  }

  // 通过好友账号获取好友对应的医生,并提供医生的知识库,方便好友使用该知识库得到答案
  servicesGetFriendKnowledge({ account, data }: { account: EthAddress; data: IKNOWLEDGE_REQUEST }) {
    return getFriendKnowledge({ account, data })
  }

  servicesGetPaitents = async ({ sessionId, sign }: { sessionId: string; sign: string }) => {
    const sess = await this.checkSession(sessionId)
    return getPaitents(sess.account)
  };

  servicesGetFriend({ account, friendAccount }: { account: EthAddress; friendAccount: string }) {
    return getFriend(account, friendAccount)
  }

  servicesAddFriend = addPatients;

  servicesDelFriend = deleteFriend;

  servicesAddFriendTimer = addFriendTimer;

  servicesGetFriendTimer = getFriendTimer;

  servicesGetFriendTimers = getFriendTimers;

  // 获取参与患者答疑的最近50个医生的account
  getLastDoctors = async (count: number = 50) => {
    const query = new ParseDB.Query("AIMyReply")
    query.descending("updatedAt")
    query.limit(count)
    // 最新的account不重复的50条记录,即50个用户
    return query.distinct("account")
  };

  // 获取某个医生答疑的最近50个患者的account
  getLastPaitents = async (doctor: EthAddress, count: number = 50) => {
    const query = new ParseDB.Query("AIMyReply")
    query.equalTo("account", doctor)
    query.descending("updatedAt")
    query.limit(count)
    // 最新的account不重复的50条记录,即50个用户
    return query.distinct("chatUserObjectId")
  };

  // 获取两个人之间的提问信息汇总
  getQuestions = async ({
    userObjectId,
    doctor,
  }: {
    userObjectId: string
    doctor: EthAddress
  }): Promise<{
    questionCount: number // 问题总数
    questionTextAll: string // 问题文本汇总
    questionTextSize: number // 问题文本字节数
    asker: any // 提问人信息
    groupName: string
    agent: EthAddress
  }> => {
    const query = new ParseDB.Query("AIChatRecord")
    query.equalTo("userObjectId", userObjectId)
    query.equalTo("myDoctor", doctor)
    const questionCount = await query.count()
    const qs = await query.find()

    if (!qs || qs.length < 1) {
      logger.info(`getQuestions 患者(${userObjectId})-医生(${doctor})没有找到提问 ${qs}`)
      return undefined
    }

    const questionTextAll = qs?.map((item: any) => item.get("message")).join("\n")

    // 不管是群聊还是私聊，只要来自微信的，agent 就是receiver
    let agent = qs[0].get("receiver")
    let groupName = ""

    for (let i = 0; i < qs.length; i += 1) {
      const item = qs[i]
      const wxGroupName = item.get("wxGroupName")
      if (wxGroupName && wxGroupName.length > 0) {
        groupName = wxGroupName
        agent = item.get("receiver")
        break
      }
    }

    return {
      questionCount,
      questionTextAll,
      questionTextSize: questionTextAll?.length || 0,
      asker: {
        account: qs?.[0].get("account"),
        userId: qs?.[0].get("userId"),
        avatar: qs?.[0].get("userAvatar"),
      },
      agent,
      groupName,
    }
  };

  // 获取专家与患者的应答信息汇总
  getAnswers = async ({
    replier,
    chatUserObjectId,
  }: {
    replier: EthAddress
    chatUserObjectId: string
  }): Promise<{
    chatObjectId: string // 聊天记录的objectId
    responseCount: number // 回复次数
    responseTextAll: string // 回复文本汇总
    responseTextSize: number // 回复文本字节数
  }> => {
    const query = new ParseDB.Query("AIMyReply")
    query.equalTo("chatUserObjectId", chatUserObjectId)
    query.equalTo("account", replier)
    query.ascending("updatedAt")
    const responseCount = await query.count()
    const replies = await query.find()
    const responseTextAll = replies?.map((item: any) => item.get("replyMessage")).join("\n")
    return {
      chatObjectId: replies?.[0]?.id,
      responseCount,
      responseTextAll,
      responseTextSize: responseTextAll?.length || 0,
    }
  };

  // 汇总收集50个最近活动医生的前50个活跃好友
  // 1 找出50个医生
  // 2 找出每个医生的最近50个好友
  // 3 根据医生信息从回复表AIMyReply 找到聊天记录
  // 4 从回复表AIMyReply 找到对应的用户聊天信息id
  // 5 根据聊天信息id,从聊天信息表AIChatRecord中找到聊天记录
  // 6 从聊天记录中获取病人的 wx UserName,NickName,avatar
  // 7 将好友信息存入数据库 AIPatients
  // 8 同时将个人对应的医生关联信息存入:AIMyDoctor
  cronRefreshMyPatients = async () => {
    logger.info("cronRefreshMyPatients.......")
    const doctors = await this.getLastDoctors()
    logger.info("获取最近的50个医生的account:", doctors)
    if (!doctors || doctors.length < 1) {
      logger.info("cronRefreshMyPatients 没有找到最近50个医生")
      return
    }

    for (const doctorAccount of doctors) {
      try {
        // 为医生找出最近通讯的50个好友
        const friendObjectIds = await this.getLastPaitents(doctorAccount)
        logger.info(`医生${doctorAccount}的患者:"`, friendObjectIds)
        // 将每个好友加入好友列表
        friendObjectIds.forEach(async (objectId: string) => {
          // 提问数
          const question = await this.getQuestions({ doctor: doctorAccount, userObjectId: objectId })
          // 回复次数
          const reply = await this.getAnswers({ replier: doctorAccount, chatUserObjectId: objectId })
          // const chat = await chatServices.getChatDetail(reply.chatObjectId);
          if (!question) {
            logger.info(`医生${doctorAccount}的患者${objectId}没有提问`)
            return
          }
          const { agent } = question
          // itchat user
          const user = await getItchatUserInfo(agent, {
            objectId,
            NickName: '',
            UserName: "",
            HeadImgUrl: ""
          })
          if (!user) {
            logger.info(`医生${doctorAccount}的患者${objectId}没有微信信息 -${agent}`)
            return
          }

          const userId = question.asker.userId || user.UserName
          const userName = user.NickName
          const avatar = question?.asker.avatar || user.HeadImgUrl
          const userObjectId = question?.asker.userObjectId || user?.objectId
          const account = user.account || question?.asker.account


          logger.info(`医生 ${doctorAccount} 添加患者:${userName} - ${objectId}`)
          // 为doctor添加患者（好友）
          addPatients(doctorAccount, {
            account,
            name: userName,
            ownerAccount: doctorAccount,
            ...question,
            ...reply,
            userId,
            avatar,
            userObjectId,
          })

          logger.info(`患者 ${objectId} 添加医生:${doctorAccount}`)
          // 为个人添加 doctor
          doctorServices.addMyDoctor({
            userObjectId,
            userId,
            userAccount: account,
            userName,
            groupName: question?.groupName,
            agent,
            doctorAccount: doctorAccount,
          })
        })
      } catch (e) {
        logger.error("cronRefreshMyPatients....e:", e)
      }
    }
  };
}
export default new HealthPatientsServices()
