import fetch = require("isomorphic-fetch");
import logger from "./logger";
import { text } from "express";

const fetcher = async (
  resource: string,
  { body, validation, ...init }: Record<string, any> = {},
) => {
  const isGuildApiCall =
    !resource.startsWith("http") && !resource.startsWith("/api");
  const isServerless = resource.startsWith("/api");

  const api = isGuildApiCall ? process.env.NEXT_PUBLIC_API : "";

  const payload = body ?? {};

  const options = {
    ...(body
      ? {
        method: "POST",
        body: JSON.stringify(
          validation
            ? {
              payload,
              ...validation,
            }
            : body,
        ),
      }
      : {}),
    ...init,
    headers: {
      ...(body ? { "Content-Type": "application/json" } : {}),
      ...init.headers,
    },
  };

  return fetch(`${api}${resource}`, options)
    .catch((err: any) => {
      throw err;
    })
    .then(async (response: Response) => {
      // //console.info("response", response)
      // //console.info("response.json1", api)
      // //console.info("response.json2", resource)
      const res = await response?.json?.();

      if (!response.ok) {
        if (isGuildApiCall) {
          const error = res.errors?.[0];
          const errorMsg = error
            ? `${error.msg}${error.param ? ` : ${error.param}` : ""}`
            : res;

          return Promise.reject(errorMsg);
        }

        return Promise.reject(res);
      }

      return res;
    });
};
const fetcherGate = async (
  resource: string,
  { body, validation, ...init }: Record<string, any> = {},
) => {
  if (!resource) throw new Error("No resource provided to fetcherGate");

  const isApiCall = !resource.startsWith("http");
  const api = isApiCall ? process.env.GATE_SSO_SERVER : "";
  const payload = body ?? {};

  const options = {
    ...(body
      ? {
        method: "POST",
        body: JSON.stringify(
          validation
            ? {
              payload,
              ...validation,
            }
            : body,
        ),
      }
      : {}),
    ...init,
    headers: {
      redirect: "follow",
      ...(body ? { "Content-Type": "application/json" } : {}),
      ...init.headers,
    },
    // redirect: "follow",
  };

  return fetch(`${api}${resource}`, options)
    .catch((err: any) => {
      throw err;
    })
    .then(async (response: Response) => {
      const res = await response.json?.();
      if (!response.ok) {
        if (isApiCall) {
          const error = res.errors?.[0];
          const errorMsg = error
            ? `${error.msg}${error.param ? ` : ${error.param}` : ""}`
            : res;

          return Promise.reject(errorMsg);
        }

        return Promise.reject(res);
      }
      return res;
    });
};
const fetcherParse2 = async (
  resource: string,
  { body, validation, ...init }: Record<string, any> = {},
) => {
  const isApiCall =
    !resource.startsWith("http") && !resource.startsWith("/api");
  const api = isApiCall ? process.env.NEXT_PUBLIC_PARSE_SERVER_URL : "";

  const payload = body ?? {};
  const options = {
    ...(body
      ? {
        method: "POST",
        body: JSON.stringify(
          validation
            ? {
              payload,
              ...validation,
            }
            : body,
        ),
      }
      : {}),
    headers: {
      ...{
        "Content-Type": "application/json",
        "X-Parse-Application-Id": process.env.NEXT_PUBLIC_PARSE_APP_ID,
      },
      ...init.headers,
    },
    // redirect: "follow",
  };
  return fetch(`${api}${resource}`, options)
    .catch((err: any) => {
      throw err;
    })
    .then(async (response: Response) => {
      const res = await response.json?.();
      if (!response.ok) {
        if (isApiCall) {
          const error = res.errors?.[0];
          const errorMsg = error
            ? `${error.msg}${error.param ? ` : ${error.param}` : ""}`
            : res;

          return Promise.reject(errorMsg);
        }

        return Promise.reject(res);
      }
      return res?.result ?? res?.results ?? res;
    });
};
const fetcherMPC = async (
  resource: string,
  { body, validation, ...init }: Record<string, any> = {},
) => {
  const isApiCall = !resource.startsWith("http");
  const api = isApiCall ? process.env.API_MPC_SERVER : "";

  const payload = body ?? {};
  const options = {
    ...(body
      ? {
        method: "POST",
        body: JSON.stringify(
          validation
            ? {
              payload,
              ...validation,
            }
            : body,
        ),
      }
      : {}),
    headers: {
      ...(body
        ? {
          "Content-Type": "application/json",
        }
        : {}),
      ...init.headers,
    },
    // redirect: "follow",
  };
  return fetch(`${api}${resource}`, options)
    .catch((err: any) => {
      logger.error("fethcerMPC Failed to fetch:", err);
      throw err;
    })
    .then(async (response: Response) => {
      const res = await response.json?.();
      if (!response.ok) {
        logger.info("fetcherMPC !response.ok", response);

        const error = res.errors?.[0];
        const errorMsg = error
          ? `${error.msg}${error.param ? ` : ${error.param}` : ""}`
          : res;

        return { res, error, errorMsg };
      }
      return res;
    });
};
const fetcherOnWechat = async (
  resource: string,
  { body, validation, ...init }: Record<string, any> = {}, agent?: string,
): Promise<Response> => {
  let wechatHost = process.env.API_ON_WECHAT_SERVER;
  if (agent) {
    wechatHost = process.env[`API_ON_WECHAT_SERVER_${agent}`];
  }
  if (!wechatHost) {
    logger.error(`[fetcherOnWechat] 微信消息发送功能关闭[未设置发送服务器]:API_ON_WECHAT_SERVER_${agent}`);
    return Response.json({ code: 400, text: '禁用微信消息发送' });
  }

  const isApiCall = !resource.startsWith("http");
  const api = isApiCall ? wechatHost : "";

  const payload = body ?? {};
  const options = {
    ...(body
      ? {
        method: "POST",
        body: JSON.stringify(
          validation
            ? {
              payload,
              ...validation,
            }
            : body,
        ),
      }
      : {}),
    headers: {
      ...(body
        ? {
          "Content-Type": "application/json",
        }
        : {}),
      ...init.headers,
    },
    // redirect: "follow",
  };

  return fetch(`${api}${resource}`, options)
};
export { fetcherParse2, fetcherMPC, fetcherGate, fetcherOnWechat };
export default fetcher;
