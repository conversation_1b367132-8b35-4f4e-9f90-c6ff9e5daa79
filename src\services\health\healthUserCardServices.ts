/**
 * 用户健康档案服务
 * 功能包括:
 * 1. 用户基础健康信息管理
 * 2. 减重目标设定与追踪
 * 3. 睡眠记录档案维护
 * 4. 运动习惯记录
 */

// 用于微信公众号及Web应用登录

import { Parse, ParseDB } from "../../database/database";
import { IRunningCard, ISleepRecordCard, IStandardizedHealthGroup, IUserInfoCard, IWeightLossCard } from '../../types/health';
import { EthAddress } from "../../types/types";
import logger from "../../utils/logger";
import { BaseServices } from "../baseServices";
import {
  getCalorieCard,
  setCaloriesCard
} from './calories/healthCaloriesPlan';
import { ISleepRecordTable } from "./sleep/healthSleepServices";
import { IWeightLossTable } from "./weightLoss/weightLossTypes";

const TABLE_USER_CARD_WEIGHT_LOSS = "AI_WeightLossUserCard"
const TABLE_USER_CARD_SLEEP = "AI_SleepUserCard"
const TABLE_USER_CARD_RUNNING = "AI_RunningUserCard"
const TABLE_USER_CARD_CALORIE = "AI_CalorieUserCard"

/*
// 移到healthPatientsWxGroup.ts 后会导致循环调用
只有3-,6-,前缀群才算健康打卡群
  1. 获取群信息
  2. 判断是否是健康打卡群
  3. 获取健康打卡群信息
*/
const getStandardizedHealthGroup = async (groupOID: string): Promise<IStandardizedHealthGroup | null> => {
  const query = new ParseDB.Query("AIHealthPatientsWxGroup");
  query.equalTo('groupOID', groupOID);
  query.notEqualTo('groupStatus', 'INACTIVE');
  const record = await query.first();
  if (!record) return null;
  if (record) {
    const result = record.toJSON() as unknown as IStandardizedHealthGroup
    result.reVisitDates = record.get("reVisitDates")
    result.startDate = result.reVisitDates?.[0]
    result.weightLossPlan = record.get("weightLossPlan")
    return result

  }
  return null
};

class HealthUserUserCard extends BaseServices {
  getRecordJson = (record: ParseDB.Object): IWeightLossCard => record ? {
    objectId: record.id,
    agent: record.get("agent"),
    userObjectId: record.get("userObjectId"),
    groupObjectId: record.get("groupObjectId"),
    userName: record.get("userName"),
    groupName: record.get("groupName"),
    firstWeight: record.get("firstWeight") || 0,
    lastWeight: record.get("lastWeight") || 0,
    targetWeight: record.get("targetWeight") || 0,
    targetWeightHalfMonth: record.get("targetWeightHalfMonth"),
    targetWeightMonth: record.get("targetWeightMonth"),
    targetWeightThisWeek: record.get("targetWeightThisWeek"),
    weightLossPeriodMonth: record.get("weightLossPeriodMonth"),
    lastWeightDate: record.get("lastWeightDate") || new Date(0),
    firstWeightDate: record.get("firstWeightDate") || new Date(0),
    totalScore: record.get("totalScore") || 0,
    isAdminModify: record.get("isAdminModify") || false,
  } : undefined
  // 计算用户打卡得分
  calculateScore = (checkInRecord: IWeightLossTable) => {
    let score = 0;
    if (checkInRecord.weight) score += 10;
    if (checkInRecord.waterIntake) score += 10;
    if (checkInRecord.sleepTime) score += 10;
    if (checkInRecord.bloodSugar) score += 10;
    if (checkInRecord.exerciseAerobic) score += 10;
    if (checkInRecord.exerciseResistance) score += 10;
    if (checkInRecord.exerciseFlexibility) score += 10;

    // if (checkInRecord.weight && userCard.firstWeight && checkInRecord.weight < userCard.firstWeight) {
    //   score += 10;
    // }
    // if (checkInRecord.weight && userCard.lastWeight && checkInRecord.weight > userCard.lastWeight) {
    //   score += 10;
    // }

    return score;
  }

  /** 创建或保存用户减重档案信息
   * @param agent 用户钱包地址
   * @param userObjectId 用户ObjectId
   * @param userRecord 用户减重记录
   * @param targetWeight 目标体重
   * @param userRecordMerge 是否合并记录
   * @returns 用户减重档案信息
   */
  async saveUserWeightLossCard(agent: EthAddress, userObjectId: string, userRecord: IWeightLossTable, targetWeight: number, userRecordMerge: boolean)
    : Promise<IWeightLossCard> {
    if (!agent || !userObjectId || !userRecord) return undefined

    const data: IWeightLossCard = {
      objectId: "",
      agent, userObjectId,
      groupObjectId: userRecord.wxGroupObjectId,
      userName: userRecord.userName,
      groupName: userRecord.groupName,
      firstWeight: userRecord.weight ?? 0,
      firstWeightDate: userRecord.activityDate,
      lastWeight: userRecord.weight ?? 0,
      lastWeightDate: userRecord.activityDate,
      targetWeight: targetWeight ?? userRecord.weight * (1 - 0.15),
      targetWeightHalfMonth: 0.025,
      targetWeightMonth: 0.05,
      targetWeightThisWeek: 20,
      weightLossPeriodMonth: 3,
      totalScore: 0,
      isAdminModify: false
    }

    //--------------------------------------------------------------------------
    logger.info(`更新或创建用户减重档案卡:${userObjectId} - ${agent}`, userRecord?.userName)

    // 是否存在档案卡？
    const query = new Parse.Query(TABLE_USER_CARD_WEIGHT_LOSS)
    query.equalTo("agent", agent)
    query.equalTo("userObjectId", userObjectId)
    // 方便数据迁移,后续需要修改 TODO
    query.containedIn("groupObjectId", [userRecord.wxGroupObjectId, null, undefined])

    let createUserCard = false
    let record = await query.first()
    // 创建记录
    if (!record) {
      logger.info(`创建用户档案卡:${userObjectId} - ${agent}`, JSON.stringify(data, null, 2))
      record = new ParseDB.Object(TABLE_USER_CARD_WEIGHT_LOSS)
      createUserCard = true
    } else {
      // 临时,升级几天后,即可删除代码 TODO
      if (!record.get("groupObjectId") || !record.get("groupName") || !record.get("startDate"))
        createUserCard = true
    }
    // 更新用户档案卡
    record.set("userName", data.userName)
    record.set("groupName", data.groupName)

    const fWeightDate = record.get("firstWeightDate") || new Date()
    if (data.firstWeight && data.firstWeightDate && data.firstWeightDate <= fWeightDate) {
      record.set("firstWeight", data.firstWeight)
      record.set("firstWeightDate", data.firstWeightDate)
      logger.info(`更新用户档案卡首重:${userObjectId} - ${agent}`, JSON.stringify(data, null, 2))
    }

    const lWeightDate = record.get("lastWeightDate") || new Date(0)
    if (data.lastWeight && data.lastWeightDate && data.lastWeightDate >= lWeightDate) {
      record.set("lastWeight", data.lastWeight)
      record.set("lastWeightDate", data.lastWeightDate)
      logger.info(`更新用户档案卡末重:${userObjectId} - ${agent}`, JSON.stringify(data, null, 2))
    }

    record.set("targetWeight", data.targetWeight || 0)

    if (data.targetWeightHalfMonth) record.set("targetWeightHalfMonth", data.targetWeightHalfMonth || 0)
    if (data.targetWeightMonth) record.set("targetWeightMonth", data.targetWeightMonth || 0)
    if (data.targetWeightThisWeek) record.set("targetWeightThisWeek", data.targetWeightThisWeek || 0)

    record.set("weightLossPeriodMonth", data.weightLossPeriodMonth ?? 3)

    if (!userRecordMerge) {
      data.totalScore = record.get("totalScore") + userRecord.score
      record.set("totalScore", data.totalScore)
    }

    if (createUserCard) {
      record.set("agent", agent)
      record.set("userObjectId", userObjectId)
      record.set("groupObjectId", userRecord.wxGroupObjectId)

      let hGroup = await getStandardizedHealthGroup(userRecord.wxGroupObjectId)
      record.set("startDate", hGroup?.reVisitDates?.[0])
    }

    const result = await record.save()
    logger.info(`保存用户档案卡:${userObjectId} - ${agent}`, JSON.stringify(result, null, 2))

    return this.getRecordJson(result)
  }
  /**
   * 设置用户信息档案卡
   * @param agent 用户钱包地址
   * @param userObjectId 用户ObjectId
   * @param body 用户信息档案卡
   * @returns 用户信息档案卡
   */
  setUserInfoCard = async (agent: EthAddress, userObjectId: string, groupObjectId: string, body: any): Promise<IUserInfoCard> => {
    if (!agent || !userObjectId || !groupObjectId || !body) {
      throw new Error("参数错误")
    }
    logger.warn(`设置用户信息档案卡:${userObjectId} - ${agent}`, JSON.stringify(body, null, 2))

    const record = await this.getUserWeightLossCardRecord(agent, userObjectId, groupObjectId)
    if (!record) return undefined

    const weightLossCard: IWeightLossCard = body.weightLossCard
    let result = undefined
    if (weightLossCard) {
      if (weightLossCard.firstWeight) {
        if (weightLossCard.firstWeight < 20 || weightLossCard.firstWeight > 200) {
          throw new Error("体重范围错误")
        }
        record.set("firstWeight", weightLossCard.firstWeight)
        record.set("isAdminModify", true)
      }
      if (weightLossCard.targetWeight) {
        if (weightLossCard.targetWeight < 20 || weightLossCard.targetWeight > 200) {
          throw new Error("目标体重范围错误")
        }
        record.set("targetWeight", weightLossCard.targetWeight)
        record.set("isAdminModify", true)
      }
      result = await record.save()
    }
    let caloriesCard = body.caloriesCard
    if (caloriesCard) {
      caloriesCard = await setCaloriesCard(agent, userObjectId, groupObjectId, caloriesCard)
    }

    if (result) {
      const weightLossCard: IWeightLossCard = this.getRecordJson(result)

      const sleepCard = await this.getUserSleepRecordCard(agent, weightLossCard.userObjectId, weightLossCard.groupObjectId)
      const runningCard = await this.getUserRunningUserCard(agent, weightLossCard.userObjectId)
      const hGroup = await getStandardizedHealthGroup(weightLossCard.groupObjectId)
      return {
        weightLossCard,

        caloriesCard,

        sleepCard,
        runningCard,
        userName: weightLossCard?.userName,
        userObjectId: weightLossCard?.userObjectId,
        groupObjectId: hGroup?.objectId,
        groupName: hGroup?.groupName,
        wxGroup: hGroup,
        startDate: hGroup?.reVisitDates?.[0],
      }
    }
    return undefined
  }

  /**
     * 获取用户信息档案卡(包含减重,卡路里等信息)
     * @param agent 用户钱包地址
     * @param userObjectId 用户ObjectId
     * @param groupObjectId 群组ObjectId
     * @returns 用户信息档案卡
     */
  getUserInfoCard = async (agent: EthAddress, userObjectId: string, groupObjectId: string): Promise<IUserInfoCard | undefined> => {
    if (!agent || !userObjectId || !groupObjectId) return undefined

    const weightLossCard = await this.getUserWeightLossCard(agent, userObjectId, groupObjectId)
    if (!weightLossCard) return undefined

    const caloriesCard = await getCalorieCard(agent, userObjectId, groupObjectId)
    const sleepCard = await this.getUserSleepRecordCard(agent, userObjectId, groupObjectId)
    const runningCard = await this.getUserRunningUserCard(agent, userObjectId)
    const hGroup = await getStandardizedHealthGroup(groupObjectId)

    return {
      weightLossCard,
      caloriesCard,
      sleepCard,
      runningCard,
      userName: weightLossCard?.userName,
      userObjectId: weightLossCard?.userObjectId,
      groupObjectId: weightLossCard?.groupObjectId || hGroup?.groupOID,
      groupName: weightLossCard?.groupName || hGroup?.groupName,
      wxGroup: hGroup,
      startDate: hGroup?.reVisitDates?.[0],
    }
  }
  /**
   * 获取用户信息档案卡列表(包含减重,卡路里等信息)
     * @param agent 用户钱包地址
     * @returns 用户减重档案卡
     */
  getUserInfoCardList = async (agent: EthAddress, page: number = 1, pageSize: number = 30, search?: any): Promise<{ total: number, list: IUserInfoCard[] }> => {
    if (!agent) return { total: 0, list: [] }
    const query = new Parse.Query(TABLE_USER_CARD_WEIGHT_LOSS)
    query.equalTo("agent", agent)
    query.descending("totalScore")
    //query.descending("createdAt")
    //query.greaterThan("totalScore", 10)
    query.exists("userObjectId")
    query.exists("groupObjectId")

    if (search) {
      if (search.userName) query.matches('userName', new RegExp(search.userName, 'i'))
      if (search.groupName) query.matches('groupName', new RegExp(search.groupName, 'i'))

      if (search.startDate) query.greaterThan('startDate', search.startDate)
      if (search.endDate) query.lessThan('endDate', search.endDate)
      if (search.interventionPlan) query.contains('interventionPlan', search.interventionPlan)
    }
    const total = await query.count()
    query.skip(this.getSkipNumber(total, page, pageSize))
    query.limit(pageSize)
    const records = await query.find()

    const list = await Promise.all(records.map(async record => {
      const weightLossCard = await this.getRecordJson(record)
      // await this.getUserWeightLossCard(agent, weightLossCard.userObjectId, weightLossCard.groupObjectId)
      const caloriesCard = await getCalorieCard(agent, weightLossCard.userObjectId, weightLossCard.groupObjectId)
      const sleepCard = await this.getUserSleepRecordCard(agent, weightLossCard.userObjectId, weightLossCard.groupObjectId)
      const runningCard = await this.getUserRunningUserCard(agent, weightLossCard.userObjectId)
      const hGroup = await getStandardizedHealthGroup(weightLossCard.groupObjectId)
      if (!hGroup) {
        logger.warn(`用户档案卡:${weightLossCard.userObjectId} - ${agent} 未找到对应的标准群组`)
      }
      return {
        weightLossCard,
        caloriesCard,
        sleepCard,
        runningCard,

        userName: weightLossCard?.userName,
        userObjectId: weightLossCard?.userObjectId,
        groupObjectId: weightLossCard?.groupObjectId || hGroup?.groupOID,
        groupName: weightLossCard?.groupName || hGroup?.groupName,
        wxGroup: hGroup,
        startDate: hGroup?.reVisitDates?.[0],
      }
    }))
    return { total, list }
  }

  // 获取用户信息档案卡列表
  servicesGetUserInfoCardList = async ({ sessionId, pageNum = 1, pageSize = 30, search }:
    { sessionId: string, pageNum: number, pageSize: number, search: any }) => {
    const session = await this.checkPermission({ sessionId, roles: 'HEALTH_MANAGER' })
    const { total, list } = await this.getUserInfoCardList(session.agent, pageNum, pageSize, search)
    return this.makeGResponseList(list, pageNum, pageSize, total)
  }

  // 设置用户信息档案卡
  servicesSetUserInfoCard = async ({ sessionId, userObjectId, groupObjectId, body }:
    { sessionId: string, userObjectId: string, groupObjectId: string, body: any }) => {
    const session = await this.checkSession(sessionId)
    const userInfoCard = await this.setUserInfoCard(session.agent, userObjectId, body.groupObjectId, body)
    return this.makeGResponse(userInfoCard)
  }
  // 获取用户减重档案卡 表记录
  getUserWeightLossCardRecord = async (agent: EthAddress, userObjectId: string, groupObjectId: string): Promise<ParseDB.Object> => {
    if (!agent || !userObjectId || !groupObjectId) return undefined
    const query = new Parse.Query(TABLE_USER_CARD_WEIGHT_LOSS)
    query.equalTo("agent", agent)
    query.equalTo("userObjectId", userObjectId)
    query.equalTo("groupObjectId", groupObjectId)
    const record = await query.first()
    return record
  }
  // 获取用户减重档案卡
  getUserWeightLossCard = async (agent: EthAddress, userObjectId: string, groupObjectId: string): Promise<IWeightLossCard> => {
    const record = await this.getUserWeightLossCardRecord(agent, userObjectId, groupObjectId)
    return this.getRecordJson(record)
  }
  // 获取用户睡眠记录档案卡 表记录
  getUserSleepRecordCardRecord = async (agent: EthAddress, userObjectId: string, groupObjectId: string): Promise<ParseDB.Object> => {
    if (!agent || !userObjectId || !groupObjectId) return undefined
    const query = new Parse.Query(TABLE_USER_CARD_SLEEP)
    query.equalTo("agent", agent)
    query.equalTo("userObjectId", userObjectId)
    query.equalTo("groupObjectId", groupObjectId)
    const record = await query.first()
    return record
  }
  // 获取用户睡眠记录档案卡
  getUserSleepRecordCard = async (agent: EthAddress, userObjectId: string, groupObjectId: string): Promise<ISleepRecordCard> => {
    const record = await this.getUserSleepRecordCardRecord(agent, userObjectId, groupObjectId)
    return this.getRecordJson(record)
  }

  // 获取用户跑步记录档案卡
  getUserRunningUserCard = async (agent: EthAddress, userObjectId: string): Promise<IRunningCard> => {
    if (!agent || !userObjectId) return undefined
    const query = new Parse.Query(TABLE_USER_CARD_RUNNING)
    query.equalTo("agent", agent)
    query.equalTo("userObjectId", userObjectId)
    const record = await query.first()
    return record ? record.toJSON() : undefined
  }

  // 创建或保存用户档案信息
  async saveUserSleepCard(agent: EthAddress, userObjectId: string, groupObjectId: string, userRecord: ISleepRecordTable, userRecordMerge: boolean) {
    if (!userRecord) return

    const data: ISleepRecordCard = {
      objectId: "",
      agent,
      userObjectId,
      groupObjectId: userRecord.wxGroupObjectId,
      userName: userRecord.userName,
      firstWeight: userRecord.weight ?? 0,
      firstWeightDate: userRecord.activityDate,
      lastWeight: userRecord.weight ?? 0,
      lastWeightDate: userRecord.activityDate,
      targetWeightHalfMonth: 0.025,
      targetWeightMonth: 0.05,
      targetWeightThisWeek: 20,
      weightLossPeriodMonth: 3,
      totalScore: 0,
      isAdminModify: false
    }
    // 计算用户提供数据得分
    //data.totalScore += calculateScore(userCard, data)
    logger.info(`保存用户资料卡:${userObjectId} - ${agent}`, JSON.stringify(userRecord, null, 2))

    //--------------------------------------------------------------------------
    logger.info(`更新或创建用户减重档案卡:${userObjectId} - ${agent}`, userRecord?.userName)

    // 是否存在档案卡？
    const query = new Parse.Query(TABLE_USER_CARD_SLEEP)
    query.equalTo("agent", agent)
    query.equalTo("userObjectId", userObjectId)

    let createUserCard = false
    let record = await query.first()
    // 创建记录
    if (!record) {
      logger.info(`创建用户档案卡:${userObjectId} - ${agent}`, JSON.stringify(data, null, 2))
      record = new ParseDB.Object(TABLE_USER_CARD_SLEEP)
      createUserCard = true
    }

    const fWeightDate = record.get("firstWeightDate") || new Date()
    if (data.firstWeight && data.firstWeightDate && data.firstWeightDate <= fWeightDate) {
      record.set("firstWeight", data.firstWeight)
      record.set("firstWeightDate", data.firstWeightDate)
      logger.info(`更新用户档案卡首重:${userObjectId} - ${agent}`, JSON.stringify(data, null, 2))
    }

    const lWeightDate = record.get("lastWeightDate") || new Date(0)
    if (data.lastWeight && data.lastWeightDate && data.lastWeightDate >= lWeightDate) {
      record.set("lastWeight", data.lastWeight)
      record.set("lastWeightDate", data.lastWeightDate)
      logger.info(`更新用户档案卡末重:${userObjectId} - ${agent}`, JSON.stringify(data, null, 2))
    }

    record.set("targetWeight", data.targetWeight || 0)

    if (data.targetWeightHalfMonth) record.set("targetWeightHalfMonth", data.targetWeightHalfMonth || 0)
    if (data.targetWeightMonth) record.set("targetWeightMonth", data.targetWeightMonth || 0)
    if (data.targetWeightThisWeek) record.set("targetWeightThisWeek", data.targetWeightThisWeek || 0)

    record.set("weightLossPeriodMonth", data.weightLossPeriodMonth ?? 3)

    if (!userRecordMerge) {
      data.totalScore = record.get("totalScore") + userRecord.score
      record.set("totalScore", data.totalScore)
    }

    if (createUserCard) {
      record.set("agent", agent)
      record.set("userObjectId", userObjectId)
    }

    return this.getRecordJson(await record.save())
  }


}

export default new HealthUserUserCard()
