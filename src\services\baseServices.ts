import ApiError from "../utils/api_error";
import { ParseDB } from "../database/database";
import session, { SessionInfo } from "./sessionManager";

import { GResponse, GResponseList } from "../types/types";
import gateAdminApi from "./gateSSO/gateSSOAdminApi";
import { getSkipNumber } from "./services";
import { WxGroupInfo } from "./wechat/types";
import { IKnowReferer } from '../controllers/controller';


export class BaseServices {
  // 虚变量
  TABLE_NAME: string;
  constructor() {
    // 虚变量
    this.TABLE_NAME = "default";
  }

  // 虚函数
  protected async getRecord(recordId: string): Promise<any> {
    throw new Error("Method not implemented.");
  }



  // 虚函数
  getRecordJson(record: ParseDB.Object): any {
    throw new Error("Method not implemented.");
  }

  checkSession = async (sessionId: string): Promise<SessionInfo> => {
    const sess = await session.validateSession(sessionId);
    if (!sess) throw new ApiError("session is error", 60001);
    return sess;
  };

  /**
   * 检查用户是否为管理员,匹配权限或角色
   * @param param0
   * @returns
   */
  checkPermission = async ({ sessionId, permission, roles }:
    { sessionId: string; permission?: string; roles?: string }): Promise<SessionInfo> => {

    const sess = await session.validateSession(sessionId);
    if (!sess) throw new ApiError("session is error", 60001);
    // 是否管理员
    const adminRoles = ['role_iknow_admin', 'R_SUPER']
    if (adminRoles.some(permission => sess.roles.includes(permission))) return sess;

    if (permission === "chatlist") {
      // 验证gate sso chatlist 读取权限
      const ret = await gateAdminApi.checkPermission(sess.platformUserId, process.env.GATE_SSO_PERMISSION_CHATLIST);
      if (ret) return sess
    }

    if (permission && !sess.permissions?.includes(permission)) { throw new ApiError("权限不足", 60002) }
    if (roles && !sess.roles?.includes(roles)) throw new ApiError("权限不足", 60002);

    return sess;
  };

  isAdminByRoles = async (roles: string[]): Promise<boolean> => {
    const adminRoles = ['role_iknow_admin', 'R_SUPER']
    if (adminRoles.some(permission => roles.includes(permission))) return true;
    return false
  };

  isAdminByObjectId = async ({ userObjectId }): Promise<boolean> => {
    const query = new ParseDB.Query("Users");
    query.equalTo("userObjectId", userObjectId);
    const result = await query.first();
    if (result) return this.isAdminByRoles(result.get("roles"));
    return false;
  };
  /**
   * 是否为管理员或包含其中一个权限,验证通过;不区分 permission,roles
   * @param sessionId
   * @param permissions
   * @returns
   */
  checkPermissions = async (sessionId: string, permissions: string[]): Promise<SessionInfo> => {
    // 验证 session
    const sess = await this.checkSession(sessionId);
    // 是否管理员
    if (this.isAdminByRoles(sess.roles)) return sess;

    if (permissions.some(permission => sess.permissions.includes(permission))) return sess;
    if (permissions.some(permission => sess.roles.includes(permission))) return sess;
    return null;
  };
  public async isSuperMan(sessionId: string): Promise<SessionInfo> {
    const sess = await this.checkSession(sessionId);
    // 是否管理员
    if (sess?.roles?.includes('role_iknow_admin')) return sess
    return null;
  }
  public isAdminOrMySelf = async (sessionId: string, account: string): Promise<SessionInfo> => {
    if (!account) throw new ApiError('无效的参数', 400);

    const sess = await this.checkSession(sessionId);
    if (this.isAdminByRoles(sess?.roles) || sess.account === account) return sess
    return null;
  }
  public isAdminOrMySelfOID = async (sessionId: string, objectId: string): Promise<SessionInfo> => {
    if (!objectId) throw new ApiError('无效的参数', 400);

    const sess = await this.checkSession(sessionId);
    if (this.isAdminByRoles(sess?.roles) || sess.userObjectId === objectId) return sess
    return null;
  }
  public isGroupOwner = async (sessionId: string, groupObjectId: string): Promise<WxGroupInfo> => {
    if (!groupObjectId || !sessionId) throw new ApiError('无效的参数', 400);

    const sess = await this.checkSession(sessionId);
    const refGroups = { __type: 'Pointer', className: 'AIItchatGroups', objectId: groupObjectId }

    const query = new ParseDB.Query("AIItchatGroupsOfUser");
    query.equalTo("agent", sess.agent);
    query.equalTo("refGroups", refGroups);
    query.equalTo("account", sess.account);
    const result = await query.first();
    return result ? result.toJSON() as WxGroupInfo : null;
  };

  public isSuperOrOwnerGroup = async (sessionId: string, groupObjectId: string): Promise<SessionInfo> => {
    if (!groupObjectId || !sessionId) return null
    // 验证 session
    const sess = await this.checkSession(sessionId);
    // 是否管理员
    if (sess?.roles?.includes('role_iknow_admin')) return sess

    const refGroups = { __type: 'Pointer', className: 'AIItchatGroups', objectId: groupObjectId }

    const query = new ParseDB.Query("AIItchatGroupsOfUser");
    query.equalTo("agent", sess.agent);
    query.equalTo("refGroups", refGroups);
    query.equalTo("account", sess.account);
    const result = await query.first();

    if (result) return sess;

    const query2 = new ParseDB.Query("AIItchatGroups");
    query2.equalTo("agent", sess.agent);
    query2.equalTo("objectId", groupObjectId);
    query2.equalTo("owner_Account", sess.account);
    const result2 = await query2.first();
    return result2 ? sess : null;
  }

  // 是否有移动端
  public isMobile = (referer: IKnowReferer) => {
    if (!referer) return false;
    const refererStr = referer.userAgent;
    return refererStr?.includes('mobile') || refererStr?.includes('Android') || refererStr?.includes('iPhone');
  }
  // 是否PC前端
  public isPc = (referer: IKnowReferer) => {
    if (!referer) return false;
    const refererStr = referer.userAgent;
    return refererStr?.includes('pc') || refererStr?.includes('Windows');
  }
  // 是否在微信中
  public isWechat = (referer: IKnowReferer) => {
    if (!referer) return false;
    const refererStr = referer.userAgent;
    return refererStr?.includes('MicroMessenger');
  }




  getSkipNumber = getSkipNumber

  makeGResponse = <T>(data: T, code: number = 200, msg: string = 'ok'): GResponse<T> => ({ code, msg, data })

  makeGResponseError = (code: number = 60000, msg: string = 'error'): GResponse<any> => ({ code, msg, data: null, })

  makeGResponseList = (data: any, current: number, size: number, total: number, code: number = 200, msg: string = 'ok'): GResponseList<any> =>
    ({ code, msg, list: data, current, size, total })

  makeGResponseListError = (code: number = 60000, msg: string = 'error'): GResponseList<any> =>
    ({ code, msg, list: [], current: 0, size: 0, total: 0 })

}

// 导出一个默认实例
export default new BaseServices();
