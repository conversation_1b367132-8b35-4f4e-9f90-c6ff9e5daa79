# Dify API 集成说明

## 概述

本项目已完善了 `GPTServerApiDify` 类，支持 Dify 平台的文本生成和对话两种API模式。

## 环境配置

在使用前，请确保设置以下环境变量：

```bash
# Dify API 密钥
DIFY_API_KEY=your-dify-api-key-here

# Dify 对话API URL
DIFY_CHAT_URL=https://api.dify.ai/v1/chat-messages

# Dify 文本生成API URL  
DIFY_COMPLETION_URL=https://api.dify.ai/v1/completion-messages
```

## API 功能

### 1. 对话模式 (Chat Messages)

支持会话持续性，可以维持对话上下文：

```typescript
// 创建新会话
const result = await gptDify.chatQuery(userid, "你好")

// 继续会话（需要conversation_id）
const result2 = await gptDify.chatQuery(userid, "请继续", conversationId, inputs)
```

### 2. 文本生成模式 (Completion Messages)

适用于单次文本生成任务：

```typescript
const result = await gptDify.completionQuery(userid, prompt, inputs)
```

### 3. 兼容方法

保持与原有代码的兼容性：

```typescript
// 自动选择最佳模式（优先对话模式，失败时尝试文本生成模式）
const result = await gptDify.difyQuery(userid, prompt)
```

## 方法参数

### chatQuery(userid, prompt, conversationId?, inputs?)

- `userid`: 用户ID
- `prompt`: 用户输入文本
- `conversationId`: 可选，会话ID（为空时创建新会话）
- `inputs`: 可选，输入变量对象

### completionQuery(userid, prompt, inputs?)

- `userid`: 用户ID  
- `prompt`: 用户输入文本
- `inputs`: 可选，输入变量对象

### difyQuery(userid, prompt)

- `userid`: 用户ID
- `prompt`: 用户输入文本

## 响应格式

所有方法返回标准的 `IGPTResponse<T>` 格式：

```typescript
{
    model: string,           // 模型名称
    showText: string,        // AI响应文本
    aiTokens: number,        // 总token数
    completionTokens: number, // 完成token数
    promptTokens: number,    // 提示token数
    json?: T                 // 解析后的JSON数据（如果适用）
}
```

## 错误处理

- API配置缺失时会记录错误并返回null
- 网络请求失败时会记录详细错误信息
- 支持自动降级（对话模式失败时尝试文本生成模式）

## 测试

运行测试文件验证API功能：

```bash
node test_dify_api.js
```

## 与其他GPT服务的集成

该类已集成到 `GPTServerApi` 中，可通过以下方式使用：

```typescript
// 在 gptServerApi.ts 中
case 'dify':
    gptResp = await gptDify.difyQuery(userid, prompt);
    break;
```

## 注意事项

1. 确保Dify应用已正确配置并获得有效的API密钥
2. 不同的Dify应用可能有不同的输入变量要求
3. 对话模式需要妥善管理conversation_id以维持会话状态
4. API调用会产生费用，请合理使用缓存机制

## 日志

所有API调用都会记录详细的日志信息，包括：
- 请求参数
- 响应状态
- 错误信息
- Token使用情况

查看日志以便调试和监控API使用情况。
