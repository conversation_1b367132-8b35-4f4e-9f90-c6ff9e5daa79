import { Request, Response } from "express";
import { contractServices as services } from "../services/token/contractServices";
import uServices, { utilsServices } from "../services/utilsServices";
import aiServices from "../services/aiSetting/aiSetting"

import ipfsGatway from "../utils/ipfs";
import { controlFunc } from "./controller";
import systemInfoServices from "../services/systemInfoServices";
import weatherServices from "../services/weatherServices";

const getTokenInfo = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, utilsServices.serviceGetTokenInfo, {
    address: req?.params?.address,
    chain: req?.params?.chain,
  });

const getNftInfo = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceGetNftInfo, {
    address: req?.params?.address,
    isRequireType: true,
  });

const getNftInfoByName = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceGetNftInfoByName, req?.params?.name);

const getNftInfoByCustomId = async (
  req: Request,
  res: Response,
): Promise<void> =>
  controlFunc(req, res, services.serviceGetNftInfoByTokenId, {
    ...req.params,
  });

const getNftList = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceGetNftList);
const getContractType = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, services.serviceContractType, {
    address: req?.params?.address,
    type: req?.params?.type,
    chain: req?.params?.chain,
  });
const getPinataKey = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, utilsServices.getPinataKey);
const revokePinataKey = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, utilsServices.revokePinataKey, { ...req.body });

const getIpfsGateway = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, ipfsGatway.serviceGetIpfsGateway);
const feedBack = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, utilsServices.feedBack, { ...req.body });
// 腾讯云文件存储 cos - 获取临时存取密钥服务
const generateCosKey = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, utilsServices.generateCosKey, req.params.ext);
// eslint-disable-next-line import/prefer-default-export

const getAiSetting = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, aiServices.servicesGetAiSetting, { sessionId: req.headers['session-id'], });

const setAiSetting = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, aiServices.servicesSetAiSetting, { sessionId: req.headers['session-id'], ...req.body });

const getSystemStatistics = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, systemInfoServices.servicesGetStatsHome, { sessionId: req.headers['session-id'], ...req.body });

const getSystemUsersStatistics = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, systemInfoServices.servicesGetStatsByTimeRange,
    { sessionId: req.headers['session-id'], params: req.params });

const getSystemAlerts = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, systemInfoServices.servicesGetAlerts,
    {
      sessionId: req.headers['session-id'],
      pageNum: Number(req.params.pageNum),
      pageSize: Number(req.params.pageSize),
      isReadObjectIds: req.body?.isReadObjectIds as string[],
      allReadTimestamp: req.body?.allReadTimestamp as string,
    });

const getWeather = async (req: Request, res: Response): Promise<void> =>
  controlFunc(req, res, weatherServices.servicesGetWeather,
    { sessionId: req.headers['session-id'], params: req.params });

export {
  getTokenInfo,
  getNftInfo,
  getNftList,
  getNftInfoByName,
  getNftInfoByCustomId,
  getContractType,
  getPinataKey,
  revokePinataKey,
  getIpfsGateway,
  generateCosKey,
  feedBack, getAiSetting, setAiSetting,
  getSystemStatistics,
  getSystemUsersStatistics,
  getSystemAlerts,
  getWeather
};
